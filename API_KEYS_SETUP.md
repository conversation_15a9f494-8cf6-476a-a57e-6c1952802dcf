# API密钥配置指南

## 🔐 安全配置API密钥

本项目使用Google官方推荐的Secrets Gradle Plugin来安全地管理API密钥，确保敏感信息不会被提交到版本控制系统。

## 📋 配置步骤

### 1. 复制示例配置文件
```bash
cp local.properties.example local.properties
```

### 2. 编辑local.properties文件
打开`local.properties`文件，替换以下占位符为您的实际API密钥：

```properties
# 您的Android SDK路径
sdk.dir=YOUR_ANDROID_SDK_PATH

# API密钥配置 - 请替换为您的实际密钥
OPENROUTER_API_KEY=your_actual_openrouter_api_key_here
GEMINI_MODEL=google/gemini-2.5-flash-lite-preview-06-17
```

### 3. 获取OpenRouter API密钥
1. 访问 [OpenRouter官网](https://openrouter.ai/)
2. 注册账户并登录
3. 在Dashboard中生成API密钥
4. 将密钥复制到`local.properties`文件中

## 🛡️ 安全特性

- ✅ **本地存储**: API密钥存储在`local.properties`文件中，不会被提交到Git
- ✅ **构建时注入**: 密钥在构建时安全地注入到`BuildConfig`中
- ✅ **运行时验证**: 应用启动时会检查API密钥是否已正确配置
- ✅ **错误处理**: 提供清晰的错误信息指导用户正确配置

## 📁 文件说明

- `local.properties` - 实际的配置文件（已在.gitignore中）
- `local.properties.example` - 示例配置文件（可以提交到Git）
- `ApiConfig.kt` - API配置工具类，提供安全的密钥访问
- `OpenRouterApiService.kt` - 网络服务类，使用安全配置

## ⚠️ 重要提醒

1. **永远不要**将`local.properties`文件提交到版本控制系统
2. **永远不要**在代码中硬编码API密钥
3. 如果意外提交了API密钥，请立即撤销并重新生成
4. 定期轮换API密钥以提高安全性

## 🔧 故障排除

### 问题：应用提示"API密钥未配置"
**解决方案**：
1. 确认`local.properties`文件存在
2. 检查`OPENROUTER_API_KEY`是否正确设置
3. 重新构建项目

### 问题：网络请求失败
**解决方案**：
1. 验证API密钥是否有效
2. 检查网络连接
3. 确认OpenRouter服务状态

## 📞 支持

如果遇到配置问题，请检查：
1. API密钥格式是否正确
2. `local.properties`文件权限
3. 项目构建日志中的错误信息
