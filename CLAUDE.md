# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Building the Project
```bash
# Build debug APK
./gradlew assembleDebug

# Build release APK
./gradlew assembleRelease

# Clean build
./gradlew clean

# Install debug APK to connected device
./gradlew installDebug
```

### Testing Commands
```bash
# Run unit tests
./gradlew test

# Run instrumented tests
./gradlew connectedAndroidTest

# Run all tests
./gradlew check
```

### Code Quality
```bash
# Run lint checks
./gradlew lint

# Generate lint report
./gradlew lintDebug
```

## API Configuration Setup

**CRITICAL**: Before building, you must configure API keys in `local.properties`. Copy `local.properties.example` to `local.properties` and fill in your actual API keys:

```properties
sdk.dir=YOUR_ANDROID_SDK_PATH
OPENROUTER_API_KEY=your_actual_openrouter_api_key_here
GEMINI_MODEL=google/gemini-2.5-flash-lite-preview-06-17  
SILICONFLOW_API_KEY=your_siliconflow_api_key_here
GEMINI_TTS_API_KEY=your_gemini_tts_api_key_here
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

API keys are securely injected into `BuildConfig` at build time. Never hardcode keys in source code.

## High-Level Architecture

### Core Components

**RAG (Retrieval-Augmented Generation) System**:
- `KnowledgeBaseManager`: Orchestrates document processing, chunking, vectorization
- `RagQueryEngine`: Integrates vector search with AI response generation  
- `VectorSearchService`: Handles both cloud (Supabase) and local vector search
- `SemanticChunker`: Intelligently chunks documents for optimal retrieval

**Document Processing Pipeline**:
1. `DocumentParser`: Extracts text from PDF, DOCX, TXT files
2. `SemanticChunker`: Breaks documents into meaningful chunks
3. `SiliconFlowEmbeddingService`: Generates vector embeddings
4. `SupabaseVectorService`: Stores/retrieves vectors in cloud database

**AI Services**:
- `OpenRouterApiService`: Primary AI chat interface using Gemini models
- `SiliconFlowApiService`: Alternative AI service provider
- `GeminiTtsApiService`: Text-to-speech using Gemini TTS
- `VoiceToTextService`: Speech recognition via SiliconFlow

**Voice Features**:
- `TextToSpeechService`: Converts AI responses to speech with Gemini TTS
- `VoiceToTextService`: Converts speech to text for voice input
- `TtsEngine` interface: Abstraction for different TTS providers (Android native, Gemini)

**Data Layer**:
- Room database with entities: `ChatMessageEntity`, `DocumentEntity`, `ConversationGroupEntity`, `UserEntity`
- Automatic migrations handle schema changes (currently at version 4)
- `SupabaseDocumentService`: Syncs local documents with cloud storage

### Key Architecture Patterns

**Fragment-Based Navigation**: Main app uses fragment-based navigation with `MainActivity` hosting different fragments like `ChatFragment`, `KnowledgeFragment`, `ProfileFragment`.

**MVVM Pattern**: Fragments use repositories and services, with ViewModels implied through fragment lifecycle management.

**Hybrid Search Strategy**: Vector search attempts cloud-first, falls back to local with graceful degradation.

**Secure Configuration**: API keys managed through Gradle Secrets Plugin, preventing accidental commits.

**Async Processing**: Heavy operations use Kotlin coroutines with proper Dispatchers.IO context switching.

### Search Modes

The system supports two search strategies:
- **Threshold-based**: Filters results by similarity threshold, may return no results
- **TOP-K search**: Always returns K most similar results, useful for ensuring responses

## Key Files for Development

### Configuration Files
- `app/build.gradle.kts`: Dependencies, API key injection, KSP configuration
- `local.properties`: API keys (create from `.example` file)
- `gradle/libs.versions.toml`: Version catalog for dependencies

### Core Service Classes
- `service/KnowledgeBaseManager.kt`: Central document processing orchestrator
- `service/RagQueryEngine.kt`: Main RAG query interface
- `service/VectorSearchService.kt`: Vector search with fallback strategies
- `utils/PromptBuilder.kt`: Constructs prompts for different scenarios
- `utils/ApiConfig.kt`: Secure API key access utilities

### Database Layer
- `database/AppDatabase.kt`: Room database with migration support
- Database migrations handle RAG features added in version 4

### Voice Integration
- TTS system supports multiple engines (Android native, Gemini)
- Speech recognition integrated into chat interface
- All network timeouts set to 30 seconds for voice processing

## Development Notes

**Dependency Management**: Project uses KSP (not KAPT) for Room compilation. Uses Supabase Kotlin client for cloud sync.

**Error Handling**: Services use `Result<T>` pattern for consistent error handling throughout the app.

**Logging**: Extensive logging with consistent TAG patterns for debugging (`"ServiceName"` format).

**Resource Management**: Automatic cleanup of temporary files, MediaPlayer resources, and database connections.

**Theme Support**: Custom theme system with `ThemeManager` supporting multiple color schemes including gray theme options.

**Permissions**: App handles audio recording, file access, and network permissions with proper user prompts.

When working with this codebase, pay attention to the RAG pipeline flow, secure API key handling, and the hybrid cloud/local architecture for robust offline support.