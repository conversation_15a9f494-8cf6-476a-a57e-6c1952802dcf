# 增强RAG系统使用指南

## 概述

本文档介绍了增强版RAG系统的新功能和使用方法。增强版系统提供了更智能的上下文工程、自适应提示词生成和查询理解能力。

## 新增功能

### 1. 智能上下文管理 (ContextManager)

#### 功能特点：
- **多样性选择**：避免内容重复，提高上下文覆盖面
- **动态窗口管理**：根据内容重要性动态分配上下文空间
- **质量评估**：评估上下文的相关性、多样性和覆盖度
- **智能重排序**：根据查询相关性重新排序文档

#### 使用示例：
```kotlin
val contextManager = ContextManager()

// 选择最优上下文
val optimizedResults = contextManager.selectOptimalContext(
    searchResults = searchResults,
    query = "什么是机器学习？",
    maxResults = 5
)

// 评估上下文质量
val qualityMetrics = contextManager.assessContextQuality(
    results = optimizedResults,
    query = query
)

println("上下文质量分数: ${qualityMetrics.qualityScore}")
```

### 2. 增强提示词构建器 (EnhancedPromptBuilder)

#### 功能特点：
- **自适应提示词**：根据查询类型自动选择最佳提示词策略
- **多文档排序**：按相似度和重要性排序文档
- **元数据标注**：包含相似度、来源等信息
- **多种查询类型支持**：事实性、分析性、比较性、程序性查询

#### 使用示例：
```kotlin
// 自适应提示词生成
val prompt = EnhancedPromptBuilder.buildAdaptivePrompt(
    searchResults = searchResults,
    query = "比较深度学习和机器学习的区别",
    queryType = QueryType.COMPARATIVE
)

// 低相似度特殊处理
val lowSimPrompt = EnhancedPromptBuilder.buildLowSimilarityPrompt(
    searchResults = searchResults,
    query = query,
    maxSimilarity = 0.4
)
```

### 3. 查询分析器 (QueryAnalyzer)

#### 功能特点：
- **意图识别**：自动识别查询意图（定义、解释、比较等）
- **复杂度评估**：评估查询的复杂程度
- **关键词提取**：智能提取查询关键词
- **查询重构**：生成查询改进建议

#### 使用示例：
```kotlin
val queryAnalyzer = QueryAnalyzer()

// 分析查询
val analysis = queryAnalyzer.analyzeQuery("深度学习和机器学习有什么区别？")

println("查询意图: ${analysis.intent}")
println("复杂度: ${analysis.complexity}")
println("关键词: ${analysis.keywords}")
println("建议重构: ${analysis.suggestedReformulations}")

// 查询扩展
val expandedQueries = queryAnalyzer.expandQuery("机器学习算法")
println("扩展查询: $expandedQueries")
```

### 4. 增强RAG引擎

#### 新增方法：

##### queryEnhanced()
使用所有增强功能的主要查询方法：
```kotlin
val ragEngine = RagQueryEngine(context, vectorService, apiService)

val result = ragEngine.queryEnhanced(
    RagQueryRequest(query = "什么是深度学习？")
)

result.onSuccess { (ragResponse, aiAnswer) ->
    println("找到 ${ragResponse.results.size} 个相关文档")
    println("AI回答: $aiAnswer")
}
```

##### queryWithMultipleStrategies()
尝试多种查询策略：
```kotlin
val strategies = ragEngine.queryWithMultipleStrategies("机器学习")

strategies.onSuccess { results ->
    results.forEach { (strategy, answer) ->
        println("$strategy: $answer")
    }
}
```

##### getEnhancedQuerySuggestions()
获取智能查询建议：
```kotlin
val suggestions = ragEngine.getEnhancedQuerySuggestions("深度学习")

suggestions.onSuccess { suggestionList ->
    suggestionList.forEach { suggestion ->
        println("建议: $suggestion")
    }
}
```

## 配置管理

### RagConfig 配置选项

```kotlin
// 设置性能模式
RagConfig.setPerformanceMode(PerformanceMode.QUALITY)

// 启用调试模式
RagConfig.setDebugMode(true)

// 自定义配置
RagConfig.ENABLE_ADAPTIVE_PROMPTS = true
RagConfig.ENABLE_DIVERSITY_SELECTION = true
RagConfig.MAX_CONTEXT_LENGTH = 8000

// 获取配置摘要
val config = RagConfig.getConfigSummary()
println("当前配置: $config")
```

### 性能模式说明

1. **FAST模式**：优先速度，禁用复杂功能
2. **BALANCED模式**：平衡速度和质量
3. **QUALITY模式**：优先质量，启用所有增强功能

## 最佳实践

### 1. 查询优化建议

```kotlin
// 好的查询示例
"深度学习和传统机器学习算法的主要区别是什么？"
"如何选择合适的神经网络架构？"
"Python中实现卷积神经网络的具体步骤"

// 避免的查询示例
"学习"  // 太简单
"请详细解释所有机器学习算法的优缺点、适用场景、数学原理..." // 太复杂
```

### 2. 上下文优化

```kotlin
// 使用上下文管理器优化结果
val contextManager = ContextManager()

val optimizedResults = contextManager.selectOptimalContext(
    searchResults = rawResults,
    query = query,
    maxResults = 5
)

// 评估质量并调整
val quality = contextManager.assessContextQuality(optimizedResults, query)
if (quality.qualityScore < 0.5) {
    // 尝试扩展查询或调整参数
    val expandedQuery = queryAnalyzer.expandQuery(query)
    // 重新搜索...
}
```

### 3. 提示词策略选择

```kotlin
// 根据查询类型选择策略
val queryType = when {
    query.contains("是什么") -> QueryType.FACTUAL
    query.contains("如何") -> QueryType.PROCEDURAL
    query.contains("比较") -> QueryType.COMPARATIVE
    query.contains("分析") -> QueryType.ANALYTICAL
    else -> QueryType.GENERAL
}

val prompt = EnhancedPromptBuilder.buildAdaptivePrompt(
    searchResults, query, queryType
)
```

## 性能监控

### 质量指标

```kotlin
val qualityMetrics = contextManager.assessContextQuality(results, query)

println("平均相似度: ${qualityMetrics.averageSimilarity}")
println("多样性分数: ${qualityMetrics.diversityScore}")
println("覆盖度分数: ${qualityMetrics.coverageScore}")
println("综合质量: ${qualityMetrics.qualityScore}")
```

### 性能统计

```kotlin
val stats = ragEngine.getEngineStatistics()
stats.onSuccess { statistics ->
    println("RAG引擎版本: ${statistics["ragEngineVersion"]}")
    println("增强功能状态: ${statistics["enhancedFeaturesEnabled"]}")
    println("最大上下文长度: ${statistics["maxContextLength"]}")
}
```

## 故障排除

### 常见问题

1. **上下文质量低**
   - 检查相似度阈值设置
   - 尝试查询扩展或重构
   - 增加检索结果数量

2. **响应速度慢**
   - 切换到FAST性能模式
   - 减少最大上下文长度
   - 禁用部分增强功能

3. **回答不准确**
   - 启用查询分析
   - 使用自适应提示词
   - 检查文档质量和相关性

### 调试技巧

```kotlin
// 启用详细日志
RagConfig.setDebugMode(true)

// 查看查询分析结果
val analysis = queryAnalyzer.analyzeQuery(query)
Log.d("RAG", "查询分析: $analysis")

// 检查上下文质量
val quality = contextManager.assessContextQuality(results, query)
Log.d("RAG", "上下文质量: $quality")
```

## 升级指南

### 从原版本升级

1. **添加新依赖**：确保导入新的工具类
2. **更新调用方式**：使用 `queryEnhanced()` 替代原方法
3. **配置调整**：根据需要调整 `RagConfig` 设置
4. **测试验证**：验证新功能是否正常工作

### 兼容性说明

- 原有的 `queryWithAiResponse()` 方法仍然可用
- 新功能是可选的，不会影响现有功能
- 可以逐步迁移到增强版功能

## 总结

增强版RAG系统提供了更智能、更灵活的文档检索和回答生成能力。通过合理配置和使用这些新功能，可以显著提升系统的准确性和用户体验。

建议根据具体应用场景选择合适的性能模式和功能组合，并通过监控质量指标来持续优化系统表现。