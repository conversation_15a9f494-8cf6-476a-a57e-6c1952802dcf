# RAG Android项目架构分析与Supabase数据库设计

## 项目概述

这是一个基于Android平台的RAG（检索增强生成）智能对话应用，集成了文档知识库管理、向量搜索、语义检索和AI对话功能。

## 项目架构分析

### 1. 整体架构模式
- **架构模式**: MVVM (Model-View-ViewModel)
- **数据存储**: 双层存储架构（本地SQLite + 云端Supabase）
- **UI框架**: Fragment-based导航 + Material Design
- **异步处理**: Kotlin Coroutines + LiveData

### 2. 核心模块分析

#### 2.1 数据层 (Database/Repository)
```
database/
├── AppDatabase.kt              # Room数据库主类
├── entity/                     # 数据实体
│   ├── ChatMessageEntity.kt    # 聊天消息实体
│   ├── ConversationGroupEntity.kt # 对话分组实体
│   ├── DocumentEntity.kt       # 文档实体
│   └── UserEntity.kt          # 用户实体
├── dao/                       # 数据访问对象
│   ├── ChatMessageDao.kt      # 聊天消息DAO
│   ├── ConversationGroupDao.kt # 对话分组DAO
│   ├── DocumentDao.kt         # 文档DAO
│   └── UserDao.kt            # 用户DAO
└── converter/                 # 类型转换器
    └── StringListConverter.kt # 字符串列表转换器
```

**特点**:
- 使用Room ORM进行本地数据持久化
- 支持复杂数据类型的序列化存储
- 提供完整的CRUD操作接口

#### 2.2 业务逻辑层 (Service)
```
service/
├── OpenRouterApiService.kt     # OpenRouter API服务
├── GeminiTtsApiService.kt      # Gemini TTS服务
├── SiliconFlowApiService.kt    # SiliconFlow API服务
├── SiliconFlowEmbeddingService.kt # 向量嵌入服务
├── RagQueryEngine.kt           # RAG查询引擎
├── VectorSearchService.kt      # 向量搜索服务
├── KnowledgeBaseManager.kt     # 知识库管理器
├── DocumentParser.kt           # 文档解析器
├── SemanticChunker.kt          # 语义分块器
├── SupabaseConfig.kt           # Supabase配置
├── SupabaseDocumentService.kt  # Supabase文档同步服务
├── SupabaseVectorService.kt    # Supabase向量服务
├── TextToSpeechService.kt      # 文本转语音服务
├── VoiceToTextService.kt       # 语音转文本服务
└── tts/                        # TTS引擎
    ├── TtsEngine.kt            # TTS引擎接口
    ├── AndroidTtsEngine.kt     # Android原生TTS
    └── GeminiTtsEngine.kt      # Gemini TTS引擎
```

**核心功能**:
- **RAG查询引擎**: 实现检索增强生成逻辑
- **向量搜索**: 基于语义相似度的文档检索
- **多模态AI服务**: 集成多个AI服务提供商
- **文档处理**: 支持多种格式文档的解析和向量化
- **云端同步**: 本地与Supabase的数据同步

#### 2.3 表现层 (UI)
```
fragment/
├── ChatFragment.kt                    # 主聊天界面
├── ConversationListFragment.kt        # 对话列表
├── ConversationGroupsFragment.kt      # 对话分组管理
├── KnowledgeFragment.kt               # 知识库管理
├── ProfileFragment.kt                 # 用户资料
├── SettingsFragment.kt                # 设置主页面
├── SystemSettingsFragment.kt          # 系统设置
├── ThemeSettingsFragment.kt           # 主题设置
├── ThemeColorSettingsFragment.kt      # 主题颜色设置
└── VoiceEngineSettingsFragment.kt     # 语音引擎设置

adapter/
├── ChatAdapter.kt                     # 聊天消息适配器
├── ConversationAdapter.kt             # 对话适配器
├── ConversationGroupAdapter.kt        # 对话分组适配器
├── DocumentAdapter.kt                 # 文档列表适配器
├── MainPagerAdapter.kt                # 主页面适配器
├── NavConversationAdapter.kt          # 导航对话适配器
└── NavGroupAdapter.kt                 # 导航分组适配器
```

**UI特点**:
- Fragment-based单Activity架构
- Material Design设计语言
- 支持主题切换和个性化设置
- 响应式布局适配不同屏幕尺寸

#### 2.4 数据模型层 (Model)
```
model/
├── ApiModels.kt           # API响应模型
├── ChatMessage.kt         # 聊天消息模型
├── Conversation.kt        # 对话模型
├── ConversationGroup.kt   # 对话分组模型
├── EmbeddingModels.kt     # 嵌入向量模型
├── SupabaseModels.kt      # Supabase数据模型
├── TtsModels.kt           # TTS模型
└── VoiceToTextResponse.kt # 语音转文本响应模型
```

#### 2.5 工具类层 (Utils)
```
utils/
├── ApiConfig.kt           # API配置管理
├── DataManager.kt         # 数据管理器
├── KeyboardUtils.kt       # 键盘工具类
├── PromptBuilder.kt       # 提示词构建器
├── ThemeManager.kt        # 主题管理器
├── TtsConfig.kt           # TTS配置
└── TtsEngineType.kt       # TTS引擎类型
```

### 3. 技术栈分析

#### 3.1 核心技术
- **开发语言**: Kotlin
- **UI框架**: Android Jetpack (Fragment, Navigation, LiveData, ViewModel)
- **本地数据库**: Room (SQLite)
- **云端数据库**: Supabase (PostgreSQL + pgvector)
- **网络请求**: Retrofit + OkHttp
- **异步处理**: Kotlin Coroutines
- **依赖注入**: 手动依赖注入

#### 3.2 AI服务集成
- **OpenRouter**: 多模型AI对话服务
- **Gemini**: Google的AI模型和TTS服务
- **SiliconFlow**: 向量嵌入服务
- **向量搜索**: pgvector扩展

#### 3.3 文档处理能力
- **支持格式**: PDF, DOCX, TXT, CSV, MD
- **文本提取**: 多格式文档解析
- **语义分块**: 智能文档分割
- **向量化**: 文本嵌入向量生成

## Supabase数据库设计

### 1. 数据库架构概览

我已经为您的项目创建了完整的Supabase数据库架构，包含以下核心表：

#### 1.1 用户管理
- **users**: 用户基本信息
- **conversation_groups**: 对话分组
- **conversations**: 对话记录
- **chat_messages**: 聊天消息

#### 1.2 知识库管理
- **documents**: 文档元数据
- **document_chunks**: 文档向量块
- **knowledge_collections**: 知识库集合
- **collection_documents**: 集合文档关联

#### 1.3 系统功能
- **search_history**: 搜索历史
- **system_configs**: 系统配置

### 2. 详细表结构

#### 2.1 用户表 (users)
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    nickname VARCHAR(100) NOT NULL DEFAULT '用户',
    email VARCHAR(255) UNIQUE,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    preferences JSONB DEFAULT '{}'
);
```

#### 2.2 文档表 (documents)
```sql
CREATE TABLE documents (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    file_name VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64),
    upload_time BIGINT NOT NULL,
    is_vectorized BOOLEAN DEFAULT false,
    chunk_count INTEGER DEFAULT 0,
    processing_status VARCHAR(50) DEFAULT 'pending',
    error_message TEXT DEFAULT '',
    extracted_text TEXT DEFAULT '',
    last_processed_time BIGINT DEFAULT 0,
    summary TEXT DEFAULT '',
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT false
);
```

#### 2.3 文档向量块表 (document_chunks)
```sql
CREATE TABLE document_chunks (
    id BIGSERIAL PRIMARY KEY,
    document_id BIGINT REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    embedding vector(1536), -- 使用pgvector存储1536维向量
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(document_id, chunk_index)
);
```

### 3. 核心功能实现

#### 3.1 向量搜索函数
```sql
CREATE OR REPLACE FUNCTION search_documents(
    query_embedding vector(1536),
    similarity_threshold float DEFAULT 0.3,
    match_count int DEFAULT 5,
    filter_document_ids bigint[] DEFAULT NULL
)
RETURNS TABLE (
    chunk_id bigint,
    document_id bigint,
    content text,
    similarity float,
    metadata jsonb,
    document_name varchar(500)
)
```

#### 3.2 性能优化
- **HNSW索引**: 为向量搜索创建高效索引
- **复合索引**: 为常用查询字段创建索引
- **分区策略**: 支持大规模数据存储

#### 3.3 安全策略
- **行级安全策略 (RLS)**: 确保用户只能访问自己的数据
- **数据完整性**: 外键约束保证数据一致性
- **软删除**: 重要数据支持软删除机制

### 4. 系统配置

已预置的系统配置包括：
- 应用版本管理
- 文件上传限制
- 支持的文件类型
- 嵌入模型配置
- 搜索参数设置

## 项目优势与特点

### 1. 技术优势
- **双层存储**: 本地缓存 + 云端同步，保证离线可用性
- **向量搜索**: 基于语义相似度的智能检索
- **多模态AI**: 集成多个AI服务提供商，提高可用性
- **模块化设计**: 清晰的分层架构，易于维护和扩展

### 2. 功能特点
- **智能对话**: RAG增强的AI对话体验
- **知识库管理**: 支持多种文档格式的知识库构建
- **个性化设置**: 丰富的主题和功能配置选项
- **语音交互**: 支持语音输入和TTS输出

### 3. 用户体验
- **响应式设计**: 适配不同屏幕尺寸
- **离线支持**: 核心功能支持离线使用
- **数据同步**: 无缝的云端数据同步
- **个性化**: 支持主题切换和个人偏好设置

## 部署建议

### 1. 环境配置
确保在 `local.properties` 中配置所有必要的API密钥：
- OPENROUTER_API_KEY
- GEMINI_TTS_API_KEY
- SILICONFLOW_API_KEY
- SUPABASE_URL
- SUPABASE_ANON_KEY

### 2. 数据库初始化
Supabase数据库已完成初始化，包括：
- ✅ pgvector扩展已启用
- ✅ 所有表结构已创建
- ✅ 索引和触发器已配置
- ✅ 行级安全策略已设置
- ✅ 初始配置数据已插入

### 3. 功能测试建议
1. 测试文档上传和向量化功能
2. 验证RAG查询的准确性
3. 测试本地与云端的数据同步
4. 验证多用户数据隔离
5. 测试向量搜索性能

## 总结

这个RAG Android项目展现了现代移动应用开发的最佳实践，结合了AI技术、向量搜索、云端同步等先进功能。通过完善的Supabase数据库设计，为应用提供了强大的后端支持，能够满足智能对话和知识库管理的各种需求。

项目架构清晰、功能完整、扩展性强，是一个优秀的RAG应用实现案例。