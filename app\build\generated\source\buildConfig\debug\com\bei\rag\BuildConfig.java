/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.bei.rag;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String APPLICATION_ID = "com.bei.rag";
  public static final String BUILD_TYPE = "debug";
  public static final int VERSION_CODE = 1;
  public static final String VERSION_NAME = "1.0";
  public static final String GEMINI_MODEL = "openrouter/horizon-beta";
  public static final String GEMINI_TTS_API_KEY = "AIzaSyADWZhsrutRDj1xpHoBTjhFFRSbQ0Ze2Ls";
  public static final String OPENROUTER_API_KEY = "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";
  public static final String SILICONFLOW_API_KEY = "sk-zokxenyexetkzgkbufqedzxxztnhkmecmudtswwdgxzcisze";
  public static final String SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtwcmdyeWxvdmFoaGF6anVhZWxvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxNjg1MzcsImV4cCI6MjA2ODc0NDUzN30.rZ60bF_BT8EpM3jk6cuWD0CO9KPv7T3xqtwn_RGU7UM";
  public static final String SUPABASE_URL = "https://kprgrylovahhazjuaelo.supabase.co";
}
