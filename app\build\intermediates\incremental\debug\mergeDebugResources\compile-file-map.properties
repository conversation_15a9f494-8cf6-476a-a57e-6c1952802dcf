#Sun Aug 03 15:18:50 CST 2025
com.bei.rag.app-main-6\:/drawable/bg_message_ai.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_message_ai.xml.flat
com.bei.rag.app-main-6\:/drawable/bg_message_user.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_message_user.xml.flat
com.bei.rag.app-main-6\:/drawable/bg_operation_button.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_operation_button.xml.flat
com.bei.rag.app-main-6\:/drawable/bg_operation_buttons.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_operation_buttons.xml.flat
com.bei.rag.app-main-6\:/drawable/bg_send_button.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_send_button.xml.flat
com.bei.rag.app-main-6\:/drawable/bg_source_pill.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_source_pill.xml.flat
com.bei.rag.app-main-6\:/drawable/bg_voice_button.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_voice_button.xml.flat
com.bei.rag.app-main-6\:/drawable/circle_background.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background.xml.flat
com.bei.rag.app-main-6\:/drawable/color_circle.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_color_circle.xml.flat
com.bei.rag.app-main-6\:/drawable/edit_text_background.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_edit_text_background.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_add.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_arrow_back.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_back.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_arrow_right.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_right.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_bug_report.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_bug_report.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_chat.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chat.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_check.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_copy.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_copy.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_delete.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_edit.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_file_csv.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file_csv.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_file_doc.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file_doc.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_file_pdf.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file_pdf.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_file_txt.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file_txt.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_file_unknown.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file_unknown.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_folder.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_folder.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_info.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_info.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_knowledge.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_knowledge.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_launcher_background.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_launcher_foreground.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_logout.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_logout.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_menu.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_mic.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_mic.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_more_horiz.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_more_horiz.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_more_vert.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_more_vert.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_notifications.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notifications.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_profile.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_profile.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_refresh.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_refresh.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_search.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_select_all.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_select_all.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_send.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_send.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_settings.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_share.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_share.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_upload.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_upload.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_volume_off.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_volume_off.xml.flat
com.bei.rag.app-main-6\:/drawable/ic_volume_up.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_volume_up.xml.flat
com.bei.rag.app-main-6\:/drawable/ios_badge_background.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ios_badge_background.xml.flat
com.bei.rag.app-main-6\:/drawable/ios_button_primary.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ios_button_primary.xml.flat
com.bei.rag.app-main-6\:/drawable/ios_button_secondary.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ios_button_secondary.xml.flat
com.bei.rag.app-main-6\:/drawable/ios_card_background.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ios_card_background.xml.flat
com.bei.rag.app-main-6\:/drawable/ios_input_background.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ios_input_background.xml.flat
com.bei.rag.app-main-6\:/drawable/ios_upload_area.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ios_upload_area.xml.flat
com.bei.rag.app-main-6\:/drawable/rag_icon.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rag_icon.png.flat
com.bei.rag.app-main-6\:/drawable/upload_area_background.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_upload_area_background.xml.flat
com.bei.rag.app-main-6\:/layout/activity_chat.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_chat.xml.flat
com.bei.rag.app-main-6\:/layout/activity_main.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.bei.rag.app-main-6\:/layout/activity_status_bar_test.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_status_bar_test.xml.flat
com.bei.rag.app-main-6\:/layout/dialog_create_group.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_create_group.xml.flat
com.bei.rag.app-main-6\:/layout/dialog_edit_profile.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_profile.xml.flat
com.bei.rag.app-main-6\:/layout/dialog_knowledge_management.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_knowledge_management.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_chat.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_chat.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_conversation_groups.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_conversation_groups.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_conversation_list.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_conversation_list.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_knowledge.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_knowledge.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_profile.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_profile.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_settings.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_settings.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_system_settings.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_system_settings.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_theme_color_settings.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_theme_color_settings.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_theme_settings.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_theme_settings.xml.flat
com.bei.rag.app-main-6\:/layout/fragment_voice_engine_settings.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_voice_engine_settings.xml.flat
com.bei.rag.app-main-6\:/layout/item_conversation.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_conversation.xml.flat
com.bei.rag.app-main-6\:/layout/item_conversation_group.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_conversation_group.xml.flat
com.bei.rag.app-main-6\:/layout/item_document.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_document.xml.flat
com.bei.rag.app-main-6\:/layout/item_message_ai.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_message_ai.xml.flat
com.bei.rag.app-main-6\:/layout/item_message_user.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_message_user.xml.flat
com.bei.rag.app-main-6\:/layout/item_nav_conversation.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_nav_conversation.xml.flat
com.bei.rag.app-main-6\:/layout/item_nav_group.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_nav_group.xml.flat
com.bei.rag.app-main-6\:/layout/navigation_drawer.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_navigation_drawer.xml.flat
com.bei.rag.app-main-6\:/menu/ai_message_options_menu.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_ai_message_options_menu.xml.flat
com.bei.rag.app-main-6\:/menu/debug_menu.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_debug_menu.xml.flat
com.bei.rag.app-main-6\:/menu/group_options_menu.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_group_options_menu.xml.flat
com.bei.rag.app-main-6\:/menu/message_context_menu.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_message_context_menu.xml.flat
com.bei.rag.app-main-6\:/mipmap-hdpi/ic_launcher.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.bei.rag.app-main-6\:/mipmap-hdpi/ic_launcher_round.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.png.flat
com.bei.rag.app-main-6\:/mipmap-mdpi/ic_launcher.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.bei.rag.app-main-6\:/mipmap-mdpi/ic_launcher_round.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.png.flat
com.bei.rag.app-main-6\:/mipmap-xhdpi/ic_launcher.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.bei.rag.app-main-6\:/mipmap-xhdpi/ic_launcher_round.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.png.flat
com.bei.rag.app-main-6\:/mipmap-xxhdpi/ic_launcher.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.bei.rag.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.png.flat
com.bei.rag.app-main-6\:/mipmap-xxxhdpi/ic_launcher.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.bei.rag.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.png=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.png.flat
com.bei.rag.app-main-6\:/xml/backup_rules.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.bei.rag.app-main-6\:/xml/data_extraction_rules.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.bei.rag.app-main-6\:/xml/network_security_config.xml=D\:\\CodeBase\\AndroidCode\\RAG-Andorid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
