<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <attr format="color" name="colorAccent"/>
    <attr format="color" name="colorAppBackground"/>
    <attr format="color" name="colorBadgeBackground"/>
    <attr format="color" name="colorBorder"/>
    <attr format="color" name="colorCardBackground"/>
    <attr format="color" name="colorFileIconDoc"/>
    <attr format="color" name="colorHeader"/>
    <attr format="color" name="colorHeaderDark"/>
    <attr format="color" name="colorProfileHeader"/>
    <attr format="color" name="colorSendButton"/>
    <attr format="color" name="colorSendButtonPressed"/>
    <attr format="color" name="colorTabSelected"/>
    <attr format="color" name="colorTextPrimary"/>
    <attr format="color" name="colorTextSecondary"/>
    <attr format="color" name="colorThemePrimary"/>
    <attr format="color" name="colorThemePrimaryDark"/>
    <attr format="color" name="colorThemePrimaryLight"/>
    <attr format="color" name="colorUserMessageBg"/>
    <color name="ai_message_bg">@color/ios_card_background</color>
    <color name="background_elevated">#FFFFFF</color>
    <color name="background_primary">#F8F9FA</color>
    <color name="background_secondary">#F1F3F4</color>
    <color name="black">#FF000000</color>
    <color name="border_gray">@color/ios_border</color>
    <color name="chat_background">@color/ios_background</color>
    <color name="divider_color">@color/ios_separator</color>
    <color name="document_item_bg">@color/ios_card_background</color>
    <color name="file_icon_csv">@color/ios_green</color>
    <color name="file_icon_doc">@color/ios_blue</color>
    <color name="file_icon_pdf">@color/ios_red</color>
    <color name="file_icon_txt">@color/ios_purple</color>
    <color name="gray_background">#FAFBFC</color>
    <color name="gray_border">#E9ECEF</color>
    <color name="gray_light">#F8F9FA</color>
    <color name="gray_pressed">#E9ECEF</color>
    <color name="gray_text">#6C757D</color>
    <color name="gray_text_light">#ADB5BD</color>
    <color name="header_blue">@color/ios_blue</color>
    <color name="header_blue_dark">@color/ios_blue_dark</color>
    <color name="ios_background">#F2F2F7</color>
    <color name="ios_background_secondary">#FFFFFF</color>
    <color name="ios_background_tertiary">#F8F8F8</color>
    <color name="ios_background_unified">#F2F2F7</color>
    <color name="ios_blue">#007AFF</color>
    <color name="ios_blue_dark">#0056CC</color>
    <color name="ios_blue_light">#E3F2FD</color>
    <color name="ios_border">#E5E5EA</color>
    <color name="ios_card_background">#F2F2F7</color>
    <color name="ios_green">#34C759</color>
    <color name="ios_orange">#FF9500</color>
    <color name="ios_purple">#AF52DE</color>
    <color name="ios_red">#FF3B30</color>
    <color name="ios_separator">#C6C6C8</color>
    <color name="ios_text_primary">#1C1C1E</color>
    <color name="ios_text_secondary">#8E8E93</color>
    <color name="ios_text_tertiary">#C7C7CC</color>
    <color name="ios_text_white">#FFFFFF</color>
    <color name="ios_yellow">#FFCC00</color>
    <color name="logout_button">@color/ios_red</color>
    <color name="profile_header_bg">@color/ios_blue</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="setting_item_bg">@color/ios_card_background</color>
    <color name="shadow_dark">#24000000</color>
    <color name="shadow_light">#08000000</color>
    <color name="shadow_medium">#16000000</color>
    <color name="source_pill_bg">@color/ios_background_tertiary</color>
    <color name="status_bar_dark_blue">#1565C0</color>
    <color name="status_bar_dark_gray">#424242</color>
    <color name="status_bar_dark_green">#2E7D32</color>
    <color name="status_bar_dark_orange">#EF6C00</color>
    <color name="status_bar_dark_pink">#C2185B</color>
    <color name="status_bar_dark_purple">#7B1FA2</color>
    <color name="status_bar_dark_red">#C62828</color>
    <color name="status_bar_light_blue">#E3F2FD</color>
    <color name="status_bar_light_gray">#F5F5F5</color>
    <color name="status_bar_light_green">#E8F5E8</color>
    <color name="status_bar_light_orange">#FFF3E0</color>
    <color name="status_bar_light_pink">#FCE4EC</color>
    <color name="status_bar_light_purple">#F3E5F5</color>
    <color name="status_bar_light_red">#FFEBEE</color>
    <color name="status_bar_overlay_dark">#1AFFFFFF</color>
    <color name="status_bar_overlay_light">#1A000000</color>
    <color name="status_bar_text_dark">#E6E1E5</color>
    <color name="status_bar_text_light">#1C1B1F</color>
    <color name="tab_selected">@color/ios_blue</color>
    <color name="tab_unselected">@color/ios_text_secondary</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_dark">@color/ios_text_primary</color>
    <color name="text_gray">@color/ios_text_secondary</color>
    <color name="text_primary_strong">#1A1A1A</color>
    <color name="text_secondary_soft">#6C757D</color>
    <color name="text_tertiary_light">#ADB5BD</color>
    <color name="theme_brown">#A2845E</color>
    <color name="theme_gray">#8E8E93</color>
    <color name="theme_gray_dark">#6D6D70</color>
    <color name="theme_gray_light">#F2F2F7</color>
    <color name="theme_green">#34C759</color>
    <color name="theme_green_dark">#28A745</color>
    <color name="theme_green_light">#E8F5E8</color>
    <color name="theme_indigo">#5856D6</color>
    <color name="theme_orange">#FF9500</color>
    <color name="theme_orange_dark">#E67E22</color>
    <color name="theme_orange_light">#FFF3E0</color>
    <color name="theme_pink">#FF2D92</color>
    <color name="theme_pink_dark">#E91E63</color>
    <color name="theme_pink_light">#FFE8F5</color>
    <color name="theme_purple">#AF52DE</color>
    <color name="theme_purple_dark">#8E44AD</color>
    <color name="theme_purple_light">#F3E8FF</color>
    <color name="theme_red">#FF3B30</color>
    <color name="theme_red_dark">#DC3545</color>
    <color name="theme_red_light">#FFE8E8</color>
    <color name="theme_teal">#5AC8FA</color>
    <color name="upload_area_bg">@color/ios_background_tertiary</color>
    <color name="upload_border">@color/ios_border</color>
    <color name="user_message_bg">@color/ios_blue_light</color>
    <color name="voice_button_background">@color/ios_background_secondary</color>
    <color name="voice_button_border">@color/ios_border</color>
    <color name="voice_processing_background">#FF9800</color>
    <color name="voice_recording_background">#4CAF50</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="status_bar_height">25dp</dimen>
    <dimen name="text_margin">16dp</dimen>
    <string name="android_tts_engine">系统语音引擎</string>
    <string name="app_name">RAG Android</string>
    <string name="auto_play_ai_reply">AI回复自动播放</string>
    <string name="auto_play_ai_reply_desc">开启后AI回复时将自动播放语音</string>
    <string name="auto_play_disabled">AI回复自动播放已关闭</string>
    <string name="auto_play_enabled">AI回复自动播放已开启</string>
    <string name="back">返回</string>
    <string name="cancel">取消</string>
    <string name="chinese_simplified">中文（简体）</string>
    <string name="chinese_traditional">中文（繁体）</string>
    <string name="confirm">确认</string>
    <string name="current_engine">当前引擎</string>
    <string name="engine_description">引擎说明</string>
    <string name="engine_switched">已切换到%s</string>
    <string name="english_uk">英语（英国）</string>
    <string name="english_us">英语（美国）</string>
    <string name="gemini_tts_engine">Gemini语音引擎</string>
    <string name="language">语言</string>
    <string name="language_set">语言已设置为%s</string>
    <string name="message_hint">输入您的消息...</string>
    <string name="pitch">音调</string>
    <string name="reset_confirm_message">确定要将所有语音设置重置为默认值吗？</string>
    <string name="reset_confirm_title">重置设置</string>
    <string name="reset_settings">重置为默认设置</string>
    <string name="select_language">选择语言</string>
    <string name="select_voice_engine">选择语音引擎</string>
    <string name="send">发送</string>
    <string name="set_language_failed">设置语言失败</string>
    <string name="settings_reset">设置已重置为默认值</string>
    <string name="speech_rate">语速</string>
    <string name="start_chat">开始聊天</string>
    <string name="stop_test">停止测试</string>
    <string name="switch_engine_failed">切换引擎失败</string>
    <string name="test_voice">测试语音</string>
    <string name="voice_engine">语音引擎</string>
    <string name="voice_engine_settings">语音引擎设置</string>
    <string name="voice_test_failed">语音测试失败</string>
    <string name="voice_test_text_en">Hello, this is a voice test.</string>
    <string name="voice_test_text_zh">你好，这是语音测试。</string>
    <string name="welcome_message">您好！我是您的AI助手，有什么可以帮助您的吗？</string>
    <style name="AppBackButton">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:src">@drawable/ic_arrow_back</item>
        <item name="android:tint">@color/white</item>
        <item name="android:contentDescription">返回</item>
        <item name="android:padding">12dp</item>
    </style>
    <style name="AppContentLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
    </style>
    <style name="AppHeaderContent">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingHorizontal">20dp</item>
    </style>
    <style name="AppHeaderLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">?attr/colorHeader</item>
        <item name="android:elevation">4dp</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:paddingTop">@dimen/status_bar_height</item>
    </style>
    <style name="AppHeaderSpacer">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
    </style>
    <style name="AppHeaderTitle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="AppPageRootLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:orientation">vertical</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:background">?attr/colorAppBackground</item>
    </style>
    <style name="AppScrollView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:fillViewport">true</item>
        <item name="android:overScrollMode">ifContentScrolls</item>
    </style>
    <style name="Theme.Ragandroid" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="m">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowTranslucentStatus" ns1:targetApi="kitkat">false</item>
        
        
        <item name="android:windowSoftInputMode">adjustResize</item>

        
        <item name="colorThemePrimary">@color/ios_blue</item>
        <item name="colorThemePrimaryDark">@color/ios_blue_dark</item>
        <item name="colorThemePrimaryLight">@color/ios_blue_light</item>
        <item name="colorHeader">@color/ios_blue</item>
        <item name="colorHeaderDark">@color/ios_blue_dark</item>
        <item name="colorAccent">@color/ios_blue</item>
        <item name="colorTabSelected">@color/ios_blue</item>
        <item name="colorUserMessageBg">@color/ios_blue</item>
        <item name="colorProfileHeader">@color/ios_blue</item>
        <item name="colorFileIconDoc">@color/ios_blue</item>
        <item name="colorSendButton">@color/ios_blue</item>
        <item name="colorSendButtonPressed">@color/ios_blue_dark</item>
        <item name="colorBadgeBackground">@color/ios_blue</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>
    <style name="Theme.Ragandroid.Blue" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/ios_blue</item>
        <item name="colorThemePrimaryDark">@color/ios_blue_dark</item>
        <item name="colorThemePrimaryLight">@color/ios_blue_light</item>
        <item name="colorHeader">@color/ios_blue</item>
        <item name="colorHeaderDark">@color/ios_blue_dark</item>
        <item name="colorAccent">@color/ios_blue</item>
        <item name="colorTabSelected">@color/ios_blue</item>
        <item name="colorUserMessageBg">@color/ios_blue</item>
        <item name="colorProfileHeader">@color/ios_blue</item>
        <item name="colorFileIconDoc">@color/ios_blue</item>
        <item name="colorSendButton">@color/ios_blue</item>
        <item name="colorSendButtonPressed">@color/ios_blue_dark</item>
        <item name="colorBadgeBackground">@color/ios_blue</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>
    <style name="Theme.Ragandroid.Gray" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_gray</item>
        <item name="colorThemePrimaryDark">@color/theme_gray_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_gray_light</item>
        <item name="colorHeader">@color/theme_gray</item>
        <item name="colorHeaderDark">@color/theme_gray_dark</item>
        <item name="colorAccent">@color/theme_gray</item>
        <item name="colorTabSelected">@color/theme_gray</item>
        <item name="colorUserMessageBg">@color/theme_gray</item>
        <item name="colorProfileHeader">@color/theme_gray</item>
        <item name="colorFileIconDoc">@color/theme_gray</item>
        <item name="colorSendButton">@color/theme_gray</item>
        <item name="colorSendButtonPressed">@color/theme_gray_dark</item>
        <item name="colorBadgeBackground">@color/theme_gray</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>
    <style name="Theme.Ragandroid.Green" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_green</item>
        <item name="colorThemePrimaryDark">@color/theme_green_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_green_light</item>
        <item name="colorHeader">@color/theme_green</item>
        <item name="colorHeaderDark">@color/theme_green_dark</item>
        <item name="colorAccent">@color/theme_green</item>
        <item name="colorTabSelected">@color/theme_green</item>
        <item name="colorUserMessageBg">@color/theme_green</item>
        <item name="colorProfileHeader">@color/theme_green</item>
        <item name="colorFileIconDoc">@color/theme_green</item>
        <item name="colorSendButton">@color/theme_green</item>
        <item name="colorSendButtonPressed">@color/theme_green_dark</item>
        <item name="colorBadgeBackground">@color/theme_green</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>
    <style name="Theme.Ragandroid.Orange" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_orange</item>
        <item name="colorThemePrimaryDark">@color/theme_orange_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_orange_light</item>
        <item name="colorHeader">@color/theme_orange</item>
        <item name="colorHeaderDark">@color/theme_orange_dark</item>
        <item name="colorAccent">@color/theme_orange</item>
        <item name="colorTabSelected">@color/theme_orange</item>
        <item name="colorUserMessageBg">@color/theme_orange</item>
        <item name="colorProfileHeader">@color/theme_orange</item>
        <item name="colorFileIconDoc">@color/theme_orange</item>
        <item name="colorSendButton">@color/theme_orange</item>
        <item name="colorSendButtonPressed">@color/theme_orange_dark</item>
        <item name="colorBadgeBackground">@color/theme_orange</item>
    </style>
    <style name="Theme.Ragandroid.Pink" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_pink</item>
        <item name="colorThemePrimaryDark">@color/theme_pink_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_pink_light</item>
        <item name="colorHeader">@color/theme_pink</item>
        <item name="colorHeaderDark">@color/theme_pink_dark</item>
        <item name="colorAccent">@color/theme_pink</item>
        <item name="colorTabSelected">@color/theme_pink</item>
        <item name="colorUserMessageBg">@color/theme_pink</item>
        <item name="colorProfileHeader">@color/theme_pink</item>
        <item name="colorFileIconDoc">@color/theme_pink</item>
        <item name="colorSendButton">@color/theme_pink</item>
        <item name="colorSendButtonPressed">@color/theme_pink_dark</item>
        <item name="colorBadgeBackground">@color/theme_pink</item>
    </style>
    <style name="Theme.Ragandroid.Purple" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_purple</item>
        <item name="colorThemePrimaryDark">@color/theme_purple_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_purple_light</item>
        <item name="colorHeader">@color/theme_purple</item>
        <item name="colorHeaderDark">@color/theme_purple_dark</item>
        <item name="colorAccent">@color/theme_purple</item>
        <item name="colorTabSelected">@color/theme_purple</item>
        <item name="colorUserMessageBg">@color/theme_purple</item>
        <item name="colorProfileHeader">@color/theme_purple</item>
        <item name="colorFileIconDoc">@color/theme_purple</item>
        <item name="colorSendButton">@color/theme_purple</item>
        <item name="colorSendButtonPressed">@color/theme_purple_dark</item>
        <item name="colorBadgeBackground">@color/theme_purple</item>
    </style>
    <style name="Theme.Ragandroid.Red" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_red</item>
        <item name="colorThemePrimaryDark">@color/theme_red_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_red_light</item>
        <item name="colorHeader">@color/theme_red</item>
        <item name="colorHeaderDark">@color/theme_red_dark</item>
        <item name="colorAccent">@color/theme_red</item>
        <item name="colorTabSelected">@color/theme_red</item>
        <item name="colorUserMessageBg">@color/theme_red</item>
        <item name="colorProfileHeader">@color/theme_red</item>
        <item name="colorFileIconDoc">@color/theme_red</item>
        <item name="colorSendButton">@color/theme_red</item>
        <item name="colorSendButtonPressed">@color/theme_red_dark</item>
        <item name="colorBadgeBackground">@color/theme_red</item>
    </style>
</resources>