1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bei.rag"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 录音权限 -->
13-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.RECORD_AUDIO" />
14-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:10:5-71
14-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:10:22-68
15
16    <permission
16-->[androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.bei.rag.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.bei.rag.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:12:5-51:19
23        android:allowBackup="true"
23-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:13:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:14:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:15:9-54
29        android:icon="@mipmap/ic_launcher"
29-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:16:9-43
30        android:label="@string/app_name"
30-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:17:9-41
31        android:networkSecurityConfig="@xml/network_security_config"
31-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:21:9-69
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:18:9-54
33        android:supportsRtl="true"
33-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:19:9-35
34        android:testOnly="true"
35        android:theme="@style/Theme.Ragandroid" >
35-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:20:9-48
36
37        <!-- 状态栏测试Activity (仅Debug版本) -->
38        <activity
38-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:44:9-49:35
39            android:name="com.bei.rag.debug.StatusBarTestActivity"
39-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:45:13-56
40            android:exported="false"
40-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:46:13-37
41            android:label="状态栏测试工具"
41-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:47:13-36
42            android:parentActivityName="com.bei.rag.MainActivity"
42-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:48:13-55
43            android:theme="@style/Theme.Ragandroid" />
43-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml:11:13-52
44        <!-- 主Activity -->
45        <activity
45-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:25:9-33:20
46            android:name="com.bei.rag.MainActivity"
46-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:26:13-41
47            android:exported="true"
47-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:27:13-36
48            android:windowSoftInputMode="adjustResize" >
48-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:28:13-55
49            <intent-filter>
49-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:29:13-32:29
50                <action android:name="android.intent.action.MAIN" />
50-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:30:17-69
50-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:30:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:31:17-77
52-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:31:27-74
53            </intent-filter>
54        </activity> <!-- 聊天Activity -->
55        <!-- 聊天Activity -->
56        <activity
56-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:37:9-41:70
57            android:name="com.bei.rag.ChatScreen"
57-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:38:13-39
58            android:exported="false"
58-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:39:13-37
59            android:parentActivityName="com.bei.rag.MainActivity"
59-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:40:13-55
60            android:windowSoftInputMode="adjustResize|stateHidden" />
60-->D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:41:13-67
61
62        <service
62-->[androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
63            android:name="androidx.room.MultiInstanceInvalidationService"
63-->[androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
64            android:directBootAware="true"
64-->[androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
65            android:exported="false" />
65-->[androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
66
67        <provider
67-->[androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
68            android:name="androidx.startup.InitializationProvider"
68-->[androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
69            android:authorities="com.bei.rag.androidx-startup"
69-->[androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
70            android:exported="false" >
70-->[androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
71            <meta-data
71-->[androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.emoji2.text.EmojiCompatInitializer"
72-->[androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
73                android:value="androidx.startup" />
73-->[androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:14:13-16:52
75                android:name="io.github.jan.supabase.storage.SupabaseInitializer"
75-->[io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:15:17-82
76                android:value="androidx.startup" />
76-->[io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:16:17-49
77            <meta-data
77-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
78                android:name="io.github.jan.supabase.gotrue.SupabaseInitializer"
78-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
79                android:value="androidx.startup" />
79-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
80            <meta-data
80-->[androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:29:13-31:52
81                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
81-->[androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:30:17-78
82                android:value="androidx.startup" />
82-->[androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:31:17-49
83            <meta-data
83-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
84                android:name="com.russhwolf.settings.SettingsInitializer"
84-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
85                android:value="androidx.startup" />
85-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
86            <meta-data
86-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
87-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
88                android:value="androidx.startup" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
89        </provider>
90
91        <uses-library
91-->[androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
92            android:name="androidx.window.extensions"
92-->[androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
93            android:required="false" />
93-->[androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
94        <uses-library
94-->[androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
95            android:name="androidx.window.sidecar"
95-->[androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
96            android:required="false" />
96-->[androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
97
98        <receiver
98-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
99            android:name="androidx.profileinstaller.ProfileInstallReceiver"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
100            android:directBootAware="false"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
101            android:enabled="true"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
102            android:exported="true"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
103            android:permission="android.permission.DUMP" >
103-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
105                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
105-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
108                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
108-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
108-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
111                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
111-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
111-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
114                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
114-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
114-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
115            </intent-filter>
116        </receiver>
117    </application>
118
119</manifest>
