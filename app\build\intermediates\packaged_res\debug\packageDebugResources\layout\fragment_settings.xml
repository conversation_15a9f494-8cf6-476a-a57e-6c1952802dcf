<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/AppPageRootLayout"
    android:orientation="vertical">

    <!-- 标题栏 - 使用统一样式 -->
    <LinearLayout style="@style/AppHeaderLayout">
        <LinearLayout style="@style/AppHeaderContent">

            <!-- 返回按钮 -->
            <ImageButton
                android:id="@+id/btn_back"
                style="@style/AppBackButton" />

            <!-- 标题 -->
            <TextView
                style="@style/AppHeaderTitle"
                android:text="设置" />

            <!-- 占位 -->
            <View style="@style/AppHeaderSpacer" />

        </LinearLayout>
    </LinearLayout>

    <!-- 设置列表 -->
    <ScrollView style="@style/AppScrollView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingVertical="16dp">

            <!-- 主题设置 -->
            <LinearLayout
                android:id="@+id/setting_theme"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="主题设置"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="跟随系统"
                    android:textSize="14sp"
                    android:textColor="?attr/colorTextSecondary"
                    android:layout_marginEnd="8dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_right"
                    android:tint="@color/ios_text_secondary" />

            </LinearLayout>

            <!-- 分割线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color"
                android:layout_marginVertical="8dp" />

            <!-- 音色设置 -->
            <LinearLayout
                android:id="@+id/setting_voice"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="语音播放引擎"
                    android:textSize="16sp"
                    android:textColor="@color/text_dark" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="系统默认"
                    android:textSize="14sp"
                    android:textColor="@color/ios_text_secondary"
                    android:layout_marginEnd="8dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_right"
                    android:tint="@color/ios_text_secondary" />

            </LinearLayout>

            <!-- AI回复自动播放 -->
            <LinearLayout
                android:id="@+id/setting_auto_play"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/auto_play_ai_reply"
                        android:textSize="16sp"
                        android:textColor="@color/text_dark" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_auto_play"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false" />

                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/auto_play_ai_reply_desc"
                    android:textSize="12sp"
                    android:textColor="@color/ios_text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <!-- 分割线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color"
                android:layout_marginVertical="8dp" />


            <!-- 系统设置 -->
            <LinearLayout
                android:id="@+id/setting_system"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="系统设置"
                    android:textSize="16sp"
                    android:textColor="@color/text_dark" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_right"
                    android:tint="@color/ios_text_secondary" />

            </LinearLayout>

            <!-- 分割线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color"
                android:layout_marginVertical="8dp" />

        </LinearLayout>

    </ScrollView>

    <!-- 退出登录按钮 -->
    <LinearLayout
        android:id="@+id/btn_logout"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_margin="16dp"
        android:background="@drawable/ios_button_secondary"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="退出登录"
            android:textSize="16sp"
            android:textColor="@color/logout_button"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>