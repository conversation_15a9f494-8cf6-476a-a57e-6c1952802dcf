<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/AppPageRootLayout"
    android:orientation="vertical">

    <!-- 标题栏 - 使用统一样式 -->
    <LinearLayout style="@style/AppHeaderLayout">
        <LinearLayout style="@style/AppHeaderContent">

            <!-- 返回按钮 -->
            <ImageButton
                android:id="@+id/btn_back"
                style="@style/AppBackButton" />

            <!-- 标题 -->
            <TextView
                style="@style/AppHeaderTitle"
                android:text="主题设置" />

            <!-- 占位 -->
            <View style="@style/AppHeaderSpacer" />

        </LinearLayout>
    </LinearLayout>

    <!-- 设置列表 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:overScrollMode="ifContentScrolls">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingVertical="16dp"
            android:minHeight="200dp"
            android:background="?attr/colorAppBackground">

            <!-- 调试信息 - 临时添加 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="主题设置页面已加载"
                android:textSize="16sp"
                android:textColor="#FF0000"
                android:textStyle="bold"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:background="#FFFF00"
                android:visibility="visible" />

            <!-- 主题模式 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="主题模式"
                android:textSize="14sp"
                android:textColor="?attr/colorTextSecondary"
                android:textStyle="bold"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:visibility="visible" />

            <!-- 跟随系统 -->
            <LinearLayout
                android:id="@+id/theme_system"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <RadioButton
                    android:id="@+id/radio_follow_system"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="跟随系统"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/iv_system_check"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_check"
                    app:tint="?attr/colorThemePrimary"
                    android:visibility="visible" />

            </LinearLayout>

            <!-- 浅色模式 -->
            <LinearLayout
                android:id="@+id/theme_light"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <RadioButton
                    android:id="@+id/radio_light_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="浅色模式"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/iv_light_check"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_check"
                    app:tint="?attr/colorThemePrimary"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 深色模式 -->
            <LinearLayout
                android:id="@+id/theme_dark"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <RadioButton
                    android:id="@+id/radio_dark_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="深色模式"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/iv_dark_check"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_check"
                    app:tint="?attr/colorThemePrimary"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 主题颜色 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="主题颜色"
                android:textSize="14sp"
                android:textColor="?attr/colorTextSecondary"
                android:textStyle="bold"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:layout_marginTop="24dp" />

            <!-- 主题颜色选择 -->
            <LinearLayout
                android:id="@+id/setting_theme_color"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="选择主题颜色"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <TextView
                    android:id="@+id/tv_current_theme_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="蓝色"
                    android:textSize="14sp"
                    android:textColor="?attr/colorTextSecondary"
                    android:layout_marginEnd="8dp" />

                <View
                    android:id="@+id/current_color_preview"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/color_circle"
                    android:backgroundTint="?attr/colorThemePrimary"
                    android:layout_marginEnd="8dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="?attr/colorTextSecondary" />

            </LinearLayout>

            <!-- 字体设置 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="字体设置"
                android:textSize="14sp"
                android:textColor="?attr/colorTextSecondary"
                android:textStyle="bold"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:layout_marginTop="24dp" />

            <!-- 字体大小 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:background="?attr/colorCardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="字体大小"
                        android:textSize="16sp"
                        android:textColor="?attr/colorTextPrimary" />

                    <TextView
                        android:id="@+id/tv_font_size_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="标准"
                        android:textSize="14sp"
                        android:textColor="?attr/colorTextSecondary" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_font_size"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:max="4"
                    android:progress="2" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="小"
                        android:textSize="12sp"
                        android:textColor="?attr/colorTextSecondary"
                        android:gravity="start" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="大"
                        android:textSize="12sp"
                        android:textColor="?attr/colorTextSecondary"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>