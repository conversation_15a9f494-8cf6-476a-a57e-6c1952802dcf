(androidx.appcompat.app.AppCompatActivity1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder0androidx.viewpager2.adapter.FragmentStateAdapterandroidx.room.RoomDatabase#androidx.room.RoomDatabase.Callbackandroidx.fragment.app.Fragment2kotlinx.serialization.internal.GeneratedSerializer"com.bei.rag.model.VoiceToTextState"com.bei.rag.service.DocumentParser!com.bei.rag.service.tts.TtsEngine.android.speech.tts.TextToSpeech.OnInitListenerkotlin.Enum                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           