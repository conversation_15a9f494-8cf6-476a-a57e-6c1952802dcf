/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder1 0androidx.viewpager2.adapter.FragmentStateAdapter2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.room.RoomDatabase$ #androidx.room.RoomDatabase.Callback) (androidx.appcompat.app.AppCompatActivity androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer# "com.bei.rag.model.VoiceToTextState# "com.bei.rag.model.VoiceToTextState# "com.bei.rag.model.VoiceToTextState# "com.bei.rag.model.VoiceToTextState# "com.bei.rag.model.VoiceToTextState# "com.bei.rag.service.DocumentParser# "com.bei.rag.service.DocumentParser# "com.bei.rag.service.DocumentParser# "com.bei.rag.service.DocumentParserQ !com.bei.rag.service.tts.TtsEngine.android.speech.tts.TextToSpeech.OnInitListener" !com.bei.rag.service.tts.TtsEngine kotlin.Enum kotlin.Enum kotlin.Enum