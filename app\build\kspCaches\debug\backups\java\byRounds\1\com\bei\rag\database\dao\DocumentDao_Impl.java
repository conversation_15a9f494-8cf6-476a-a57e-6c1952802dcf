package com.bei.rag.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.bei.rag.database.entity.DocumentEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class DocumentDao_Impl implements DocumentDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<DocumentEntity> __insertionAdapterOfDocumentEntity;

  private final EntityDeletionOrUpdateAdapter<DocumentEntity> __deletionAdapterOfDocumentEntity;

  private final EntityDeletionOrUpdateAdapter<DocumentEntity> __updateAdapterOfDocumentEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteDocumentById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllDocuments;

  private final SharedSQLiteStatement __preparedStmtOfUpdateProcessingStatus;

  private final SharedSQLiteStatement __preparedStmtOfUpdateVectorizedStatus;

  private final SharedSQLiteStatement __preparedStmtOfUpdateProcessingError;

  private final SharedSQLiteStatement __preparedStmtOfUpdateExtractedText;

  public DocumentDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfDocumentEntity = new EntityInsertionAdapter<DocumentEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `documents` (`id`,`fileName`,`fileType`,`filePath`,`fileSize`,`uploadTime`,`isProcessed`,`summary`,`tags`,`isVectorized`,`chunkCount`,`processingStatus`,`errorMessage`,`extractedText`,`lastProcessedTime`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DocumentEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getFileName());
        statement.bindString(3, entity.getFileType());
        statement.bindString(4, entity.getFilePath());
        statement.bindLong(5, entity.getFileSize());
        statement.bindLong(6, entity.getUploadTime());
        final int _tmp = entity.isProcessed() ? 1 : 0;
        statement.bindLong(7, _tmp);
        statement.bindString(8, entity.getSummary());
        statement.bindString(9, entity.getTags());
        final int _tmp_1 = entity.isVectorized() ? 1 : 0;
        statement.bindLong(10, _tmp_1);
        statement.bindLong(11, entity.getChunkCount());
        statement.bindString(12, entity.getProcessingStatus());
        statement.bindString(13, entity.getErrorMessage());
        statement.bindString(14, entity.getExtractedText());
        statement.bindLong(15, entity.getLastProcessedTime());
      }
    };
    this.__deletionAdapterOfDocumentEntity = new EntityDeletionOrUpdateAdapter<DocumentEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `documents` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DocumentEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfDocumentEntity = new EntityDeletionOrUpdateAdapter<DocumentEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `documents` SET `id` = ?,`fileName` = ?,`fileType` = ?,`filePath` = ?,`fileSize` = ?,`uploadTime` = ?,`isProcessed` = ?,`summary` = ?,`tags` = ?,`isVectorized` = ?,`chunkCount` = ?,`processingStatus` = ?,`errorMessage` = ?,`extractedText` = ?,`lastProcessedTime` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DocumentEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getFileName());
        statement.bindString(3, entity.getFileType());
        statement.bindString(4, entity.getFilePath());
        statement.bindLong(5, entity.getFileSize());
        statement.bindLong(6, entity.getUploadTime());
        final int _tmp = entity.isProcessed() ? 1 : 0;
        statement.bindLong(7, _tmp);
        statement.bindString(8, entity.getSummary());
        statement.bindString(9, entity.getTags());
        final int _tmp_1 = entity.isVectorized() ? 1 : 0;
        statement.bindLong(10, _tmp_1);
        statement.bindLong(11, entity.getChunkCount());
        statement.bindString(12, entity.getProcessingStatus());
        statement.bindString(13, entity.getErrorMessage());
        statement.bindString(14, entity.getExtractedText());
        statement.bindLong(15, entity.getLastProcessedTime());
        statement.bindLong(16, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteDocumentById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM documents WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllDocuments = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM documents";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateProcessingStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE documents SET processingStatus = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateVectorizedStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE documents SET isVectorized = ?, chunkCount = ?, processingStatus = ?, lastProcessedTime = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateProcessingError = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE documents SET processingStatus = ?, errorMessage = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateExtractedText = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE documents SET extractedText = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertDocument(final DocumentEntity document,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfDocumentEntity.insertAndReturnId(document);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteDocument(final DocumentEntity document,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfDocumentEntity.handle(document);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateDocument(final DocumentEntity document,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfDocumentEntity.handle(document);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteDocumentById(final long documentId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteDocumentById.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, documentId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteDocumentById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllDocuments(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllDocuments.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllDocuments.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateProcessingStatus(final long documentId, final String status,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateProcessingStatus.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, status);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, documentId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateProcessingStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateVectorizedStatus(final long documentId, final boolean isVectorized,
      final int chunkCount, final String status, final long time,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateVectorizedStatus.acquire();
        int _argIndex = 1;
        final int _tmp = isVectorized ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, chunkCount);
        _argIndex = 3;
        _stmt.bindString(_argIndex, status);
        _argIndex = 4;
        _stmt.bindLong(_argIndex, time);
        _argIndex = 5;
        _stmt.bindLong(_argIndex, documentId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateVectorizedStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateProcessingError(final long documentId, final String status,
      final String errorMessage, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateProcessingError.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, status);
        _argIndex = 2;
        _stmt.bindString(_argIndex, errorMessage);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, documentId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateProcessingError.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateExtractedText(final long documentId, final String text,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateExtractedText.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, text);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, documentId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateExtractedText.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<DocumentEntity>> getAllDocuments() {
    final String _sql = "SELECT * FROM documents ORDER BY uploadTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"documents"}, new Callable<List<DocumentEntity>>() {
      @Override
      @NonNull
      public List<DocumentEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileType = CursorUtil.getColumnIndexOrThrow(_cursor, "fileType");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadTime");
          final int _cursorIndexOfIsProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "isProcessed");
          final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfIsVectorized = CursorUtil.getColumnIndexOrThrow(_cursor, "isVectorized");
          final int _cursorIndexOfChunkCount = CursorUtil.getColumnIndexOrThrow(_cursor, "chunkCount");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfExtractedText = CursorUtil.getColumnIndexOrThrow(_cursor, "extractedText");
          final int _cursorIndexOfLastProcessedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastProcessedTime");
          final List<DocumentEntity> _result = new ArrayList<DocumentEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DocumentEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final String _tmpFileType;
            _tmpFileType = _cursor.getString(_cursorIndexOfFileType);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpUploadTime;
            _tmpUploadTime = _cursor.getLong(_cursorIndexOfUploadTime);
            final boolean _tmpIsProcessed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsProcessed);
            _tmpIsProcessed = _tmp != 0;
            final String _tmpSummary;
            _tmpSummary = _cursor.getString(_cursorIndexOfSummary);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final boolean _tmpIsVectorized;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVectorized);
            _tmpIsVectorized = _tmp_1 != 0;
            final int _tmpChunkCount;
            _tmpChunkCount = _cursor.getInt(_cursorIndexOfChunkCount);
            final String _tmpProcessingStatus;
            _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            final String _tmpErrorMessage;
            _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            final String _tmpExtractedText;
            _tmpExtractedText = _cursor.getString(_cursorIndexOfExtractedText);
            final long _tmpLastProcessedTime;
            _tmpLastProcessedTime = _cursor.getLong(_cursorIndexOfLastProcessedTime);
            _item = new DocumentEntity(_tmpId,_tmpFileName,_tmpFileType,_tmpFilePath,_tmpFileSize,_tmpUploadTime,_tmpIsProcessed,_tmpSummary,_tmpTags,_tmpIsVectorized,_tmpChunkCount,_tmpProcessingStatus,_tmpErrorMessage,_tmpExtractedText,_tmpLastProcessedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllDocumentsSync(final Continuation<? super List<DocumentEntity>> $completion) {
    final String _sql = "SELECT * FROM documents ORDER BY uploadTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<DocumentEntity>>() {
      @Override
      @NonNull
      public List<DocumentEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileType = CursorUtil.getColumnIndexOrThrow(_cursor, "fileType");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadTime");
          final int _cursorIndexOfIsProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "isProcessed");
          final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfIsVectorized = CursorUtil.getColumnIndexOrThrow(_cursor, "isVectorized");
          final int _cursorIndexOfChunkCount = CursorUtil.getColumnIndexOrThrow(_cursor, "chunkCount");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfExtractedText = CursorUtil.getColumnIndexOrThrow(_cursor, "extractedText");
          final int _cursorIndexOfLastProcessedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastProcessedTime");
          final List<DocumentEntity> _result = new ArrayList<DocumentEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DocumentEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final String _tmpFileType;
            _tmpFileType = _cursor.getString(_cursorIndexOfFileType);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpUploadTime;
            _tmpUploadTime = _cursor.getLong(_cursorIndexOfUploadTime);
            final boolean _tmpIsProcessed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsProcessed);
            _tmpIsProcessed = _tmp != 0;
            final String _tmpSummary;
            _tmpSummary = _cursor.getString(_cursorIndexOfSummary);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final boolean _tmpIsVectorized;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVectorized);
            _tmpIsVectorized = _tmp_1 != 0;
            final int _tmpChunkCount;
            _tmpChunkCount = _cursor.getInt(_cursorIndexOfChunkCount);
            final String _tmpProcessingStatus;
            _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            final String _tmpErrorMessage;
            _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            final String _tmpExtractedText;
            _tmpExtractedText = _cursor.getString(_cursorIndexOfExtractedText);
            final long _tmpLastProcessedTime;
            _tmpLastProcessedTime = _cursor.getLong(_cursorIndexOfLastProcessedTime);
            _item = new DocumentEntity(_tmpId,_tmpFileName,_tmpFileType,_tmpFilePath,_tmpFileSize,_tmpUploadTime,_tmpIsProcessed,_tmpSummary,_tmpTags,_tmpIsVectorized,_tmpChunkCount,_tmpProcessingStatus,_tmpErrorMessage,_tmpExtractedText,_tmpLastProcessedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<DocumentEntity>> getDocumentsByType(final String fileType) {
    final String _sql = "SELECT * FROM documents WHERE fileType = ? ORDER BY uploadTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, fileType);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"documents"}, new Callable<List<DocumentEntity>>() {
      @Override
      @NonNull
      public List<DocumentEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileType = CursorUtil.getColumnIndexOrThrow(_cursor, "fileType");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadTime");
          final int _cursorIndexOfIsProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "isProcessed");
          final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfIsVectorized = CursorUtil.getColumnIndexOrThrow(_cursor, "isVectorized");
          final int _cursorIndexOfChunkCount = CursorUtil.getColumnIndexOrThrow(_cursor, "chunkCount");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfExtractedText = CursorUtil.getColumnIndexOrThrow(_cursor, "extractedText");
          final int _cursorIndexOfLastProcessedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastProcessedTime");
          final List<DocumentEntity> _result = new ArrayList<DocumentEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DocumentEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final String _tmpFileType;
            _tmpFileType = _cursor.getString(_cursorIndexOfFileType);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpUploadTime;
            _tmpUploadTime = _cursor.getLong(_cursorIndexOfUploadTime);
            final boolean _tmpIsProcessed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsProcessed);
            _tmpIsProcessed = _tmp != 0;
            final String _tmpSummary;
            _tmpSummary = _cursor.getString(_cursorIndexOfSummary);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final boolean _tmpIsVectorized;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVectorized);
            _tmpIsVectorized = _tmp_1 != 0;
            final int _tmpChunkCount;
            _tmpChunkCount = _cursor.getInt(_cursorIndexOfChunkCount);
            final String _tmpProcessingStatus;
            _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            final String _tmpErrorMessage;
            _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            final String _tmpExtractedText;
            _tmpExtractedText = _cursor.getString(_cursorIndexOfExtractedText);
            final long _tmpLastProcessedTime;
            _tmpLastProcessedTime = _cursor.getLong(_cursorIndexOfLastProcessedTime);
            _item = new DocumentEntity(_tmpId,_tmpFileName,_tmpFileType,_tmpFilePath,_tmpFileSize,_tmpUploadTime,_tmpIsProcessed,_tmpSummary,_tmpTags,_tmpIsVectorized,_tmpChunkCount,_tmpProcessingStatus,_tmpErrorMessage,_tmpExtractedText,_tmpLastProcessedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object searchDocuments(final String query,
      final Continuation<? super List<DocumentEntity>> $completion) {
    final String _sql = "SELECT * FROM documents WHERE fileName LIKE '%' || ? || '%' ORDER BY uploadTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, query);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<DocumentEntity>>() {
      @Override
      @NonNull
      public List<DocumentEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileType = CursorUtil.getColumnIndexOrThrow(_cursor, "fileType");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadTime");
          final int _cursorIndexOfIsProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "isProcessed");
          final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfIsVectorized = CursorUtil.getColumnIndexOrThrow(_cursor, "isVectorized");
          final int _cursorIndexOfChunkCount = CursorUtil.getColumnIndexOrThrow(_cursor, "chunkCount");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfExtractedText = CursorUtil.getColumnIndexOrThrow(_cursor, "extractedText");
          final int _cursorIndexOfLastProcessedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastProcessedTime");
          final List<DocumentEntity> _result = new ArrayList<DocumentEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DocumentEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final String _tmpFileType;
            _tmpFileType = _cursor.getString(_cursorIndexOfFileType);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpUploadTime;
            _tmpUploadTime = _cursor.getLong(_cursorIndexOfUploadTime);
            final boolean _tmpIsProcessed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsProcessed);
            _tmpIsProcessed = _tmp != 0;
            final String _tmpSummary;
            _tmpSummary = _cursor.getString(_cursorIndexOfSummary);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final boolean _tmpIsVectorized;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVectorized);
            _tmpIsVectorized = _tmp_1 != 0;
            final int _tmpChunkCount;
            _tmpChunkCount = _cursor.getInt(_cursorIndexOfChunkCount);
            final String _tmpProcessingStatus;
            _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            final String _tmpErrorMessage;
            _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            final String _tmpExtractedText;
            _tmpExtractedText = _cursor.getString(_cursorIndexOfExtractedText);
            final long _tmpLastProcessedTime;
            _tmpLastProcessedTime = _cursor.getLong(_cursorIndexOfLastProcessedTime);
            _item = new DocumentEntity(_tmpId,_tmpFileName,_tmpFileType,_tmpFilePath,_tmpFileSize,_tmpUploadTime,_tmpIsProcessed,_tmpSummary,_tmpTags,_tmpIsVectorized,_tmpChunkCount,_tmpProcessingStatus,_tmpErrorMessage,_tmpExtractedText,_tmpLastProcessedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getDocumentCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM documents";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalFileSize(final Continuation<? super Long> $completion) {
    final String _sql = "SELECT SUM(fileSize) FROM documents";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final long _tmp;
            _tmp = _cursor.getLong(0);
            _result = _tmp;
          } else {
            _result = 0L;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getDocumentById(final long documentId,
      final Continuation<? super DocumentEntity> $completion) {
    final String _sql = "SELECT * FROM documents WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, documentId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<DocumentEntity>() {
      @Override
      @Nullable
      public DocumentEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileType = CursorUtil.getColumnIndexOrThrow(_cursor, "fileType");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadTime");
          final int _cursorIndexOfIsProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "isProcessed");
          final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfIsVectorized = CursorUtil.getColumnIndexOrThrow(_cursor, "isVectorized");
          final int _cursorIndexOfChunkCount = CursorUtil.getColumnIndexOrThrow(_cursor, "chunkCount");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfExtractedText = CursorUtil.getColumnIndexOrThrow(_cursor, "extractedText");
          final int _cursorIndexOfLastProcessedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastProcessedTime");
          final DocumentEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final String _tmpFileType;
            _tmpFileType = _cursor.getString(_cursorIndexOfFileType);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpUploadTime;
            _tmpUploadTime = _cursor.getLong(_cursorIndexOfUploadTime);
            final boolean _tmpIsProcessed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsProcessed);
            _tmpIsProcessed = _tmp != 0;
            final String _tmpSummary;
            _tmpSummary = _cursor.getString(_cursorIndexOfSummary);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final boolean _tmpIsVectorized;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVectorized);
            _tmpIsVectorized = _tmp_1 != 0;
            final int _tmpChunkCount;
            _tmpChunkCount = _cursor.getInt(_cursorIndexOfChunkCount);
            final String _tmpProcessingStatus;
            _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            final String _tmpErrorMessage;
            _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            final String _tmpExtractedText;
            _tmpExtractedText = _cursor.getString(_cursorIndexOfExtractedText);
            final long _tmpLastProcessedTime;
            _tmpLastProcessedTime = _cursor.getLong(_cursorIndexOfLastProcessedTime);
            _result = new DocumentEntity(_tmpId,_tmpFileName,_tmpFileType,_tmpFilePath,_tmpFileSize,_tmpUploadTime,_tmpIsProcessed,_tmpSummary,_tmpTags,_tmpIsVectorized,_tmpChunkCount,_tmpProcessingStatus,_tmpErrorMessage,_tmpExtractedText,_tmpLastProcessedTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getDocumentByIdSync(final long documentId,
      final Continuation<? super DocumentEntity> $completion) {
    final String _sql = "SELECT * FROM documents WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, documentId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<DocumentEntity>() {
      @Override
      @Nullable
      public DocumentEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileType = CursorUtil.getColumnIndexOrThrow(_cursor, "fileType");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadTime");
          final int _cursorIndexOfIsProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "isProcessed");
          final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfIsVectorized = CursorUtil.getColumnIndexOrThrow(_cursor, "isVectorized");
          final int _cursorIndexOfChunkCount = CursorUtil.getColumnIndexOrThrow(_cursor, "chunkCount");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfExtractedText = CursorUtil.getColumnIndexOrThrow(_cursor, "extractedText");
          final int _cursorIndexOfLastProcessedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastProcessedTime");
          final DocumentEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final String _tmpFileType;
            _tmpFileType = _cursor.getString(_cursorIndexOfFileType);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpUploadTime;
            _tmpUploadTime = _cursor.getLong(_cursorIndexOfUploadTime);
            final boolean _tmpIsProcessed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsProcessed);
            _tmpIsProcessed = _tmp != 0;
            final String _tmpSummary;
            _tmpSummary = _cursor.getString(_cursorIndexOfSummary);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final boolean _tmpIsVectorized;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVectorized);
            _tmpIsVectorized = _tmp_1 != 0;
            final int _tmpChunkCount;
            _tmpChunkCount = _cursor.getInt(_cursorIndexOfChunkCount);
            final String _tmpProcessingStatus;
            _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            final String _tmpErrorMessage;
            _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            final String _tmpExtractedText;
            _tmpExtractedText = _cursor.getString(_cursorIndexOfExtractedText);
            final long _tmpLastProcessedTime;
            _tmpLastProcessedTime = _cursor.getLong(_cursorIndexOfLastProcessedTime);
            _result = new DocumentEntity(_tmpId,_tmpFileName,_tmpFileType,_tmpFilePath,_tmpFileSize,_tmpUploadTime,_tmpIsProcessed,_tmpSummary,_tmpTags,_tmpIsVectorized,_tmpChunkCount,_tmpProcessingStatus,_tmpErrorMessage,_tmpExtractedText,_tmpLastProcessedTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUnprocessedDocuments(
      final Continuation<? super List<DocumentEntity>> $completion) {
    final String _sql = "SELECT * FROM documents WHERE isVectorized = 0 AND processingStatus = 'pending' ORDER BY uploadTime ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<DocumentEntity>>() {
      @Override
      @NonNull
      public List<DocumentEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileType = CursorUtil.getColumnIndexOrThrow(_cursor, "fileType");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadTime");
          final int _cursorIndexOfIsProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "isProcessed");
          final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfIsVectorized = CursorUtil.getColumnIndexOrThrow(_cursor, "isVectorized");
          final int _cursorIndexOfChunkCount = CursorUtil.getColumnIndexOrThrow(_cursor, "chunkCount");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfExtractedText = CursorUtil.getColumnIndexOrThrow(_cursor, "extractedText");
          final int _cursorIndexOfLastProcessedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastProcessedTime");
          final List<DocumentEntity> _result = new ArrayList<DocumentEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DocumentEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final String _tmpFileType;
            _tmpFileType = _cursor.getString(_cursorIndexOfFileType);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpUploadTime;
            _tmpUploadTime = _cursor.getLong(_cursorIndexOfUploadTime);
            final boolean _tmpIsProcessed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsProcessed);
            _tmpIsProcessed = _tmp != 0;
            final String _tmpSummary;
            _tmpSummary = _cursor.getString(_cursorIndexOfSummary);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final boolean _tmpIsVectorized;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVectorized);
            _tmpIsVectorized = _tmp_1 != 0;
            final int _tmpChunkCount;
            _tmpChunkCount = _cursor.getInt(_cursorIndexOfChunkCount);
            final String _tmpProcessingStatus;
            _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            final String _tmpErrorMessage;
            _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            final String _tmpExtractedText;
            _tmpExtractedText = _cursor.getString(_cursorIndexOfExtractedText);
            final long _tmpLastProcessedTime;
            _tmpLastProcessedTime = _cursor.getLong(_cursorIndexOfLastProcessedTime);
            _item = new DocumentEntity(_tmpId,_tmpFileName,_tmpFileType,_tmpFilePath,_tmpFileSize,_tmpUploadTime,_tmpIsProcessed,_tmpSummary,_tmpTags,_tmpIsVectorized,_tmpChunkCount,_tmpProcessingStatus,_tmpErrorMessage,_tmpExtractedText,_tmpLastProcessedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getVectorizedDocuments(
      final Continuation<? super List<DocumentEntity>> $completion) {
    final String _sql = "SELECT * FROM documents WHERE isVectorized = 1 ORDER BY uploadTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<DocumentEntity>>() {
      @Override
      @NonNull
      public List<DocumentEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileType = CursorUtil.getColumnIndexOrThrow(_cursor, "fileType");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadTime");
          final int _cursorIndexOfIsProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "isProcessed");
          final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfIsVectorized = CursorUtil.getColumnIndexOrThrow(_cursor, "isVectorized");
          final int _cursorIndexOfChunkCount = CursorUtil.getColumnIndexOrThrow(_cursor, "chunkCount");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfExtractedText = CursorUtil.getColumnIndexOrThrow(_cursor, "extractedText");
          final int _cursorIndexOfLastProcessedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastProcessedTime");
          final List<DocumentEntity> _result = new ArrayList<DocumentEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DocumentEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final String _tmpFileType;
            _tmpFileType = _cursor.getString(_cursorIndexOfFileType);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpUploadTime;
            _tmpUploadTime = _cursor.getLong(_cursorIndexOfUploadTime);
            final boolean _tmpIsProcessed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsProcessed);
            _tmpIsProcessed = _tmp != 0;
            final String _tmpSummary;
            _tmpSummary = _cursor.getString(_cursorIndexOfSummary);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final boolean _tmpIsVectorized;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVectorized);
            _tmpIsVectorized = _tmp_1 != 0;
            final int _tmpChunkCount;
            _tmpChunkCount = _cursor.getInt(_cursorIndexOfChunkCount);
            final String _tmpProcessingStatus;
            _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            final String _tmpErrorMessage;
            _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            final String _tmpExtractedText;
            _tmpExtractedText = _cursor.getString(_cursorIndexOfExtractedText);
            final long _tmpLastProcessedTime;
            _tmpLastProcessedTime = _cursor.getLong(_cursorIndexOfLastProcessedTime);
            _item = new DocumentEntity(_tmpId,_tmpFileName,_tmpFileType,_tmpFilePath,_tmpFileSize,_tmpUploadTime,_tmpIsProcessed,_tmpSummary,_tmpTags,_tmpIsVectorized,_tmpChunkCount,_tmpProcessingStatus,_tmpErrorMessage,_tmpExtractedText,_tmpLastProcessedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getVectorizedDocumentCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM documents WHERE isVectorized = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
