  Activity android.app  Context android.content  ContextWrapper android.content  TextToSpeech android.speech.tts  ContextThemeWrapper android.view  ComponentActivity androidx.activity  AppCompatActivity androidx.appcompat.app  ComponentActivity androidx.core.app  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  OnConflictStrategy 
androidx.room  RoomDatabase 
androidx.room  	Companion  androidx.room.OnConflictStrategy  Callback androidx.room.RoomDatabase  	Migration androidx.room.migration  SupportSQLiteDatabase androidx.sqlite.db  
ChatScreen com.bei.rag  MainActivity com.bei.rag  ChatAdapter com.bei.rag.adapter  ConversationAdapter com.bei.rag.adapter  ConversationGroupAdapter com.bei.rag.adapter  DocumentAdapter com.bei.rag.adapter  NavConversationAdapter com.bei.rag.adapter  NavGroupAdapter com.bei.rag.adapter  AiMessageViewHolder com.bei.rag.adapter.ChatAdapter  	Companion com.bei.rag.adapter.ChatAdapter  UserMessageViewHolder com.bei.rag.adapter.ChatAdapter  ConversationViewHolder 'com.bei.rag.adapter.ConversationAdapter  GroupViewHolder ,com.bei.rag.adapter.ConversationGroupAdapter  DocumentViewHolder #com.bei.rag.adapter.DocumentAdapter  NavConversationViewHolder *com.bei.rag.adapter.NavConversationAdapter  NavGroupViewHolder #com.bei.rag.adapter.NavGroupAdapter  AppDatabase com.bei.rag.database  	Companion  com.bei.rag.database.AppDatabase  DatabaseCallback *com.bei.rag.database.AppDatabase.Companion  StringListConverter com.bei.rag.database.converter  ChatMessageDao com.bei.rag.database.dao  ConversationGroupDao com.bei.rag.database.dao  DocumentDao com.bei.rag.database.dao  UserDao com.bei.rag.database.dao  ChatMessageEntity com.bei.rag.database.entity  ConversationGroupEntity com.bei.rag.database.entity  DocumentEntity com.bei.rag.database.entity  
UserEntity com.bei.rag.database.entity  StatusBarTestActivity com.bei.rag.debug  ChatFragment com.bei.rag.fragment  ConversationGroupsFragment com.bei.rag.fragment  ConversationListFragment com.bei.rag.fragment  KnowledgeFragment com.bei.rag.fragment  ProfileFragment com.bei.rag.fragment  SettingsFragment com.bei.rag.fragment  SystemSettingsFragment com.bei.rag.fragment  ThemeColorSettingsFragment com.bei.rag.fragment  ThemeSettingsFragment com.bei.rag.fragment  VoiceEngineSettingsFragment com.bei.rag.fragment  	Companion !com.bei.rag.fragment.ChatFragment  	Companion /com.bei.rag.fragment.ConversationGroupsFragment  	Companion -com.bei.rag.fragment.ConversationListFragment  	Companion &com.bei.rag.fragment.KnowledgeFragment  	Companion $com.bei.rag.fragment.ProfileFragment  	Companion %com.bei.rag.fragment.SettingsFragment  	Companion +com.bei.rag.fragment.SystemSettingsFragment  	Companion /com.bei.rag.fragment.ThemeColorSettingsFragment  	Companion *com.bei.rag.fragment.ThemeSettingsFragment  	Companion 0com.bei.rag.fragment.VoiceEngineSettingsFragment  BatchOperationResult com.bei.rag.model  ChatMessage com.bei.rag.model  ChatRequest com.bei.rag.model  ChatResponse com.bei.rag.model  Choice com.bei.rag.model  Content com.bei.rag.model  
EmbeddingData com.bei.rag.model  EmbeddingRequest com.bei.rag.model  EmbeddingResponse com.bei.rag.model  EmbeddingUsage com.bei.rag.model  Message com.bei.rag.model  ResponseMessage com.bei.rag.model  SupabaseDocument com.bei.rag.model  SupabaseDocumentChunk com.bei.rag.model  TtsCandidate com.bei.rag.model  
TtsContent com.bei.rag.model  TtsError com.bei.rag.model  TtsGenerationConfig com.bei.rag.model  
TtsInlineData com.bei.rag.model  TtsPart com.bei.rag.model  TtsPrebuiltVoiceConfig com.bei.rag.model  
TtsRequest com.bei.rag.model  TtsResponse com.bei.rag.model  TtsResponseContent com.bei.rag.model  TtsResponsePart com.bei.rag.model  TtsSpeechConfig com.bei.rag.model  TtsVoiceConfig com.bei.rag.model  Usage com.bei.rag.model  GeminiTtsApiService com.bei.rag.service  OpenRouterApiService com.bei.rag.service  SiliconFlowApiService com.bei.rag.service  SiliconFlowEmbeddingService com.bei.rag.service  SupabaseConfig com.bei.rag.service  TextToSpeechService com.bei.rag.service  VoiceToTextService com.bei.rag.service  	Companion 'com.bei.rag.service.TextToSpeechService  AndroidTtsEngine com.bei.rag.service.tts  GeminiTtsEngine com.bei.rag.service.tts  	Companion (com.bei.rag.service.tts.AndroidTtsEngine  	Companion 'com.bei.rag.service.tts.GeminiTtsEngine  DataManager com.bei.rag.utils  StatusBarAnimationManager com.bei.rag.utils  StatusBarColorAdapter com.bei.rag.utils  StatusBarCompatManager com.bei.rag.utils  StatusBarManager com.bei.rag.utils  StatusBarOverlayManager com.bei.rag.utils  StatusBarTestHelper com.bei.rag.utils  ThemeManager com.bei.rag.utils  	TtsConfig com.bei.rag.utils  	Companion +com.bei.rag.utils.StatusBarAnimationManager  StatusBarConfig 'com.bei.rag.utils.StatusBarColorAdapter  	Companion (com.bei.rag.utils.StatusBarCompatManager  	Companion "com.bei.rag.utils.StatusBarManager  	Companion )com.bei.rag.utils.StatusBarOverlayManager  	Companion %com.bei.rag.utils.StatusBarTestHelper  	Companion com.bei.rag.utils.ThemeManager  	Companion com.bei.rag.utils.TtsConfig  
MultipartBody okhttp3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               