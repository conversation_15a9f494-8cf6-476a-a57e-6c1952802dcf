{"src\\main\\java\\com\\bei\\rag\\service\\SupabaseDocumentService.kt": ["Companion:com.bei.rag.service.SupabaseDocumentService", "getSyncStatus:com.bei.rag.service.SupabaseDocumentService", "syncSupabaseToLocal:com.bei.rag.service.SupabaseDocumentService", "uploadDocumentToSupabase:com.bei.rag.service.SupabaseDocumentService", "syncLocalToSupabase:com.bei.rag.service.SupabaseDocumentService", "<init>:com.bei.rag.service.SupabaseDocumentService.Companion", "SupabaseDocumentService:com.bei.rag.service", "checkOnlineStatus:com.bei.rag.service.SupabaseDocumentService"], "src\\main\\java\\com\\bei\\rag\\model\\VoiceToTextResponse.kt": ["text:com.bei.rag.model.VoiceToTextState.Success", "<init>:com.bei.rag.model.VoiceToTextState.Processing", "<init>:com.bei.rag.model.VoiceToTextState", "Processing:com.bei.rag.model.VoiceToTextState", "<init>:com.bei.rag.model.VoiceToTextState.Idle", "Recording:com.bei.rag.model.VoiceToTextState", "text:com.bei.rag.model.VoiceToTextResponse", "Error:com.bei.rag.model.VoiceToTextState", "Idle:com.bei.rag.model.VoiceToTextState", "message:com.bei.rag.model.VoiceToTextState.Error", "Success:com.bei.rag.model.VoiceToTextState", "VoiceToTextState:com.bei.rag.model", "VoiceToTextResponse:com.bei.rag.model", "<init>:com.bei.rag.model.VoiceToTextState.Recording"], "src\\main\\java\\com\\bei\\rag\\service\\DocumentParser.kt": ["getSupportedExtensions:com.bei.rag.service.DocxParser", "DocumentParsingManager:com.bei.rag.service", "DocumentParser:com.bei.rag.service", "isFileTypeSupported:com.bei.rag.service.DocumentParsingManager", "getSupportedExtensions:com.bei.rag.service.DocumentParser", "parseDocument:com.bei.rag.service.CsvParser", "parseDocument:com.bei.rag.service.DocumentParsingManager", "getSupportedExtensions:com.bei.rag.service.TxtParser", "<init>:com.bei.rag.service.TxtParser", "Companion:com.bei.rag.service.DocumentParserFactory", "<init>:com.bei.rag.service.DocumentParserFactory.Companion", "<init>:com.bei.rag.service.DocumentParserFactory", "DocxParser:com.bei.rag.service", "getSupportedExtensions:com.bei.rag.service.CsvParser", "<init>:com.bei.rag.service.CsvParser", "getParser:com.bei.rag.service.DocumentParserFactory.Companion", "getSupportedFileTypes:com.bei.rag.service.DocumentParsingManager", "getSupportedExtensions:com.bei.rag.service.PdfParser", "TxtParser:com.bei.rag.service", "PdfParser:com.bei.rag.service", "DocumentParserFactory:com.bei.rag.service", "parseDocument:com.bei.rag.service.DocxParser", "parseDocument:com.bei.rag.service.DocumentParser", "parseDocument:com.bei.rag.service.PdfParser", "parseDocument:com.bei.rag.service.TxtParser", "CsvParser:com.bei.rag.service"], "src\\main\\java\\com\\bei\\rag\\model\\EmbeddingModels.kt": ["metadata:com.bei.rag.model.DocumentContent", "data:com.bei.rag.model.EmbeddingResponse", "totalTokens:com.bei.rag.model.EmbeddingUsage", "chunkIndex:com.bei.rag.model.DocumentChunk", "model:com.bei.rag.model.EmbeddingResponse", "encodingFormat:com.bei.rag.model.EmbeddingRequest", "hashCode:com.bei.rag.model.DocumentChunk", "startIndex:com.bei.rag.model.DocumentChunk", "endIndex:com.bei.rag.model.DocumentChunk", "equals:com.bei.rag.model.DocumentChunk", "text:com.bei.rag.model.DocumentContent", "input:com.bei.rag.model.EmbeddingRequest", "DocumentContent:com.bei.rag.model", "EmbeddingData:com.bei.rag.model", "EmbeddingResponse:com.bei.rag.model", "metadata:com.bei.rag.model.DocumentChunk", "usage:com.bei.rag.model.EmbeddingResponse", "id:com.bei.rag.model.DocumentChunk", "index:com.bei.rag.model.EmbeddingData", "DocumentChunk:com.bei.rag.model", "embedding:com.bei.rag.model.DocumentChunk", "promptTokens:com.bei.rag.model.EmbeddingUsage", "objectType:com.bei.rag.model.EmbeddingData", "completionTokens:com.bei.rag.model.EmbeddingUsage", "documentId:com.bei.rag.model.DocumentChunk", "embedding:com.bei.rag.model.EmbeddingData", "objectType:com.bei.rag.model.EmbeddingResponse", "model:com.bei.rag.model.EmbeddingRequest", "EmbeddingUsage:com.bei.rag.model", "EmbeddingRequest:com.bei.rag.model", "content:com.bei.rag.model.DocumentChunk"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\bei\\rag\\database\\dao\\DocumentDao_Impl.java": ["getVectorizedDocuments:com.bei.rag.database.dao.DocumentDao_Impl", "updateVectorizedStatus:com.bei.rag.database.dao.DocumentDao_Impl", "getDocumentByIdSync:com.bei.rag.database.dao.DocumentDao_Impl", "getRequiredConverters:com.bei.rag.database.dao.DocumentDao_Impl", "updateDocument:com.bei.rag.database.dao.DocumentDao_Impl", "updateProcessingError:com.bei.rag.database.dao.DocumentDao_Impl", "deleteDocumentById:com.bei.rag.database.dao.DocumentDao_Impl", "<init>:com.bei.rag.database.dao.DocumentDao_Impl", "getDocumentsByType:com.bei.rag.database.dao.DocumentDao_Impl", "getTotalFileSize:com.bei.rag.database.dao.DocumentDao_Impl", "deleteAllDocuments:com.bei.rag.database.dao.DocumentDao_Impl", "getUnprocessedDocuments:com.bei.rag.database.dao.DocumentDao_Impl", "DocumentDao_Impl:com.bei.rag.database.dao", "searchDocuments:com.bei.rag.database.dao.DocumentDao_Impl", "updateProcessingStatus:com.bei.rag.database.dao.DocumentDao_Impl", "updateExtractedText:com.bei.rag.database.dao.DocumentDao_Impl", "getDocumentCount:com.bei.rag.database.dao.DocumentDao_Impl", "getDocumentById:com.bei.rag.database.dao.DocumentDao_Impl", "deleteDocument:com.bei.rag.database.dao.DocumentDao_Impl", "getAllDocumentsSync:com.bei.rag.database.dao.DocumentDao_Impl", "insertDocument:com.bei.rag.database.dao.DocumentDao_Impl", "getAllDocuments:com.bei.rag.database.dao.DocumentDao_Impl", "getVectorizedDocumentCount:com.bei.rag.database.dao.DocumentDao_Impl"], "src\\main\\java\\com\\bei\\rag\\model\\ApiModels.kt": ["Choice:com.bei.rag.model", "model:com.bei.rag.model.ChatRequest", "finishReason:com.bei.rag.model.Choice", "reasoning:com.bei.rag.model.ResponseMessage", "refusal:com.bei.rag.model.ResponseMessage", "content:com.bei.rag.model.Message", "message:com.bei.rag.model.Choice", "completionTokens:com.bei.rag.model.Usage", "id:com.bei.rag.model.ChatResponse", "model:com.bei.rag.model.ChatResponse", "totalTokens:com.bei.rag.model.Usage", "ChatResponse:com.bei.rag.model", "Message:com.bei.rag.model", "objectType:com.bei.rag.model.ChatResponse", "Content:com.bei.rag.model", "ChatRequest:com.bei.rag.model", "role:com.bei.rag.model.Message", "text:com.bei.rag.model.Content", "messages:com.bei.rag.model.ChatRequest", "usage:com.bei.rag.model.ChatResponse", "type:com.bei.rag.model.Content", "created:com.bei.rag.model.ChatResponse", "promptTokens:com.bei.rag.model.Usage", "provider:com.bei.rag.model.ChatResponse", "nativeFinishReason:com.bei.rag.model.Choice", "index:com.bei.rag.model.Choice", "choices:com.bei.rag.model.ChatResponse", "content:com.bei.rag.model.ResponseMessage", "ResponseMessage:com.bei.rag.model", "role:com.bei.rag.model.ResponseMessage", "Usage:com.bei.rag.model", "logprobs:com.bei.rag.model.Choice"], "src\\main\\java\\com\\bei\\rag\\service\\KnowledgeBaseManager.kt": ["totalDocuments:com.bei.rag.service.KnowledgeBaseStats", "totalSize:com.bei.rag.service.KnowledgeBaseStats", "content:com.bei.rag.service.SearchResult", "similarity:com.bei.rag.service.SearchResult", "Companion:com.bei.rag.service.KnowledgeBaseManager", "documentId:com.bei.rag.service.SearchResult", "vectorizedDocuments:com.bei.rag.service.KnowledgeBaseStats", "unprocessedDocuments:com.bei.rag.service.KnowledgeBaseStats", "syncFromCloud:com.bei.rag.service.KnowledgeBaseManager", "fileName:com.bei.rag.service.SearchResult", "processingRate:com.bei.rag.service.KnowledgeBaseStats", "KnowledgeBaseManager:com.bei.rag.service", "SearchResult:com.bei.rag.service", "chunkCount:com.bei.rag.service.ProcessingResult", "processingTime:com.bei.rag.service.ProcessingResult", "documentId:com.bei.rag.service.ProcessingResult", "deleteDocument:com.bei.rag.service.KnowledgeBaseManager", "ProcessingResult:com.bei.rag.service", "searchKnowledgeBase:com.bei.rag.service.KnowledgeBaseManager", "processDocument:com.bei.rag.service.KnowledgeBaseManager", "KnowledgeBaseStats:com.bei.rag.service", "<init>:com.bei.rag.service.KnowledgeBaseManager.Companion", "metadata:com.bei.rag.service.SearchResult", "syncToCloud:com.bei.rag.service.KnowledgeBaseManager", "processUnprocessedDocuments:com.bei.rag.service.KnowledgeBaseManager", "getKnowledgeBaseStats:com.bei.rag.service.KnowledgeBaseManager", "reprocessDocument:com.bei.rag.service.KnowledgeBaseManager", "fileName:com.bei.rag.service.ProcessingResult", "getSyncStatus:com.bei.rag.service.KnowledgeBaseManager"], "src\\main\\java\\com\\bei\\rag\\fragment\\ConversationGroupsFragment.kt": ["onViewCreated:com.bei.rag.fragment.ConversationGroupsFragment", "onCreateView:com.bei.rag.fragment.ConversationGroupsFragment", "<init>:com.bei.rag.fragment.ConversationGroupsFragment", "<init>:com.bei.rag.fragment.ConversationGroupsFragment.Companion", "Companion:com.bei.rag.fragment.ConversationGroupsFragment", "newInstance:com.bei.rag.fragment.ConversationGroupsFragment.Companion", "ConversationGroupsFragment:com.bei.rag.fragment"], "src\\main\\java\\com\\bei\\rag\\utils\\StatusBarUtil.java": ["<init>:com.bei.rag.utils.StatusBarUtil", "setLightStatusBar:com.bei.rag.utils.StatusBarUtil", "setDarkStatusBar:com.bei.rag.utils.StatusBarUtil", "addStatusBarMargin:com.bei.rag.utils.StatusBarUtil", "getStatusBarHeight:com.bei.rag.utils.StatusBarUtil", "setFullScreen:com.bei.rag.utils.StatusBarUtil", "StatusBarUtil:com.bei.rag.utils", "addStatusBarPadding:com.bei.rag.utils.StatusBarUtil", "setImmersiveStatusBar:com.bei.rag.utils.StatusBarUtil", "setStatusBarColor:com.bei.rag.utils.StatusBarUtil"], "src\\main\\java\\com\\bei\\rag\\adapter\\ConversationAdapter.kt": ["ConversationAdapter:com.bei.rag.adapter", "onCreateViewHolder:com.bei.rag.adapter.ConversationAdapter", "getItemCount:com.bei.rag.adapter.ConversationAdapter", "bind:com.bei.rag.adapter.ConversationAdapter.ConversationViewHolder", "updateConversations:com.bei.rag.adapter.ConversationAdapter", "onBindViewHolder:com.bei.rag.adapter.ConversationAdapter", "ConversationViewHolder:com.bei.rag.adapter.ConversationAdapter"], "src\\main\\java\\com\\bei\\rag\\fragment\\ProfileFragment.kt": ["newInstance:com.bei.rag.fragment.ProfileFragment.Companion", "onViewCreated:com.bei.rag.fragment.ProfileFragment", "<init>:com.bei.rag.fragment.ProfileFragment.Companion", "onCreateView:com.bei.rag.fragment.ProfileFragment", "ProfileFragment:com.bei.rag.fragment", "Companion:com.bei.rag.fragment.ProfileFragment", "<init>:com.bei.rag.fragment.ProfileFragment"], "src\\main\\java\\com\\bei\\rag\\database\\dao\\UserDao.kt": ["updateAvatar:com.bei.rag.database.dao.UserDao", "getUserInfoSync:com.bei.rag.database.dao.UserDao", "updateLastLoginTime:com.bei.rag.database.dao.UserDao", "getUserInfo:com.bei.rag.database.dao.UserDao", "insertOrUpdateUser:com.bei.rag.database.dao.UserDao", "updateNickname:com.bei.rag.database.dao.UserDao", "updateUser:com.bei.rag.database.dao.UserDao", "UserDao:com.bei.rag.database.dao", "deleteUser:com.bei.rag.database.dao.UserDao", "clearUserInfo:com.bei.rag.database.dao.UserDao", "updateEmail:com.bei.rag.database.dao.UserDao"], "src\\main\\java\\com\\bei\\rag\\utils\\StatusBarTestHelper.kt": ["<init>:com.bei.rag.utils.StatusBarTestHelper.Companion", "details:com.bei.rag.utils.StatusBarTestHelper.TestResult", "message:com.bei.rag.utils.StatusBarTestHelper.TestResult", "StatusBarTestHelper:com.bei.rag.utils", "success:com.bei.rag.utils.StatusBarTestHelper.TestResult", "TestResult:com.bei.rag.utils.StatusBarTestHelper", "testName:com.bei.rag.utils.StatusBarTestHelper.TestResult", "generateCompatibilityReport:com.bei.rag.utils.StatusBarTestHelper", "generateTestReport:com.bei.rag.utils.StatusBarTestHelper", "quickHealthCheck:com.bei.rag.utils.StatusBarTestHelper", "runFullTestSuite:com.bei.rag.utils.StatusBarTestHelper", "Companion:com.bei.rag.utils.StatusBarTestHelper", "testFragmentStatusBar:com.bei.rag.utils.StatusBarTestHelper"], "src\\main\\java\\com\\bei\\rag\\utils\\StatusBarCompatManager.kt": ["<init>:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy.NEW_INSETS_CONTROLLER", "applyStyleByStrategy:com.bei.rag.utils.StatusBarCompatManager", "isStatusBarTextColorSupported:com.bei.rag.utils.StatusBarCompatManager", "getStatusBarHeight:com.bei.rag.utils.StatusBarCompatManager", "LEGACY:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy", "setEdgeToEdge:com.bei.rag.utils.StatusBarCompatManager", "<init>:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy.DIRECT_COLOR", "<init>:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy.TRANSLUCENT", "DIRECT_COLOR:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy", "<init>:com.bei.rag.utils.StatusBarCompatManager.Companion", "setImmersiveStatusBar:com.bei.rag.utils.StatusBarCompatManager", "isStatusBarColorSupported:com.bei.rag.utils.StatusBarCompatManager", "Companion:com.bei.rag.utils.StatusBarCompatManager", "NEW_INSETS_CONTROLLER:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy", "StatusBarCompatManager:com.bei.rag.utils", "applyCompatStatusBarStyle:com.bei.rag.utils.StatusBarCompatManager", "setStatusBarColor:com.bei.rag.utils.StatusBarCompatManager", "isNewInsetsControllerSupported:com.bei.rag.utils.StatusBarCompatManager", "resetToSystemDefault:com.bei.rag.utils.StatusBarCompatManager", "setStatusBarContentColor:com.bei.rag.utils.StatusBarCompatManager", "showStatusBar:com.bei.rag.utils.StatusBarCompatManager", "<init>:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy.INSETS_CONTROLLER_COMPAT", "getStatusBarStrategy:com.bei.rag.utils.StatusBarCompatManager", "<init>:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy.MODERN_API", "<init>:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy.LEGACY", "INSETS_CONTROLLER_COMPAT:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy", "StatusBarStrategy:com.bei.rag.utils.StatusBarCompatManager", "MODERN_API:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy", "TRANSLUCENT:com.bei.rag.utils.StatusBarCompatManager.StatusBarStrategy"], "src\\main\\java\\com\\bei\\rag\\adapter\\ConversationGroupAdapter.kt": ["onCreateViewHolder:com.bei.rag.adapter.ConversationGroupAdapter", "updateGroups:com.bei.rag.adapter.ConversationGroupAdapter", "GroupViewHolder:com.bei.rag.adapter.ConversationGroupAdapter", "ConversationGroupAdapter:com.bei.rag.adapter", "bind:com.bei.rag.adapter.ConversationGroupAdapter.GroupViewHolder", "onBindViewHolder:com.bei.rag.adapter.ConversationGroupAdapter", "getItemCount:com.bei.rag.adapter.ConversationGroupAdapter"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\bei\\rag\\database\\dao\\UserDao_Impl.java": ["updateNickname:com.bei.rag.database.dao.UserDao_Impl", "updateLastLoginTime:com.bei.rag.database.dao.UserDao_Impl", "<init>:com.bei.rag.database.dao.UserDao_Impl", "deleteUser:com.bei.rag.database.dao.UserDao_Impl", "getRequiredConverters:com.bei.rag.database.dao.UserDao_Impl", "insertOrUpdateUser:com.bei.rag.database.dao.UserDao_Impl", "getUserInfoSync:com.bei.rag.database.dao.UserDao_Impl", "getUserInfo:com.bei.rag.database.dao.UserDao_Impl", "clearUserInfo:com.bei.rag.database.dao.UserDao_Impl", "updateUser:com.bei.rag.database.dao.UserDao_Impl", "updateEmail:com.bei.rag.database.dao.UserDao_Impl", "UserDao_Impl:com.bei.rag.database.dao", "updateAvatar:com.bei.rag.database.dao.UserDao_Impl"], "src\\main\\java\\com\\bei\\rag\\utils\\ApiConfig.kt": ["isApiKeyConfigured:com.bei.rag.utils.ApiConfig", "getSiliconFlowApiKey:com.bei.rag.utils.ApiConfig", "ApiConfig:com.bei.rag.utils", "OPENROUTER_BASE_URL:com.bei.rag.utils.ApiConfig", "SILICONFLOW_BASE_URL:com.bei.rag.utils.ApiConfig", "getGeminiModel:com.bei.rag.utils.ApiConfig", "<init>:com.bei.rag.utils.ApiConfig", "getOpenRouterApiKey:com.bei.rag.utils.ApiConfig", "isSiliconFlowApiKeyConfigured:com.bei.rag.utils.ApiConfig"], "src\\main\\java\\com\\bei\\rag\\service\\SiliconFlowEmbeddingService.kt": ["SiliconFlowEmbeddingService:com.bei.rag.service", "generateEmbedding:com.bei.rag.service.SiliconFlowEmbeddingService", "<init>:com.bei.rag.service.SiliconFlowEmbeddingService", "batchGenerateEmbeddings:com.bei.rag.service.SiliconFlowEmbeddingService", "calculateCosineSimilarity:com.bei.rag.service.SiliconFlowEmbeddingService", "isServiceAvailable:com.bei.rag.service.SiliconFlowEmbeddingService"], "src\\main\\java\\com\\bei\\rag\\service\\tts\\AndroidTtsEngine.kt": ["initialize:com.bei.rag.service.tts.AndroidTtsEngine", "cleanup:com.bei.rag.service.tts.AndroidTtsEngine", "stopSpeaking:com.bei.rag.service.tts.AndroidTtsEngine", "setLanguage:com.bei.rag.service.tts.AndroidTtsEngine", "<init>:com.bei.rag.service.tts.AndroidTtsEngine.Companion", "isAvailable:com.bei.rag.service.tts.AndroidTtsEngine", "speakText:com.bei.rag.service.tts.AndroidTtsEngine", "setSpeechRate:com.bei.rag.service.tts.AndroidTtsEngine", "setPitch:com.bei.rag.service.tts.AndroidTtsEngine", "Companion:com.bei.rag.service.tts.AndroidTtsEngine", "getEngineType:com.bei.rag.service.tts.AndroidTtsEngine", "AndroidTtsEngine:com.bei.rag.service.tts", "isSpeaking:com.bei.rag.service.tts.AndroidTtsEngine", "onInit:com.bei.rag.service.tts.AndroidTtsEngine"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\bei\\rag\\database\\dao\\ChatMessageDao_Impl.java": ["insertMessage:com.bei.rag.database.dao.ChatMessageDao_Impl", "deleteAllMessages:com.bei.rag.database.dao.ChatMessageDao_Impl", "updateConversationGroup:com.bei.rag.database.dao.ChatMessageDao_Impl", "getConversationIdsByGroup:com.bei.rag.database.dao.ChatMessageDao_Impl", "searchMessages:com.bei.rag.database.dao.ChatMessageDao_Impl", "getConversationCountByGroup:com.bei.rag.database.dao.ChatMessageDao_Impl", "updateGroupIdForConversations:com.bei.rag.database.dao.ChatMessageDao_Impl", "getMessagesByConversation:com.bei.rag.database.dao.ChatMessageDao_Impl", "getAllMessages:com.bei.rag.database.dao.ChatMessageDao_Impl", "getMessageCount:com.bei.rag.database.dao.ChatMessageDao_Impl", "deleteMessage:com.bei.rag.database.dao.ChatMessageDao_Impl", "getAllConversationIds:com.bei.rag.database.dao.ChatMessageDao_Impl", "ChatMessageDao_Impl:com.bei.rag.database.dao", "getRequiredConverters:com.bei.rag.database.dao.ChatMessageDao_Impl", "deleteConversation:com.bei.rag.database.dao.ChatMessageDao_Impl", "<init>:com.bei.rag.database.dao.ChatMessageDao_Impl"], "src\\main\\java\\com\\bei\\rag\\utils\\StatusBarAnimationManager.kt": ["cancelAllAnimations:com.bei.rag.utils.StatusBarAnimationManager", "cleanup:com.bei.rag.utils.StatusBarAnimationManager", "isAnimating:com.bei.rag.utils.StatusBarAnimationManager", "animateWithBounceEffect:com.bei.rag.utils.StatusBarAnimationManager", "finishAllAnimations:com.bei.rag.utils.StatusBarAnimationManager", "animateGradientOverlayTransition:com.bei.rag.utils.StatusBarAnimationManager", "onAnimationStart:com.bei.rag.utils.StatusBarAnimationManager.AnimationListener", "animateThemeColorChange:com.bei.rag.utils.StatusBarAnimationManager", "setAnimationListener:com.bei.rag.utils.StatusBarAnimationManager", "onAnimationCancel:com.bei.rag.utils.StatusBarAnimationManager.AnimationListener", "<init>:com.bei.rag.utils.StatusBarAnimationManager.Companion", "AnimationListener:com.bei.rag.utils.StatusBarAnimationManager", "onAnimationEnd:com.bei.rag.utils.StatusBarAnimationManager.AnimationListener", "animateWithPulseEffect:com.bei.rag.utils.StatusBarAnimationManager", "animateOverlayTransition:com.bei.rag.utils.StatusBarAnimationManager", "StatusBarAnimationManager:com.bei.rag.utils", "animateModeSwitch:com.bei.rag.utils.StatusBarAnimationManager", "Companion:com.bei.rag.utils.StatusBarAnimationManager"], "src\\main\\java\\com\\bei\\rag\\utils\\StatusBarManager.kt": ["animateThemeTransitionWithPulse:com.bei.rag.utils.StatusBarManager", "StatusBarManager:com.bei.rag.utils", "cancelAnimations:com.bei.rag.utils.StatusBarManager", "<init>:com.bei.rag.utils.StatusBarManager.Companion", "setTransparentStatusBar:com.bei.rag.utils.StatusBarManager", "animateThemeTransition:com.bei.rag.utils.StatusBarManager", "exitImmersiveMode:com.bei.rag.utils.StatusBarManager", "isStatusBarCustomizationSupported:com.bei.rag.utils.StatusBarManager", "autoAdjustForBackground:com.bei.rag.utils.StatusBarManager", "cleanup:com.bei.rag.utils.StatusBarManager", "setLightStatusBarContent:com.bei.rag.utils.StatusBarManager", "animateThemeTransitionWithBounce:com.bei.rag.utils.StatusBarManager", "setCustomStatusBarForPage:com.bei.rag.utils.StatusBarManager", "initialize:com.bei.rag.utils.StatusBarManager", "setAnimationListener:com.bei.rag.utils.StatusBarManager", "getThemeStatusBarColor:com.bei.rag.utils.StatusBarManager", "finishAnimations:com.bei.rag.utils.StatusBarManager", "getStatusBarHeight:com.bei.rag.utils.StatusBarManager", "getStatusBarStrategy:com.bei.rag.utils.StatusBarManager", "resetToDefault:com.bei.rag.utils.StatusBarManager", "isStatusBarTextColorSupported:com.bei.rag.utils.StatusBarManager", "animateModeTransition:com.bei.rag.utils.StatusBarManager", "setImmersiveMode:com.bei.rag.utils.StatusBarManager", "applyThemeBasedStatusBar:com.bei.rag.utils.StatusBarManager", "setStatusBarColor:com.bei.rag.utils.StatusBarManager", "isAnimating:com.bei.rag.utils.StatusBarManager", "Companion:com.bei.rag.utils.StatusBarManager"], "src\\main\\java\\com\\bei\\rag\\utils\\TtsConfig.kt": ["setPitch:com.bei.rag.utils.TtsConfig", "resetToDefault:com.bei.rag.utils.TtsConfig", "setEngineType:com.bei.rag.utils.TtsConfig", "TtsConfig:com.bei.rag.utils", "Companion:com.bei.rag.utils.TtsConfig", "getLanguage:com.bei.rag.utils.TtsConfig", "setSpeechRate:com.bei.rag.utils.TtsConfig", "<init>:com.bei.rag.utils.TtsConfig.Companion", "getSpeechRate:com.bei.rag.utils.TtsConfig", "getAutoPlayAiReply:com.bei.rag.utils.TtsConfig", "setLanguage:com.bei.rag.utils.TtsConfig", "getPitch:com.bei.rag.utils.TtsConfig", "getEngineType:com.bei.rag.utils.TtsConfig", "setAutoPlayAiReply:com.bei.rag.utils.TtsConfig"], "src\\main\\java\\com\\bei\\rag\\service\\SemanticChunker.kt": ["<init>:com.bei.rag.service.SemanticChunker", "Companion:com.bei.rag.service.SemanticChunker", "SemanticChunker:com.bei.rag.service", "optimizeChunks:com.bei.rag.service.SemanticChunker", "chunkDocument:com.bei.rag.service.SemanticChunker", "<init>:com.bei.rag.service.SemanticChunker.Companion"], "src\\main\\java\\com\\bei\\rag\\fragment\\ChatFragment.kt": ["ChatFragment:com.bei.rag.fragment", "onViewCreated:com.bei.rag.fragment.ChatFragment", "onCreateView:com.bei.rag.fragment.ChatFragment", "newInstance:com.bei.rag.fragment.ChatFragment.Companion", "<init>:com.bei.rag.fragment.ChatFragment.Companion", "<init>:com.bei.rag.fragment.ChatFragment", "onDestroyView:com.bei.rag.fragment.ChatFragment", "Companion:com.bei.rag.fragment.ChatFragment"], "src\\main\\java\\com\\bei\\rag\\service\\SiliconFlowApiService.kt": ["transcribeAudio:com.bei.rag.service.SiliconFlowApiService", "SiliconFlowApiService:com.bei.rag.service"], "src\\main\\java\\com\\bei\\rag\\database\\entity\\ChatMessageEntity.kt": ["sources:com.bei.rag.database.entity.ChatMessageEntity", "conversationId:com.bei.rag.database.entity.ChatMessageEntity", "timestamp:com.bei.rag.database.entity.ChatMessageEntity", "ChatMessageEntity:com.bei.rag.database.entity", "id:com.bei.rag.database.entity.ChatMessageEntity", "isUser:com.bei.rag.database.entity.ChatMessageEntity", "content:com.bei.rag.database.entity.ChatMessageEntity", "groupId:com.bei.rag.database.entity.ChatMessageEntity"], "src\\main\\java\\com\\bei\\rag\\repository\\DocumentRepository.kt": ["getVectorizedDocumentCount:com.bei.rag.repository.DocumentRepository", "getDocumentsByType:com.bei.rag.repository.DocumentRepository", "updateVectorizedStatus:com.bei.rag.repository.DocumentRepository", "getAllDocuments:com.bei.rag.repository.DocumentRepository", "getDocumentById:com.bei.rag.repository.DocumentRepository", "updateExtractedText:com.bei.rag.repository.DocumentRepository", "searchDocuments:com.bei.rag.repository.DocumentRepository", "updateProcessingError:com.bei.rag.repository.DocumentRepository", "getDocumentCount:com.bei.rag.repository.DocumentRepository", "getUnprocessedDocuments:com.bei.rag.repository.DocumentRepository", "updateProcessingStatus:com.bei.rag.repository.DocumentRepository", "deleteDocument:com.bei.rag.repository.DocumentRepository", "insertDocument:com.bei.rag.repository.DocumentRepository", "deleteDocumentById:com.bei.rag.repository.DocumentRepository", "DocumentRepository:com.bei.rag.repository", "getTotalFileSize:com.bei.rag.repository.DocumentRepository", "getVectorizedDocuments:com.bei.rag.repository.DocumentRepository", "updateDocument:com.bei.rag.repository.DocumentRepository"], "src\\main\\java\\com\\bei\\rag\\fragment\\ConversationListFragment.kt": ["<init>:com.bei.rag.fragment.ConversationListFragment", "newInstance:com.bei.rag.fragment.ConversationListFragment.Companion", "onViewCreated:com.bei.rag.fragment.ConversationListFragment", "ConversationListFragment:com.bei.rag.fragment", "onCreateView:com.bei.rag.fragment.ConversationListFragment", "<init>:com.bei.rag.fragment.ConversationListFragment.Companion", "Companion:com.bei.rag.fragment.ConversationListFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\bei\\rag\\database\\dao\\ConversationGroupDao_Impl.java": ["<init>:com.bei.rag.database.dao.ConversationGroupDao_Impl", "updateGroup:com.bei.rag.database.dao.ConversationGroupDao_Impl", "insertGroup:com.bei.rag.database.dao.ConversationGroupDao_Impl", "getGroupById:com.bei.rag.database.dao.ConversationGroupDao_Impl", "getGroupCount:com.bei.rag.database.dao.ConversationGroupDao_Impl", "deleteGroupById:com.bei.rag.database.dao.ConversationGroupDao_Impl", "getRequiredConverters:com.bei.rag.database.dao.ConversationGroupDao_Impl", "deleteGroup:com.bei.rag.database.dao.ConversationGroupDao_Impl", "ConversationGroupDao_Impl:com.bei.rag.database.dao", "getAllGroups:com.bei.rag.database.dao.ConversationGroupDao_Impl"], "src\\main\\java\\com\\bei\\rag\\database\\entity\\UserEntity.kt": ["email:com.bei.rag.database.entity.UserEntity", "UserEntity:com.bei.rag.database.entity", "id:com.bei.rag.database.entity.UserEntity", "nickname:com.bei.rag.database.entity.UserEntity", "lastLoginTime:com.bei.rag.database.entity.UserEntity", "avatar:com.bei.rag.database.entity.UserEntity", "createdTime:com.bei.rag.database.entity.UserEntity"], "src\\main\\java\\com\\bei\\rag\\utils\\StatusBarCompatibilityValidator.kt": ["brand:com.bei.rag.utils.StatusBarCompatibilityValidator.DeviceCompatibilityInfo", "issues:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityResult", "NO_SUPPORT:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityLevel", "generateCompatibilityReport:com.bei.rag.utils.StatusBarCompatibilityValidator", "CompatibilityResult:com.bei.rag.utils.StatusBarCompatibilityValidator", "manufacturer:com.bei.rag.utils.StatusBarCompatibilityValidator.DeviceCompatibilityInfo", "androidVersion:com.bei.rag.utils.StatusBarCompatibilityValidator.DeviceCompatibilityInfo", "isCompatible:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityResult", "Companion:com.bei.rag.utils.StatusBarCompatibilityValidator", "deviceInfo:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityResult", "<init>:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityLevel.LIMITED_SUPPORT", "<init>:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityLevel.PARTIAL_SUPPORT", "<init>:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityLevel.NO_SUPPORT", "StatusBarCompatibilityValidator:com.bei.rag.utils", "<init>:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityLevel.FULL_SUPPORT", "DeviceCompatibilityInfo:com.bei.rag.utils.StatusBarCompatibilityValidator", "apiLevel:com.bei.rag.utils.StatusBarCompatibilityValidator.DeviceCompatibilityInfo", "limitations:com.bei.rag.utils.StatusBarCompatibilityValidator.DeviceCompatibilityInfo", "<init>:com.bei.rag.utils.StatusBarCompatibilityValidator.Companion", "PARTIAL_SUPPORT:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityLevel", "deviceModel:com.bei.rag.utils.StatusBarCompatibilityValidator.DeviceCompatibilityInfo", "LIMITED_SUPPORT:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityLevel", "compatibilityLevel:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityResult", "recommendations:com.bei.rag.utils.StatusBarCompatibilityValidator.DeviceCompatibilityInfo", "supportedFeatures:com.bei.rag.utils.StatusBarCompatibilityValidator.DeviceCompatibilityInfo", "CompatibilityLevel:com.bei.rag.utils.StatusBarCompatibilityValidator", "validateCompatibility:com.bei.rag.utils.StatusBarCompatibilityValidator", "FULL_SUPPORT:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityLevel", "solutions:com.bei.rag.utils.StatusBarCompatibilityValidator.CompatibilityResult"], "src\\main\\java\\com\\bei\\rag\\fragment\\SystemSettingsFragment.kt": ["SystemSettingsFragment:com.bei.rag.fragment", "onViewCreated:com.bei.rag.fragment.SystemSettingsFragment", "newInstance:com.bei.rag.fragment.SystemSettingsFragment.Companion", "Companion:com.bei.rag.fragment.SystemSettingsFragment", "<init>:com.bei.rag.fragment.SystemSettingsFragment", "onCreateView:com.bei.rag.fragment.SystemSettingsFragment", "<init>:com.bei.rag.fragment.SystemSettingsFragment.Companion"], "src\\main\\java\\com\\bei\\rag\\database\\dao\\ChatMessageDao.kt": ["getMessagesByConversation:com.bei.rag.database.dao.ChatMessageDao", "ChatMessageDao:com.bei.rag.database.dao", "deleteMessage:com.bei.rag.database.dao.ChatMessageDao", "getMessageCount:com.bei.rag.database.dao.ChatMessageDao", "deleteAllMessages:com.bei.rag.database.dao.ChatMessageDao", "updateGroupIdForConversations:com.bei.rag.database.dao.ChatMessageDao", "updateConversationGroup:com.bei.rag.database.dao.ChatMessageDao", "getAllConversationIds:com.bei.rag.database.dao.ChatMessageDao", "getConversationCountByGroup:com.bei.rag.database.dao.ChatMessageDao", "deleteConversation:com.bei.rag.database.dao.ChatMessageDao", "getAllMessages:com.bei.rag.database.dao.ChatMessageDao", "insertMessage:com.bei.rag.database.dao.ChatMessageDao", "searchMessages:com.bei.rag.database.dao.ChatMessageDao", "getConversationIdsByGroup:com.bei.rag.database.dao.ChatMessageDao"], "src\\main\\java\\com\\bei\\rag\\ChatScreen.kt": ["ChatScreen:com.bei.rag", "onCreate:com.bei.rag.ChatScreen", "<init>:com.bei.rag.ChatScreen"], "src\\main\\java\\com\\bei\\rag\\repository\\UserRepository.kt": ["updateUser:com.bei.rag.repository.UserRepository", "resetToDefault:com.bei.rag.repository.UserRepository", "clearUserInfo:com.bei.rag.repository.UserRepository", "deleteUser:com.bei.rag.repository.UserRepository", "UserRepository:com.bei.rag.repository", "updateEmail:com.bei.rag.repository.UserRepository", "getUserInfo:com.bei.rag.repository.UserRepository", "updateNickname:com.bei.rag.repository.UserRepository", "updateLastLoginTime:com.bei.rag.repository.UserRepository", "getUserInfoSync:com.bei.rag.repository.UserRepository", "updateAvatar:com.bei.rag.repository.UserRepository", "insertOrUpdateUser:com.bei.rag.repository.UserRepository"], "src\\main\\java\\com\\bei\\rag\\adapter\\ChatAdapter.kt": ["ChatAdapter:com.bei.rag.adapter", "onCreateViewHolder:com.bei.rag.adapter.ChatAdapter", "UserMessageViewHolder:com.bei.rag.adapter.ChatAdapter", "updateMessagePlayingState:com.bei.rag.adapter.ChatAdapter", "getItemViewType:com.bei.rag.adapter.ChatAdapter", "onBindViewHolder:com.bei.rag.adapter.ChatAdapter", "removeLastMessage:com.bei.rag.adapter.ChatAdapter", "bind:com.bei.rag.adapter.ChatAdapter.UserMessageViewHolder", "bind:com.bei.rag.adapter.ChatAdapter.AiMessageViewHolder", "updateMessages:com.bei.rag.adapter.ChatAdapter", "updateLastMessage:com.bei.rag.adapter.ChatAdapter", "Companion:com.bei.rag.adapter.ChatAdapter", "getItemCount:com.bei.rag.adapter.ChatAdapter", "<init>:com.bei.rag.adapter.ChatAdapter.Companion", "addMessage:com.bei.rag.adapter.ChatAdapter", "AiMessageViewHolder:com.bei.rag.adapter.ChatAdapter", "getUserMessageBefore:com.bei.rag.adapter.ChatAdapter", "getMessageIndex:com.bei.rag.adapter.ChatAdapter", "stopAllAudioPlaying:com.bei.rag.adapter.ChatAdapter"], "src\\main\\java\\com\\bei\\rag\\service\\VoiceToTextService.kt": ["cancelRecording:com.bei.rag.service.VoiceToTextService", "startRecording:com.bei.rag.service.VoiceToTextService", "stopRecordingAndTranscribe:com.bei.rag.service.VoiceToTextService", "VoiceToTextService:com.bei.rag.service"], "src\\main\\java\\com\\bei\\rag\\service\\OpenRouterApiService.kt": ["sendRawMessage:com.bei.rag.service.OpenRouterApiService", "OpenRouterApiService:com.bei.rag.service", "<init>:com.bei.rag.service.OpenRouterApiService", "sendMessage:com.bei.rag.service.OpenRouterApiService"], "src\\main\\java\\com\\bei\\rag\\model\\ConversationGroup.kt": ["ConversationGroup:com.bei.rag.model", "name:com.bei.rag.model.ConversationGroup", "createdTime:com.bei.rag.model.ConversationGroup", "description:com.bei.rag.model.ConversationGroup", "id:com.bei.rag.model.ConversationGroup", "conversationCount:com.bei.rag.model.ConversationGroup"], "src\\main\\java\\com\\bei\\rag\\database\\dao\\ConversationGroupDao.kt": ["insertGroup:com.bei.rag.database.dao.ConversationGroupDao", "deleteGroup:com.bei.rag.database.dao.ConversationGroupDao", "updateGroup:com.bei.rag.database.dao.ConversationGroupDao", "deleteGroupById:com.bei.rag.database.dao.ConversationGroupDao", "getAllGroups:com.bei.rag.database.dao.ConversationGroupDao", "getGroupById:com.bei.rag.database.dao.ConversationGroupDao", "getGroupCount:com.bei.rag.database.dao.ConversationGroupDao", "ConversationGroupDao:com.bei.rag.database.dao"], "src\\main\\java\\com\\bei\\rag\\utils\\KeyboardUtils.kt": ["onKeyboardShow:com.bei.rag.utils.KeyboardUtils.KeyboardListener", "onKeyboardHide:com.bei.rag.utils.KeyboardUtils.KeyboardListener", "KeyboardListener:com.bei.rag.utils.KeyboardUtils", "setupKeyboardListener:com.bei.rag.utils.KeyboardUtils", "setAdjustPan:com.bei.rag.utils.KeyboardUtils", "KeyboardUtils:com.bei.rag.utils", "setAdjustResize:com.bei.rag.utils.KeyboardUtils", "<init>:com.bei.rag.utils.KeyboardUtils"], "src\\main\\java\\com\\bei\\rag\\model\\ChatMessage.kt": ["isUser:com.bei.rag.model.ChatMessage", "timestamp:com.bei.rag.model.ChatMessage", "isPlaying:com.bei.rag.model.ChatMessage", "sources:com.bei.rag.model.ChatMessage", "id:com.bei.rag.model.ChatMessage", "content:com.bei.rag.model.ChatMessage", "ChatMessage:com.bei.rag.model"], "src\\main\\java\\com\\bei\\rag\\service\\tts\\GeminiTtsEngine.kt": ["isSpeaking:com.bei.rag.service.tts.GeminiTtsEngine", "<init>:com.bei.rag.service.tts.GeminiTtsEngine.Companion", "setSpeechRate:com.bei.rag.service.tts.GeminiTtsEngine", "Companion:com.bei.rag.service.tts.GeminiTtsEngine", "isAvailable:com.bei.rag.service.tts.GeminiTtsEngine", "GeminiTtsEngine:com.bei.rag.service.tts", "stopSpeaking:com.bei.rag.service.tts.GeminiTtsEngine", "setLanguage:com.bei.rag.service.tts.GeminiTtsEngine", "initialize:com.bei.rag.service.tts.GeminiTtsEngine", "cleanup:com.bei.rag.service.tts.GeminiTtsEngine", "getEngineType:com.bei.rag.service.tts.GeminiTtsEngine", "speakText:com.bei.rag.service.tts.GeminiTtsEngine", "setPitch:com.bei.rag.service.tts.GeminiTtsEngine"], "src\\main\\java\\com\\bei\\rag\\model\\Conversation.kt": ["title:com.bei.rag.model.Conversation", "messageCount:com.bei.rag.model.Conversation", "id:com.bei.rag.model.Conversation", "lastMessage:com.bei.rag.model.Conversation", "lastMessageTime:com.bei.rag.model.Conversation", "Conversation:com.bei.rag.model"], "src\\main\\java\\com\\bei\\rag\\fragment\\KnowledgeFragment.kt": ["Companion:com.bei.rag.fragment.KnowledgeFragment", "onCreateView:com.bei.rag.fragment.KnowledgeFragment", "newInstance:com.bei.rag.fragment.KnowledgeFragment.Companion", "<init>:com.bei.rag.fragment.KnowledgeFragment", "onViewCreated:com.bei.rag.fragment.KnowledgeFragment", "KnowledgeFragment:com.bei.rag.fragment", "<init>:com.bei.rag.fragment.KnowledgeFragment.Companion"], "src\\main\\java\\com\\bei\\rag\\database\\entity\\ConversationGroupEntity.kt": ["name:com.bei.rag.database.entity.ConversationGroupEntity", "id:com.bei.rag.database.entity.ConversationGroupEntity", "ConversationGroupEntity:com.bei.rag.database.entity", "createdTime:com.bei.rag.database.entity.ConversationGroupEntity", "description:com.bei.rag.database.entity.ConversationGroupEntity"], "build\\generated\\source\\buildConfig\\debug\\com\\bei\\rag\\BuildConfig.java": ["GEMINI_MODEL:com.bei.rag.BuildConfig", "BUILD_TYPE:com.bei.rag.BuildConfig", "<init>:com.bei.rag.BuildConfig", "GEMINI_TTS_API_KEY:com.bei.rag.BuildConfig", "VERSION_NAME:com.bei.rag.BuildConfig", "OPENROUTER_API_KEY:com.bei.rag.BuildConfig", "SILICONFLOW_API_KEY:com.bei.rag.BuildConfig", "SUPABASE_URL:com.bei.rag.BuildConfig", "APPLICATION_ID:com.bei.rag.BuildConfig", "VERSION_CODE:com.bei.rag.BuildConfig", "DEBUG:com.bei.rag.BuildConfig", "SUPABASE_ANON_KEY:com.bei.rag.BuildConfig", "BuildConfig:com.bei.rag"], "src\\main\\java\\com\\bei\\rag\\fragment\\ThemeSettingsFragment.kt": ["onResume:com.bei.rag.fragment.ThemeSettingsFragment", "onCreateView:com.bei.rag.fragment.ThemeSettingsFragment", "onViewCreated:com.bei.rag.fragment.ThemeSettingsFragment", "<init>:com.bei.rag.fragment.ThemeSettingsFragment", "ThemeSettingsFragment:com.bei.rag.fragment", "<init>:com.bei.rag.fragment.ThemeSettingsFragment.Companion", "Companion:com.bei.rag.fragment.ThemeSettingsFragment", "newInstance:com.bei.rag.fragment.ThemeSettingsFragment.Companion"], "src\\main\\java\\com\\bei\\rag\\adapter\\NavGroupAdapter.kt": ["updateGroups:com.bei.rag.adapter.NavGroupAdapter", "getItemCount:com.bei.rag.adapter.NavGroupAdapter", "onBindViewHolder:com.bei.rag.adapter.NavGroupAdapter", "NavGroupAdapter:com.bei.rag.adapter", "onCreateViewHolder:com.bei.rag.adapter.NavGroupAdapter", "NavGroupViewHolder:com.bei.rag.adapter.NavGroupAdapter", "bind:com.bei.rag.adapter.NavGroupAdapter.NavGroupViewHolder"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\bei\\rag\\database\\AppDatabase_Impl.java": ["clearAllTables:com.bei.rag.database.AppDatabase_Impl", "getRequiredAutoMigrationSpecs:com.bei.rag.database.AppDatabase_Impl", "AppDatabase_Impl:com.bei.rag.database", "createOpenHelper:com.bei.rag.database.AppDatabase_Impl", "createInvalidationTracker:com.bei.rag.database.AppDatabase_Impl", "documentDao:com.bei.rag.database.AppDatabase_Impl", "getRequiredTypeConverters:com.bei.rag.database.AppDatabase_Impl", "chatMessageDao:com.bei.rag.database.AppDatabase_Impl", "conversationGroupDao:com.bei.rag.database.AppDatabase_Impl", "userDao:com.bei.rag.database.AppDatabase_Impl", "getAutoMigrations:com.bei.rag.database.AppDatabase_Impl", "<init>:com.bei.rag.database.AppDatabase_Impl"], "src\\main\\java\\com\\bei\\rag\\utils\\StatusBarHelper.kt": ["getStatusBarHeightDp:com.bei.rag.utils.StatusBarHelper", "setupImmersiveStatusBar:com.bei.rag.utils.StatusBarHelper", "onThemeChanged:com.bei.rag.utils.StatusBarHelper", "setupKnowledgeStatusBar:com.bei.rag.utils.StatusBarHelper", "StatusBarHelper:com.bei.rag.utils", "setupTransparentStatusBar:com.bei.rag.utils.StatusBarHelper", "<init>:com.bei.rag.utils.StatusBarHelper", "setupStatusBarForFragment:com.bei.rag.utils.StatusBarHelper", "restoreDefaultStatusBar:com.bei.rag.utils.StatusBarHelper", "updateOverlaysOnThemeChange:com.bei.rag.utils.StatusBarHelper", "createStatusBarPlaceholder:com.bei.rag.utils.StatusBarHelper", "isDarkMode:com.bei.rag.utils.StatusBarHelper", "cleanupStatusBarOverlay:com.bei.rag.utils.StatusBarHelper", "setupChatStatusBar:com.bei.rag.utils.StatusBarHelper", "setupDrawerStatusBar:com.bei.rag.utils.StatusBarHelper"], "src\\main\\java\\com\\bei\\rag\\fragment\\SettingsFragment.kt": ["SettingsFragment:com.bei.rag.fragment", "<init>:com.bei.rag.fragment.SettingsFragment", "onViewCreated:com.bei.rag.fragment.SettingsFragment", "onCreateView:com.bei.rag.fragment.SettingsFragment", "newInstance:com.bei.rag.fragment.SettingsFragment.Companion", "Companion:com.bei.rag.fragment.SettingsFragment", "<init>:com.bei.rag.fragment.SettingsFragment.Companion"], "src\\main\\java\\com\\bei\\rag\\database\\AppDatabase.kt": ["<init>:com.bei.rag.database.AppDatabase.Companion.DatabaseCallback", "<init>:com.bei.rag.database.AppDatabase", "AppDatabase:com.bei.rag.database", "userDao:com.bei.rag.database.AppDatabase", "<init>:com.bei.rag.database.AppDatabase.Companion", "documentDao:com.bei.rag.database.AppDatabase", "Companion:com.bei.rag.database.AppDatabase", "onCreate:com.bei.rag.database.AppDatabase.Companion.DatabaseCallback", "conversationGroupDao:com.bei.rag.database.AppDatabase", "chatMessageDao:com.bei.rag.database.AppDatabase", "getDatabase:com.bei.rag.database.AppDatabase.Companion"], "src\\main\\java\\com\\bei\\rag\\utils\\TtsEngineType.kt": ["description:com.bei.rag.utils.TtsEngineType", "GEMINI_TTS:com.bei.rag.utils.TtsEngineType", "ANDROID_TTS:com.bei.rag.utils.TtsEngineType", "displayName:com.bei.rag.utils.TtsEngineType", "<init>:com.bei.rag.utils.TtsEngineType.ANDROID_TTS", "TtsEngineType:com.bei.rag.utils", "<init>:com.bei.rag.utils.TtsEngineType.GEMINI_TTS", "<init>:com.bei.rag.utils.TtsEngineType.Companion", "isOffline:com.bei.rag.utils.TtsEngineType", "getDefault:com.bei.rag.utils.TtsEngineType.Companion", "fromString:com.bei.rag.utils.TtsEngineType.Companion", "Companion:com.bei.rag.utils.TtsEngineType"], "src\\main\\java\\com\\bei\\rag\\fragment\\VoiceEngineSettingsFragment.kt": ["<init>:com.bei.rag.fragment.VoiceEngineSettingsFragment", "onDestroy:com.bei.rag.fragment.VoiceEngineSettingsFragment", "onCreateView:com.bei.rag.fragment.VoiceEngineSettingsFragment", "onViewCreated:com.bei.rag.fragment.VoiceEngineSettingsFragment", "<init>:com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion", "newInstance:com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion", "VoiceEngineSettingsFragment:com.bei.rag.fragment", "Companion:com.bei.rag.fragment.VoiceEngineSettingsFragment"], "src\\main\\java\\com\\bei\\rag\\adapter\\DocumentAdapter.kt": ["DocumentViewHolder:com.bei.rag.adapter.DocumentAdapter", "getItemCount:com.bei.rag.adapter.DocumentAdapter", "DocumentAdapter:com.bei.rag.adapter", "bind:com.bei.rag.adapter.DocumentAdapter.DocumentViewHolder", "onBindViewHolder:com.bei.rag.adapter.DocumentAdapter", "updateDocuments:com.bei.rag.adapter.DocumentAdapter", "onCreateViewHolder:com.bei.rag.adapter.DocumentAdapter"], "src\\main\\java\\com\\bei\\rag\\repository\\ConversationGroupRepository.kt": ["getDefaultGroupConversationCount:com.bei.rag.repository.ConversationGroupRepository", "ConversationGroupRepository:com.bei.rag.repository", "deleteGroup:com.bei.rag.repository.ConversationGroupRepository", "getAllGroups:com.bei.rag.repository.ConversationGroupRepository", "updateGroup:com.bei.rag.repository.ConversationGroupRepository", "getConversationCountByGroup:com.bei.rag.repository.ConversationGroupRepository", "getGroupById:com.bei.rag.repository.ConversationGroupRepository", "createGroup:com.bei.rag.repository.ConversationGroupRepository"], "src\\main\\java\\com\\bei\\rag\\database\\converter\\StringListConverter.kt": ["StringListConverter:com.bei.rag.database.converter", "fromStringList:com.bei.rag.database.converter.StringListConverter", "<init>:com.bei.rag.database.converter.StringListConverter", "toStringList:com.bei.rag.database.converter.StringListConverter"], "src\\main\\java\\com\\bei\\rag\\utils\\ThemeManager.kt": ["THEME_MODE_KEY:com.bei.rag.utils.ThemeManager.Companion", "THEME_MODE_SYSTEM:com.bei.rag.utils.ThemeManager.Companion", "getThemeColorName:com.bei.rag.utils.ThemeManager", "onThemeColorChanged:com.bei.rag.utils.ThemeManager", "getThemeColorResource:com.bei.rag.utils.ThemeManager", "getStatusBarColor:com.bei.rag.utils.ThemeManager", "THEME_COLOR_PURPLE:com.bei.rag.utils.ThemeManager.Companion", "getThemeStyleResource:com.bei.rag.utils.ThemeManager", "THEME_MODE_LIGHT:com.bei.rag.utils.ThemeManager.Companion", "getThemeModeName:com.bei.rag.utils.ThemeManager", "applyThemeMode:com.bei.rag.utils.ThemeManager", "getThemeColor:com.bei.rag.utils.ThemeManager", "THEME_COLOR_GREEN:com.bei.rag.utils.ThemeManager.Companion", "THEME_COLOR_GRAY:com.bei.rag.utils.ThemeManager.Companion", "THEME_COLOR_KEY:com.bei.rag.utils.ThemeManager.Companion", "setThemeColor:com.bei.rag.utils.ThemeManager", "<init>:com.bei.rag.utils.ThemeManager.Companion", "applyThemeColor:com.bei.rag.utils.ThemeManager", "setThemeMode:com.bei.rag.utils.ThemeManager", "ThemeManager:com.bei.rag.utils", "THEME_MODE_DARK:com.bei.rag.utils.ThemeManager.Companion", "Companion:com.bei.rag.utils.ThemeManager", "THEME_COLOR_PINK:com.bei.rag.utils.ThemeManager.Companion", "THEME_COLOR_RED:com.bei.rag.utils.ThemeManager.Companion", "getCurrentThemeColor:com.bei.rag.utils.ThemeManager", "getThemeMode:com.bei.rag.utils.ThemeManager", "isDarkMode:com.bei.rag.utils.ThemeManager", "THEME_COLOR_ORANGE:com.bei.rag.utils.ThemeManager.Companion", "THEME_COLOR_BLUE:com.bei.rag.utils.ThemeManager.Companion", "onThemeChanged:com.bei.rag.utils.ThemeManager", "initializeTheme:com.bei.rag.utils.ThemeManager"], "src\\main\\java\\com\\bei\\rag\\utils\\StatusBarOverlayManager.kt": ["createThemeBasedOverlay:com.bei.rag.utils.StatusBarOverlayManager", "<init>:com.bei.rag.utils.StatusBarOverlayManager.Companion", "createDrawerOverlay:com.bei.rag.utils.StatusBarOverlayManager", "Companion:com.bei.rag.utils.StatusBarOverlayManager", "createStatusBarOverlay:com.bei.rag.utils.StatusBarOverlayManager", "StatusBarOverlayManager:com.bei.rag.utils", "createGradientOverlay:com.bei.rag.utils.StatusBarOverlayManager", "createFullscreenOverlay:com.bei.rag.utils.StatusBarOverlayManager", "createBlurOverlay:com.bei.rag.utils.StatusBarOverlayManager", "onThemeChanged:com.bei.rag.utils.StatusBarOverlayManager", "cleanup:com.bei.rag.utils.StatusBarOverlayManager", "hasOverlay:com.bei.rag.utils.StatusBarOverlayManager", "removeStatusBarOverlay:com.bei.rag.utils.StatusBarOverlayManager", "updateOverlayColor:com.bei.rag.utils.StatusBarOverlayManager", "getCurrentOverlay:com.bei.rag.utils.StatusBarOverlayManager", "setOverlayVisibility:com.bei.rag.utils.StatusBarOverlayManager", "createChatOverlay:com.bei.rag.utils.StatusBarOverlayManager"], "src\\main\\java\\com\\bei\\rag\\database\\dao\\DocumentDao.kt": ["getDocumentCount:com.bei.rag.database.dao.DocumentDao", "getUnprocessedDocuments:com.bei.rag.database.dao.DocumentDao", "updateProcessingStatus:com.bei.rag.database.dao.DocumentDao", "deleteDocumentById:com.bei.rag.database.dao.DocumentDao", "insertDocument:com.bei.rag.database.dao.DocumentDao", "getTotalFileSize:com.bei.rag.database.dao.DocumentDao", "deleteDocument:com.bei.rag.database.dao.DocumentDao", "getAllDocuments:com.bei.rag.database.dao.DocumentDao", "searchDocuments:com.bei.rag.database.dao.DocumentDao", "updateDocument:com.bei.rag.database.dao.DocumentDao", "updateProcessingError:com.bei.rag.database.dao.DocumentDao", "DocumentDao:com.bei.rag.database.dao", "getDocumentById:com.bei.rag.database.dao.DocumentDao", "deleteAllDocuments:com.bei.rag.database.dao.DocumentDao", "getDocumentsByType:com.bei.rag.database.dao.DocumentDao", "updateExtractedText:com.bei.rag.database.dao.DocumentDao", "getDocumentByIdSync:com.bei.rag.database.dao.DocumentDao", "updateVectorizedStatus:com.bei.rag.database.dao.DocumentDao", "getAllDocumentsSync:com.bei.rag.database.dao.DocumentDao", "getVectorizedDocuments:com.bei.rag.database.dao.DocumentDao", "getVectorizedDocumentCount:com.bei.rag.database.dao.DocumentDao"], "src\\main\\java\\com\\bei\\rag\\service\\SupabaseVectorService.kt": ["getStatistics:com.bei.rag.service.SupabaseVectorService", "SupabaseVectorService:com.bei.rag.service", "updateDocumentStatus:com.bei.rag.service.SupabaseVectorService", "deleteDocument:com.bei.rag.service.SupabaseVectorService", "insertDocument:com.bei.rag.service.SupabaseVectorService", "insertDocumentChunks:com.bei.rag.service.SupabaseVectorService", "searchSimilarChunks:com.bei.rag.service.SupabaseVectorService", "<init>:com.bei.rag.service.SupabaseVectorService", "searchTopKSimilarChunks:com.bei.rag.service.SupabaseVectorService", "Companion:com.bei.rag.service.SupabaseVectorService", "getDocumentChunks:com.bei.rag.service.SupabaseVectorService", "getDocuments:com.bei.rag.service.SupabaseVectorService", "<init>:com.bei.rag.service.SupabaseVectorService.Companion"], "src\\main\\java\\com\\bei\\rag\\repository\\ChatRepository.kt": ["getAllConversationIds:com.bei.rag.repository.ChatRepository", "searchMessages:com.bei.rag.repository.ChatRepository", "updateConversationGroup:com.bei.rag.repository.ChatRepository", "deleteConversation:com.bei.rag.repository.ChatRepository", "getMessagesByConversationSync:com.bei.rag.repository.ChatRepository", "getConversationIdsByGroup:com.bei.rag.repository.ChatRepository", "getMessageCount:com.bei.rag.repository.ChatRepository", "deleteAllMessages:com.bei.rag.repository.ChatRepository", "getMessagesByConversation:com.bei.rag.repository.ChatRepository", "deleteMessage:com.bei.rag.repository.ChatRepository", "ChatRepository:com.bei.rag.repository", "insertMessage:com.bei.rag.repository.ChatRepository"], "src\\main\\java\\com\\bei\\rag\\service\\VectorSearchService.kt": ["search:com.bei.rag.service.VectorSearchService", "searchTopK:com.bei.rag.service.VectorSearchService", "VectorSearchService:com.bei.rag.service", "<init>:com.bei.rag.service.VectorSearchService.Companion", "getSearchStatistics:com.bei.rag.service.VectorSearchService", "Companion:com.bei.rag.service.VectorSearchService", "testSearch:com.bei.rag.service.VectorSearchService"], "src\\main\\java\\com\\bei\\rag\\model\\SupabaseModels.kt": ["documentId:com.bei.rag.model.VectorSearchResult", "content:com.bei.rag.model.SupabaseDocumentChunk", "fileType:com.bei.rag.model.SupabaseDocument", "lastSyncTime:com.bei.rag.model.SyncStatus", "documentId:com.bei.rag.model.SupabaseDocumentChunk", "documentName:com.bei.rag.model.VectorSearchResult", "query:com.bei.rag.model.RagQueryRequest", "createdAt:com.bei.rag.model.SupabaseDocument", "RagQueryResponse:com.bei.rag.model", "similarity:com.bei.rag.model.VectorSearchResult", "limit:com.bei.rag.model.VectorSearchRequest", "documentIds:com.bei.rag.model.RagQueryRequest", "chunkCount:com.bei.rag.model.SupabaseDocument", "documentIds:com.bei.rag.model.VectorSearchRequest", "results:com.bei.rag.model.RagQueryResponse", "updatedAt:com.bei.rag.model.SupabaseDocument", "chunkId:com.bei.rag.model.VectorSearchResult", "processingStatus:com.bei.rag.model.SupabaseDocument", "SupabaseDocument:com.bei.rag.model", "isOnline:com.bei.rag.model.SyncStatus", "pendingDownloads:com.bei.rag.model.SyncStatus", "successful:com.bei.rag.model.BatchOperationResult", "createdAt:com.bei.rag.model.SupabaseDocumentChunk", "context:com.bei.rag.model.RagQueryResponse", "id:com.bei.rag.model.SupabaseDocumentChunk", "failed:com.bei.rag.model.BatchOperationResult", "failureCount:com.bei.rag.model.BatchOperationResult", "SupabaseDocumentChunk:com.bei.rag.model", "SyncStatus:com.bei.rag.model", "VectorSearchResult:com.bei.rag.model", "totalResults:com.bei.rag.model.RagQueryResponse", "lastProcessedTime:com.bei.rag.model.SupabaseDocument", "totalProcessed:com.bei.rag.model.BatchOperationResult", "fileSize:com.bei.rag.model.SupabaseDocument", "queryEmbedding:com.bei.rag.model.VectorSearchRequest", "successCount:com.bei.rag.model.BatchOperationResult", "id:com.bei.rag.model.SupabaseDocument", "errorMessage:com.bei.rag.model.SupabaseDocument", "pendingUploads:com.bei.rag.model.SyncStatus", "fileName:com.bei.rag.model.SupabaseDocument", "metadata:com.bei.rag.model.SupabaseDocumentChunk", "RagQueryRequest:com.bei.rag.model", "metadata:com.bei.rag.model.VectorSearchResult", "maxResults:com.bei.rag.model.RagQueryRequest", "syncInProgress:com.bei.rag.model.SyncStatus", "extractedText:com.bei.rag.model.SupabaseDocument", "BatchOperationResult:com.bei.rag.model", "isVectorized:com.bei.rag.model.SupabaseDocument", "useTopKSearch:com.bei.rag.model.RagQueryRequest", "isAllSuccessful:com.bei.rag.model.BatchOperationResult", "threshold:com.bei.rag.model.VectorSearchRequest", "similarityThreshold:com.bei.rag.model.RagQueryRequest", "chunkIndex:com.bei.rag.model.SupabaseDocumentChunk", "query:com.bei.rag.model.RagQueryResponse", "embedding:com.bei.rag.model.SupabaseDocumentChunk", "uploadTime:com.bei.rag.model.SupabaseDocument", "VectorSearchRequest:com.bei.rag.model", "content:com.bei.rag.model.VectorSearchResult"], "src\\main\\java\\com\\bei\\rag\\utils\\PromptBuilder.kt": ["PromptBuilder:com.bei.rag.utils", "buildLowSimilarityPrompt:com.bei.rag.utils.PromptBuilder", "<init>:com.bei.rag.utils.PromptBuilder", "getMaxContextLength:com.bei.rag.utils.PromptBuilder", "buildStandardPrompt:com.bei.rag.utils.PromptBuilder", "buildNoContextPrompt:com.bei.rag.utils.PromptBuilder"], "src\\main\\java\\com\\bei\\rag\\model\\TtsModels.kt": ["TtsPrebuiltVoiceConfig:com.bei.rag.model", "TtsVoiceConfig:com.bei.rag.model", "error:com.bei.rag.model.TtsResponse", "TtsPart:com.bei.rag.model", "content:com.bei.rag.model.TtsCandidate", "TtsRequest:com.bei.rag.model", "TtsResponseContent:com.bei.rag.model", "TtsCandidate:com.bei.rag.model", "TtsResponse:com.bei.rag.model", "candidates:com.bei.rag.model.TtsResponse", "TtsError:com.bei.rag.model", "parts:com.bei.rag.model.TtsResponseContent", "TtsInlineData:com.bei.rag.model", "code:com.bei.rag.model.TtsError", "data:com.bei.rag.model.TtsInlineData", "speechConfig:com.bei.rag.model.TtsGenerationConfig", "voiceConfig:com.bei.rag.model.TtsSpeechConfig", "text:com.bei.rag.model.TtsPart", "TtsResponsePart:com.bei.rag.model", "status:com.bei.rag.model.TtsError", "TtsSpeechConfig:com.bei.rag.model", "message:com.bei.rag.model.TtsError", "mimeType:com.bei.rag.model.TtsInlineData", "generationConfig:com.bei.rag.model.TtsRequest", "TtsGenerationConfig:com.bei.rag.model", "prebuiltVoiceConfig:com.bei.rag.model.TtsVoiceConfig", "voiceName:com.bei.rag.model.TtsPrebuiltVoiceConfig", "parts:com.bei.rag.model.TtsContent", "TtsContent:com.bei.rag.model", "contents:com.bei.rag.model.TtsRequest", "inlineData:com.bei.rag.model.TtsResponsePart"], "src\\main\\java\\com\\bei\\rag\\service\\SupabaseConfig.kt": ["SupabaseConfig:com.bei.rag.service", "<init>:com.bei.rag.service.SupabaseConfig", "getConfig:com.bei.rag.service.SupabaseConfig", "cleanup:com.bei.rag.service.SupabaseConfig", "client:com.bei.rag.service.SupabaseConfig", "testConnection:com.bei.rag.service.SupabaseConfig", "initialize:com.bei.rag.service.SupabaseConfig", "isInitialized:com.bei.rag.service.SupabaseConfig"], "src\\main\\java\\com\\bei\\rag\\database\\entity\\DocumentEntity.kt": ["id:com.bei.rag.database.entity.DocumentEntity", "tags:com.bei.rag.database.entity.DocumentEntity", "errorMessage:com.bei.rag.database.entity.DocumentEntity", "fileName:com.bei.rag.database.entity.DocumentEntity", "uploadTime:com.bei.rag.database.entity.DocumentEntity", "isProcessed:com.bei.rag.database.entity.DocumentEntity", "extractedText:com.bei.rag.database.entity.DocumentEntity", "DocumentEntity:com.bei.rag.database.entity", "filePath:com.bei.rag.database.entity.DocumentEntity", "chunkCount:com.bei.rag.database.entity.DocumentEntity", "isVectorized:com.bei.rag.database.entity.DocumentEntity", "processingStatus:com.bei.rag.database.entity.DocumentEntity", "summary:com.bei.rag.database.entity.DocumentEntity", "fileSize:com.bei.rag.database.entity.DocumentEntity", "lastProcessedTime:com.bei.rag.database.entity.DocumentEntity", "fileType:com.bei.rag.database.entity.DocumentEntity"], "src\\main\\java\\com\\bei\\rag\\utils\\StatusBarColorAdapter.kt": ["needsDarkText:com.bei.rag.utils.StatusBarColorAdapter", "applyOverlayToColor:com.bei.rag.utils.StatusBarColorAdapter", "getStatusBarConfig:com.bei.rag.utils.StatusBarColorAdapter", "StatusBarConfig:com.bei.rag.utils.StatusBarColorAdapter", "getStatusBarTextColor:com.bei.rag.utils.StatusBarColorAdapter", "getStatusBarOverlayColor:com.bei.rag.utils.StatusBarColorAdapter", "backgroundColor:com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig", "StatusBarColorAdapter:com.bei.rag.utils", "shouldUseLightStatusBarContent:com.bei.rag.utils.StatusBarColorAdapter", "isDarkMode:com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig", "textColor:com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig", "overlayColor:com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig", "themeColor:com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig", "useLightContent:com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig", "getStatusBarBackgroundColor:com.bei.rag.utils.StatusBarColorAdapter"], "src\\main\\java\\com\\bei\\rag\\debug\\StatusBarTestActivity.kt": ["onCreate:com.bei.rag.debug.StatusBarTestActivity", "<init>:com.bei.rag.debug.StatusBarTestActivity", "StatusBarTestActivity:com.bei.rag.debug"], "src\\main\\java\\com\\bei\\rag\\service\\TextToSpeechService.kt": ["isEngineAvailable:com.bei.rag.service.TextToSpeechService", "speakText:com.bei.rag.service.TextToSpeechService", "setPitch:com.bei.rag.service.TextToSpeechService", "stopPlaying:com.bei.rag.service.TextToSpeechService", "setSpeechRate:com.bei.rag.service.TextToSpeechService", "initialize:com.bei.rag.service.TextToSpeechService", "switchEngine:com.bei.rag.service.TextToSpeechService", "<init>:com.bei.rag.service.TextToSpeechService.Companion", "setLanguage:com.bei.rag.service.TextToSpeechService", "getCurrentEngineType:com.bei.rag.service.TextToSpeechService", "isInitialized:com.bei.rag.service.TextToSpeechService", "stopSpeaking:com.bei.rag.service.TextToSpeechService", "Companion:com.bei.rag.service.TextToSpeechService", "isPlaying:com.bei.rag.service.TextToSpeechService", "TextToSpeechService:com.bei.rag.service", "isSpeaking:com.bei.rag.service.TextToSpeechService", "cleanup:com.bei.rag.service.TextToSpeechService"], "src\\main\\java\\com\\bei\\rag\\service\\RagQueryEngine.kt": ["getEngineStatistics:com.bei.rag.service.RagQueryEngine", "isValid:com.bei.rag.service.QueryValidationResult", "RagQueryEngine:com.bei.rag.service", "QueryValidationResult:com.bei.rag.service", "validateQuery:com.bei.rag.service.RagQueryEngine", "Companion:com.bei.rag.service.RagQueryEngine", "getQuerySuggestions:com.bei.rag.service.RagQueryEngine", "<init>:com.bei.rag.service.RagQueryEngine.Companion", "query:com.bei.rag.service.RagQueryEngine", "queryWithAiResponse:com.bei.rag.service.RagQueryEngine", "batchQuery:com.bei.rag.service.RagQueryEngine", "message:com.bei.rag.service.QueryValidationResult"], "src\\main\\java\\com\\bei\\rag\\fragment\\ThemeColorSettingsFragment.kt": ["<init>:com.bei.rag.fragment.ThemeColorSettingsFragment", "ThemeColorSettingsFragment:com.bei.rag.fragment", "onCreateView:com.bei.rag.fragment.ThemeColorSettingsFragment", "<init>:com.bei.rag.fragment.ThemeColorSettingsFragment.Companion", "Companion:com.bei.rag.fragment.ThemeColorSettingsFragment", "onViewCreated:com.bei.rag.fragment.ThemeColorSettingsFragment", "newInstance:com.bei.rag.fragment.ThemeColorSettingsFragment.Companion"], "src\\main\\java\\com\\bei\\rag\\adapter\\MainPagerAdapter.kt": ["getItemCount:com.bei.rag.adapter.MainPagerAdapter", "createFragment:com.bei.rag.adapter.MainPagerAdapter", "MainPagerAdapter:com.bei.rag.adapter"], "src\\main\\java\\com\\bei\\rag\\adapter\\NavConversationAdapter.kt": ["onBindViewHolder:com.bei.rag.adapter.NavConversationAdapter", "id:com.bei.rag.adapter.ConversationItem", "bind:com.bei.rag.adapter.NavConversationAdapter.NavConversationViewHolder", "NavConversationViewHolder:com.bei.rag.adapter.NavConversationAdapter", "onCreateViewHolder:com.bei.rag.adapter.NavConversationAdapter", "getItemCount:com.bei.rag.adapter.NavConversationAdapter", "timestamp:com.bei.rag.adapter.ConversationItem", "ConversationItem:com.bei.rag.adapter", "title:com.bei.rag.adapter.ConversationItem", "updateConversations:com.bei.rag.adapter.NavConversationAdapter", "NavConversationAdapter:com.bei.rag.adapter"], "src\\main\\java\\com\\bei\\rag\\service\\GeminiTtsApiService.kt": ["GeminiTtsApiService:com.bei.rag.service", "generateSpeech:com.bei.rag.service.GeminiTtsApiService"], "src\\main\\java\\com\\bei\\rag\\service\\tts\\TtsEngine.kt": ["TtsEngine:com.bei.rag.service.tts", "isSpeaking:com.bei.rag.service.tts.TtsEngine", "speakText:com.bei.rag.service.tts.TtsEngine", "setSpeechRate:com.bei.rag.service.tts.TtsEngine", "setLanguage:com.bei.rag.service.tts.TtsEngine", "stopSpeaking:com.bei.rag.service.tts.TtsEngine", "getEngineType:com.bei.rag.service.tts.TtsEngine", "cleanup:com.bei.rag.service.tts.TtsEngine", "initialize:com.bei.rag.service.tts.TtsEngine", "setPitch:com.bei.rag.service.tts.TtsEngine", "isAvailable:com.bei.rag.service.tts.TtsEngine"], "src\\main\\java\\com\\bei\\rag\\MainActivity.kt": ["onCreate:com.bei.rag.MainActivity", "onThemeColorChanged:com.bei.rag.MainActivity", "<init>:com.bei.rag.MainActivity", "getStatusBarManager:com.bei.rag.MainActivity", "onOptionsItemSelected:com.bei.rag.MainActivity", "MainActivity:com.bei.rag", "onCreateOptionsMenu:com.bei.rag.MainActivity", "onThemeChanged:com.bei.rag.MainActivity"], "src\\main\\java\\com\\bei\\rag\\utils\\DataManager.kt": ["chatMessageCount:com.bei.rag.utils.DataStatistics", "DataStatistics:com.bei.rag.utils", "clearDocuments:com.bei.rag.utils.DataManager", "clearAllData:com.bei.rag.utils.DataManager", "clearAllDataAndReset:com.bei.rag.utils.DataManager", "totalFileSize:com.bei.rag.utils.DataStatistics", "DataManager:com.bei.rag.utils", "getDataStatistics:com.bei.rag.utils.DataManager", "logoutOnly:com.bei.rag.utils.DataManager", "clearChatHistory:com.bei.rag.utils.DataManager", "documentCount:com.bei.rag.utils.DataStatistics"]}