-- Merging decision tree log ---
manifest
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:2:1-53:12
MERGED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml:2:1-14:12
INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml:2:1-14:12
INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.navigation:navigation-common:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8642ed82192f92f5a7ff21f276b7f054\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b0049283fd0a221dd059f1d98ee37cfe\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aca354158da2900b6fb5739648ae8d08\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\30a3a5cf28d82a6717a7fa7c7869d14e\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\7f340b9efe719af952d294be1d91aa2a\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\70c91663ae9e1d08ad0cf8a36439600e\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\bd272ac3b428632e9c4836d131933a19\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\87e510d0090684ec95967e00f767c8d2\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\fafbf46dfe262a160d10594082d329ed\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\bf400eb0bcd91d891e574df95efa598b\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0694865803a2e5d73514dc5f86929ad1\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\cc3f010a004ba5ff5f816d668841553a\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\16b88102b06fe29c3cacce204e1fba56\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\adbbdf277a302bd8a0816952fac7a34f\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e8ab14820f6cc5f80b08db762384969e\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\6102feefbb362b86810e12f089477319\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\60b6bf89c4e23781fd1ddda0fd453a59\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d6333596d831d95a6f58e21944f8019f\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b918e602760fc4a649ac225097856fe6\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5b450ca4a6d07b701c4c97a4ef947181\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\948e455012ec6f5747fb2c0f7b143a0a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5e957b81056b156742befb52b25e8cd9\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\a692b065bf69127aa74c233eee9b7d94\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\7ed2fdb15a01c2dd4836055ffdd5f6d2\transformed\realtime-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b0f8f438d13b70569e304931a7ce5fbb\transformed\postgrest-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\c65f52b3420cfe10a2a3723bd6d4be8b\transformed\supabase-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5d838edc95f09976b676c99e651ac74a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\eec0b832f68aa27dce1f82623e2f756d\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\613d5f4a73dd1cb30568381bb52f6ea3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b465907c6956e2551c533126608a4f40\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d1b9bf825508e0487d9d2525380d68c7\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\1a39ce3a6375ae96f75632ba6d7306ef\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\eaee31341cc7750986e939b29b0143cb\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\69e0956e1ca6accae3b189e6061a3b01\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\02e4c18b6f999ebf20ea49515d3a998a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\9b64a1d1e246818bebb2df89450ab96c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\531a5c5801be865e64b64e2eee6de46e\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\09cba91884d413b111f8703ff615ed5c\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\13d0b788504e72cb5b1bef4be970fc6f\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\4b0137496e2ec236ad82223acfcfe861\transformed\lifecycle-viewmodel-2.8.4\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\df727efb241494951b4115e8e351d454\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8e98638aaef6bf7a842e9452b7793105\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0f1c2545ff3fe51edaf44274fdead7d9\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\bd5451c169b81ad8076b9e72534ef636\transformed\lifecycle-viewmodel-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ea50133ac3694e6c2abb08eab661d465\transformed\lifecycle-livedata-core-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\660253859a8d9aa24563bd898ba33f1f\transformed\lifecycle-livedata-core-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d05070ea86ce3fd90c99ff21de7f5ab1\transformed\lifecycle-livedata-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f17e66df17f8ac4ac4360c9e7b5f53e1\transformed\lifecycle-viewmodel-savedstate-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\3fa196deffdfaf87acfc28f6f02e5b82\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f0562d5c96fc185e57b5d6c3454e3257\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\64f2dacbd89842e0e9b5f1931c2938a7\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\1d9dd1eba2e1cc0ebb829c8e813e2cec\transformed\krypto-debug\AndroidManifest.xml:2:1-9:12
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:17:1-35:12
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5eb8722c8bd9e97043038038b67ea85e\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e4d4ebf83e4083648775579a7e80fdb3\transformed\multiplatform-settings-debug\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-android-debug:2.0.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\fa0536655308c9f0ebdb3b74e834f04d\transformed\kermit-debug\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\df69ee355b79879582c0f53f4db3bf15\transformed\kermit-core-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.noties.markwon:ext-strikethrough:4.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\a89b379f7488d8281db344efa8f6fd1c\transformed\ext-strikethrough-4.6.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.noties.markwon:ext-tables:4.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\64cf2be04f8b2cf568ac11e7a2bc556d\transformed\ext-tables-4.6.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.noties.markwon:core:4.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\72a65bbdb1e9c87b164cfd1d33b0b6b7\transformed\core-4.6.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\7e84d5e82068f5a1621829129c204be1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\4967fab97ad30ad81204bc1831b30230\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\3408b9f79cf5c5d29e55fc7f9e95f71a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8a42def52982d4b87e4d920b4e3d4dc7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e45ce399109a1ccbd217ef422ae25af3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\9db9d24f913bb15a2e184b876e3cafb9\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\19c2adb6024cb353529da3b543a64abd\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b0e4210bf4a811e5b12bf131566d967e\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d1f8e0b2e924a47e5c37950abfba4769\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\839ad648b2e4830123274bb3b930b122\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.tom-roush:pdfbox-android:2.0.27.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\775e3d799280ec4f4d99eaaaf06ec77d\transformed\pdfbox-android-2.0.27.0\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:3:5-51
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:10:5-71
	android:name
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:10:22-68
application
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:12:5-51:19
MERGED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:12:5-51:19
MERGED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:12:5-51:19
INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml:4:5-12:19
MERGED from [com.google.android.material:material:1.12.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\fafbf46dfe262a160d10594082d329ed\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\fafbf46dfe262a160d10594082d329ed\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0694865803a2e5d73514dc5f86929ad1\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0694865803a2e5d73514dc5f86929ad1\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:23:5-33:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8a42def52982d4b87e4d920b4e3d4dc7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8a42def52982d4b87e4d920b4e3d4dc7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e45ce399109a1ccbd217ef422ae25af3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e45ce399109a1ccbd217ef422ae25af3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:19:9-35
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:19:9-35
	android:label
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:17:9-41
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:17:9-41
	android:fullBackupContent
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:15:9-54
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:15:9-54
	android:roundIcon
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:18:9-54
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:18:9-54
	tools:targetApi
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:22:9-29
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:22:9-29
	android:icon
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:16:9-43
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:16:9-43
	android:allowBackup
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:13:9-35
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:13:9-35
	android:theme
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:20:9-48
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:20:9-48
	android:networkSecurityConfig
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:21:9-69
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:21:9-69
	android:dataExtractionRules
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:14:9-65
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:14:9-65
activity#com.bei.rag.MainActivity
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:25:9-33:20
	android:windowSoftInputMode
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:28:13-55
	android:exported
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:27:13-36
	android:name
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:26:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:29:13-32:29
action#android.intent.action.MAIN
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:30:17-69
	android:name
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:30:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:31:17-77
	android:name
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:31:27-74
activity#com.bei.rag.ChatScreen
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:37:9-41:70
	android:parentActivityName
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:40:13-55
	android:windowSoftInputMode
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:41:13-67
	android:exported
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:39:13-37
	android:name
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:38:13-39
activity#com.bei.rag.debug.StatusBarTestActivity
ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:44:9-49:35
REJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:44:9-49:35
	tools:node
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:49:13-32
	android:parentActivityName
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:48:13-55
	android:label
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:47:13-36
	android:exported
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:46:13-37
	android:theme
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml:11:13-52
	android:name
		ADDED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\AndroidManifest.xml:45:13-56
uses-sdk
INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml
INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8642ed82192f92f5a7ff21f276b7f054\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8642ed82192f92f5a7ff21f276b7f054\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b0049283fd0a221dd059f1d98ee37cfe\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b0049283fd0a221dd059f1d98ee37cfe\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aca354158da2900b6fb5739648ae8d08\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aca354158da2900b6fb5739648ae8d08\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\30a3a5cf28d82a6717a7fa7c7869d14e\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\30a3a5cf28d82a6717a7fa7c7869d14e\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\7f340b9efe719af952d294be1d91aa2a\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\7f340b9efe719af952d294be1d91aa2a\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\70c91663ae9e1d08ad0cf8a36439600e\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\70c91663ae9e1d08ad0cf8a36439600e\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\bd272ac3b428632e9c4836d131933a19\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\bd272ac3b428632e9c4836d131933a19\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\87e510d0090684ec95967e00f767c8d2\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\87e510d0090684ec95967e00f767c8d2\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\fafbf46dfe262a160d10594082d329ed\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\fafbf46dfe262a160d10594082d329ed\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\bf400eb0bcd91d891e574df95efa598b\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\bf400eb0bcd91d891e574df95efa598b\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0694865803a2e5d73514dc5f86929ad1\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0694865803a2e5d73514dc5f86929ad1\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\cc3f010a004ba5ff5f816d668841553a\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\cc3f010a004ba5ff5f816d668841553a\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\16b88102b06fe29c3cacce204e1fba56\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\16b88102b06fe29c3cacce204e1fba56\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\adbbdf277a302bd8a0816952fac7a34f\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\adbbdf277a302bd8a0816952fac7a34f\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e8ab14820f6cc5f80b08db762384969e\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e8ab14820f6cc5f80b08db762384969e\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\6102feefbb362b86810e12f089477319\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\6102feefbb362b86810e12f089477319\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\60b6bf89c4e23781fd1ddda0fd453a59\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\60b6bf89c4e23781fd1ddda0fd453a59\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d6333596d831d95a6f58e21944f8019f\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d6333596d831d95a6f58e21944f8019f\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b918e602760fc4a649ac225097856fe6\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b918e602760fc4a649ac225097856fe6\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5b450ca4a6d07b701c4c97a4ef947181\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5b450ca4a6d07b701c4c97a4ef947181\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\948e455012ec6f5747fb2c0f7b143a0a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\948e455012ec6f5747fb2c0f7b143a0a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5e957b81056b156742befb52b25e8cd9\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5e957b81056b156742befb52b25e8cd9\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\a692b065bf69127aa74c233eee9b7d94\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\a692b065bf69127aa74c233eee9b7d94\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\7ed2fdb15a01c2dd4836055ffdd5f6d2\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\7ed2fdb15a01c2dd4836055ffdd5f6d2\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b0f8f438d13b70569e304931a7ce5fbb\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b0f8f438d13b70569e304931a7ce5fbb\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\c65f52b3420cfe10a2a3723bd6d4be8b\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\c65f52b3420cfe10a2a3723bd6d4be8b\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5d838edc95f09976b676c99e651ac74a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5d838edc95f09976b676c99e651ac74a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\eec0b832f68aa27dce1f82623e2f756d\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\eec0b832f68aa27dce1f82623e2f756d\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\613d5f4a73dd1cb30568381bb52f6ea3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\613d5f4a73dd1cb30568381bb52f6ea3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b465907c6956e2551c533126608a4f40\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b465907c6956e2551c533126608a4f40\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d1b9bf825508e0487d9d2525380d68c7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d1b9bf825508e0487d9d2525380d68c7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\1a39ce3a6375ae96f75632ba6d7306ef\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\1a39ce3a6375ae96f75632ba6d7306ef\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\eaee31341cc7750986e939b29b0143cb\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\eaee31341cc7750986e939b29b0143cb\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\69e0956e1ca6accae3b189e6061a3b01\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\69e0956e1ca6accae3b189e6061a3b01\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\02e4c18b6f999ebf20ea49515d3a998a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\02e4c18b6f999ebf20ea49515d3a998a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\9b64a1d1e246818bebb2df89450ab96c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\9b64a1d1e246818bebb2df89450ab96c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\531a5c5801be865e64b64e2eee6de46e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\531a5c5801be865e64b64e2eee6de46e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\09cba91884d413b111f8703ff615ed5c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\09cba91884d413b111f8703ff615ed5c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\13d0b788504e72cb5b1bef4be970fc6f\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\13d0b788504e72cb5b1bef4be970fc6f\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\4b0137496e2ec236ad82223acfcfe861\transformed\lifecycle-viewmodel-2.8.4\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\4b0137496e2ec236ad82223acfcfe861\transformed\lifecycle-viewmodel-2.8.4\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\df727efb241494951b4115e8e351d454\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\df727efb241494951b4115e8e351d454\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8e98638aaef6bf7a842e9452b7793105\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8e98638aaef6bf7a842e9452b7793105\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0f1c2545ff3fe51edaf44274fdead7d9\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0f1c2545ff3fe51edaf44274fdead7d9\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\bd5451c169b81ad8076b9e72534ef636\transformed\lifecycle-viewmodel-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\bd5451c169b81ad8076b9e72534ef636\transformed\lifecycle-viewmodel-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ea50133ac3694e6c2abb08eab661d465\transformed\lifecycle-livedata-core-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ea50133ac3694e6c2abb08eab661d465\transformed\lifecycle-livedata-core-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\660253859a8d9aa24563bd898ba33f1f\transformed\lifecycle-livedata-core-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\660253859a8d9aa24563bd898ba33f1f\transformed\lifecycle-livedata-core-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d05070ea86ce3fd90c99ff21de7f5ab1\transformed\lifecycle-livedata-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d05070ea86ce3fd90c99ff21de7f5ab1\transformed\lifecycle-livedata-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f17e66df17f8ac4ac4360c9e7b5f53e1\transformed\lifecycle-viewmodel-savedstate-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f17e66df17f8ac4ac4360c9e7b5f53e1\transformed\lifecycle-viewmodel-savedstate-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\3fa196deffdfaf87acfc28f6f02e5b82\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\3fa196deffdfaf87acfc28f6f02e5b82\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f0562d5c96fc185e57b5d6c3454e3257\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f0562d5c96fc185e57b5d6c3454e3257\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\64f2dacbd89842e0e9b5f1931c2938a7\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\64f2dacbd89842e0e9b5f1931c2938a7\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\1d9dd1eba2e1cc0ebb829c8e813e2cec\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\1d9dd1eba2e1cc0ebb829c8e813e2cec\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5eb8722c8bd9e97043038038b67ea85e\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\5eb8722c8bd9e97043038038b67ea85e\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e4d4ebf83e4083648775579a7e80fdb3\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e4d4ebf83e4083648775579a7e80fdb3\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\fa0536655308c9f0ebdb3b74e834f04d\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\fa0536655308c9f0ebdb3b74e834f04d\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\df69ee355b79879582c0f53f4db3bf15\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\df69ee355b79879582c0f53f4db3bf15\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [io.noties.markwon:ext-strikethrough:4.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\a89b379f7488d8281db344efa8f6fd1c\transformed\ext-strikethrough-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:ext-strikethrough:4.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\a89b379f7488d8281db344efa8f6fd1c\transformed\ext-strikethrough-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:ext-tables:4.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\64cf2be04f8b2cf568ac11e7a2bc556d\transformed\ext-tables-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:ext-tables:4.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\64cf2be04f8b2cf568ac11e7a2bc556d\transformed\ext-tables-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:core:4.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\72a65bbdb1e9c87b164cfd1d33b0b6b7\transformed\core-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:core:4.6.2] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\72a65bbdb1e9c87b164cfd1d33b0b6b7\transformed\core-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\7e84d5e82068f5a1621829129c204be1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\7e84d5e82068f5a1621829129c204be1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\4967fab97ad30ad81204bc1831b30230\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\4967fab97ad30ad81204bc1831b30230\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\3408b9f79cf5c5d29e55fc7f9e95f71a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\3408b9f79cf5c5d29e55fc7f9e95f71a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8a42def52982d4b87e4d920b4e3d4dc7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\8a42def52982d4b87e4d920b4e3d4dc7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e45ce399109a1ccbd217ef422ae25af3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e45ce399109a1ccbd217ef422ae25af3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\9db9d24f913bb15a2e184b876e3cafb9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\9db9d24f913bb15a2e184b876e3cafb9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\19c2adb6024cb353529da3b543a64abd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\19c2adb6024cb353529da3b543a64abd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b0e4210bf4a811e5b12bf131566d967e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\b0e4210bf4a811e5b12bf131566d967e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d1f8e0b2e924a47e5c37950abfba4769\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\d1f8e0b2e924a47e5c37950abfba4769\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\839ad648b2e4830123274bb3b930b122\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\839ad648b2e4830123274bb3b930b122\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.tom-roush:pdfbox-android:2.0.27.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\775e3d799280ec4f4d99eaaaf06ec77d\transformed\pdfbox-android-2.0.27.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.tom-roush:pdfbox-android:2.0.27.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\775e3d799280ec4f4d99eaaaf06ec77d\transformed\pdfbox-android-2.0.27.0\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\AndroidManifest.xml
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\dbea4ac7a23a349ccd393fee86374932\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e45ce399109a1ccbd217ef422ae25af3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\e45ce399109a1ccbd217ef422ae25af3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\0ba9b7e0ef5cc47a489e393a1b2c9750\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#io.github.jan.supabase.storage.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2e06dc96eb91a846d7b40f03c8252785\transformed\storage-kt-debug\AndroidManifest.xml:15:17-82
meta-data#io.github.jan.supabase.gotrue.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\11a29e193db9451c7c039538c3248c2a\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\ffcbfa88245deb2eef26300a4e96b954\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.bei.rag.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.bei.rag.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\f8d17c1b3f821ed6af16e75b32214186\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.4] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\2bc35797c1b16669930e35a63d2d1ab1\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:30:17-78
meta-data#com.russhwolf.settings.SettingsInitializer
ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\aeea697c881a8b30874aa32ec7e2ca16\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\SoftWare\AndroidGradleRepository\caches\8.11.1\transforms\79028180fc7117276f7f52fb908bc5ea\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
