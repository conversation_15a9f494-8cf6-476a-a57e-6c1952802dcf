<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 录音权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Ragandroid"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">

        <!-- 主Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 聊天Activity -->
        <!-- 聊天Activity -->
        <activity
            android:name=".ChatScreen"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <!-- 状态栏测试Activity (仅Debug版本) -->
        <activity
            android:name=".debug.StatusBarTestActivity"
            android:exported="false"
            android:label="状态栏测试工具"
            android:parentActivityName=".MainActivity"
            tools:node="remove" />

    </application>

</manifest>