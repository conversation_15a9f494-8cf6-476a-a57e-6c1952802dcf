package com.bei.rag

import android.graphics.Rect
import android.os.Bundle
import android.view.View
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.adapter.ChatAdapter
import com.bei.rag.model.ChatMessage
import com.bei.rag.service.OpenRouterApiService
import com.bei.rag.utils.KeyboardUtils
import io.noties.markwon.Markwon
import kotlinx.coroutines.launch

class ChatScreen : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var messageInput: EditText
    private lateinit var sendButton: ImageButton
    private lateinit var backButton: ImageButton
    private lateinit var inputLayout: LinearLayout
    private lateinit var rootLayout: LinearLayout

    private lateinit var chatAdapter: ChatAdapter
    private lateinit var markwon: Markwon
    private val apiService = OpenRouterApiService()
    private var isWaitingForResponse = false

    // 键盘监听相关
    private var rootViewHeight = 0
    private var keyboardHeight = 0
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 确保软键盘模式设置正确
        window.setSoftInputMode(
            WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE or
            WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN
        )

        setContentView(R.layout.activity_chat)

        // 隐藏默认ActionBar
        supportActionBar?.hide()

        setupUI()
        setupListeners()
        setupKeyboardHandling()

        // 添加欢迎消息
        addWelcomeMessage()
    }

    private fun setupUI() {
        recyclerView = findViewById(R.id.rv_chat_messages)
        messageInput = findViewById(R.id.et_message_input)
        sendButton = findViewById(R.id.btn_send)
        backButton = findViewById(R.id.btn_back)
        inputLayout = findViewById(R.id.input_layout)
        rootLayout = findViewById(R.id.root_layout)

        // 初始化Markwon
        markwon = Markwon.create(this)

        // 设置RecyclerView
        chatAdapter = ChatAdapter(markwon) { aiMessage ->
            // 重新生成消息的回调
            regenerateMessage(aiMessage)
        }
        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@ChatScreen)
            adapter = chatAdapter
        }
    }
    
    private fun setupListeners() {
        sendButton.setOnClickListener {
            sendMessage()
        }

        backButton.setOnClickListener {
            onBackPressed()
        }

        messageInput.addTextChangedListener { text ->
            sendButton.isEnabled = !text.isNullOrBlank() && !isWaitingForResponse
        }

        // 输入框获得焦点时滚动到底部
        messageInput.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                recyclerView.postDelayed({
                    scrollToBottom()
                }, 300) // 给键盘弹出留出时间
            }
        }

        // 初始状态下禁用发送按钮
        sendButton.isEnabled = false
    }

    private fun setupKeyboardHandling() {
        // 使用最简单直接的方法
        setupSimpleKeyboardListener()
    }

    private fun setupSimpleKeyboardListener() {
        val rootView = findViewById<View>(android.R.id.content)

        rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val rect = Rect()
            rootView.getWindowVisibleDisplayFrame(rect)
            val screenHeight = rootView.height
            val keyboardHeight = screenHeight - rect.bottom

            if (rootViewHeight == 0) {
                rootViewHeight = screenHeight
            }

            // 键盘高度超过屏幕15%认为是打开状态
            if (keyboardHeight > rootViewHeight * 0.15) {
                // 键盘显示 - 直接调整根布局的底部padding
                rootLayout.setPadding(
                    rootLayout.paddingLeft,
                    rootLayout.paddingTop,
                    rootLayout.paddingRight,
                    keyboardHeight
                )
                recyclerView.postDelayed({
                    scrollToBottom()
                }, 100)
            } else {
                // 键盘隐藏 - 恢复根布局的padding
                rootLayout.setPadding(
                    rootLayout.paddingLeft,
                    rootLayout.paddingTop,
                    rootLayout.paddingRight,
                    0
                )
            }
        }
    }


    
    private fun sendMessage() {
        val message = messageInput.text.toString().trim()
        if (message.isNotEmpty() && !isWaitingForResponse) {
            // 添加用户消息
            addUserMessage(message)

            // 清空输入框
            messageInput.text.clear()

            // 设置等待状态
            setWaitingState(true)

            // 调用API获取回复
            getAIResponse(message)
        }
    }

    private fun addUserMessage(content: String) {
        val message = ChatMessage(
            content = content,
            isUser = true
        )
        chatAdapter.addMessage(message)
        scrollToBottom()
    }

    private fun addAIMessage(content: String, sources: List<String> = emptyList()) {
        val message = ChatMessage(
            content = content,
            isUser = false,
            sources = sources
        )
        chatAdapter.addMessage(message)
        scrollToBottom()
    }

    private fun addWelcomeMessage() {
        addAIMessage("您好！我是您的AI助手，有什么可以帮助您的吗？")
    }

    private fun scrollToBottom() {
        recyclerView.post {
            if (chatAdapter.itemCount > 0) {
                recyclerView.smoothScrollToPosition(chatAdapter.itemCount - 1)
            }
        }
    }
    
    private fun getAIResponse(userMessage: String) {
        // 显示正在思考的消息
        addAIMessage("正在思考中...")

        lifecycleScope.launch {
            try {
                val result = apiService.sendMessage(userMessage)

                // 移除"正在思考中..."的消息
                chatAdapter.removeLastMessage()

                result.fold(
                    onSuccess = { response ->
                        // 模拟来源信息（实际应用中应该从API响应中获取）
                        val sources = listOf("知识库文档A", "参考资料B")
                        addAIMessage(response, sources)
                    },
                    onFailure = { error ->
                        addAIMessage("抱歉，获取回复时出现错误：${error.message}")
                        Toast.makeText(this@ChatScreen, "请求失败：${error.message}", Toast.LENGTH_LONG).show()
                    }
                )
            } catch (e: Exception) {
                // 移除"正在思考中..."的消息
                chatAdapter.removeLastMessage()
                addAIMessage("网络连接异常，请检查网络设置")
                Toast.makeText(this@ChatScreen, "网络异常：${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                setWaitingState(false)
            }
        }
    }

    private fun setWaitingState(waiting: Boolean) {
        isWaitingForResponse = waiting
        sendButton.isEnabled = !waiting && messageInput.text.isNotBlank()
        sendButton.alpha = if (waiting) 0.5f else 1.0f
    }

    /**
     * 重新生成AI消息
     * 采用方案2：不删除原有回复，在下方新建回复
     * 这样保持对话的完整性，用户可以比较不同的回复
     */
    private fun regenerateMessage(aiMessage: ChatMessage) {
        if (isWaitingForResponse) {
            Toast.makeText(this, "请等待当前回复完成", Toast.LENGTH_SHORT).show()
            return
        }

        // 找到这个AI消息对应的用户消息
        val aiMessageIndex = chatAdapter.getMessageIndex(aiMessage)
        if (aiMessageIndex == -1) {
            Toast.makeText(this, "无法找到对应的消息", Toast.LENGTH_SHORT).show()
            return
        }

        // 查找AI消息前面最近的用户消息
        val userMessage = chatAdapter.getUserMessageBefore(aiMessageIndex)
        if (userMessage == null) {
            Toast.makeText(this, "无法找到对应的用户消息", Toast.LENGTH_SHORT).show()
            return
        }

        // 设置等待状态
        setWaitingState(true)

        // 重新发送用户消息，生成新的AI回复（添加到对话末尾）
        getAIResponse(userMessage.content)
    }
}
