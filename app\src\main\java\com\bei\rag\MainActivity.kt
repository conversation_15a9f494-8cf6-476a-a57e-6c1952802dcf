package com.bei.rag

import android.os.Bundle
import android.content.Intent
import android.view.Menu
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import com.bei.rag.fragment.ChatFragment
import com.bei.rag.utils.StatusBarManager
import com.bei.rag.utils.ThemeManager

class MainActivity : AppCompatActivity() {

    private lateinit var statusBarManager: StatusBarManager
    private lateinit var themeManager: ThemeManager

    override fun onCreate(savedInstanceState: Bundle?) {
        // 初始化主题管理器
        themeManager = ThemeManager(this)
        themeManager.initializeTheme()
        themeManager.applyThemeColor(this)

        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 初始化状态栏管理器 - 必须在setContentView之后
        statusBarManager = StatusBarManager(this)
        statusBarManager.initialize()

        // 隐藏ActionBar
        supportActionBar?.hide()

        // 初始化ChatFragment
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, ChatFragment.newInstance())
                .commit()
        }
    }

    /**
     * 主题切换时更新状态栏
     * @param animated 是否使用动画过渡
     * @param onComplete 动画完成回调
     */
    fun onThemeChanged(animated: Boolean = true, onComplete: (() -> Unit)? = null) {
        if (animated) {
            statusBarManager.animateModeTransition {
                onComplete?.invoke()
            }
        } else {
            statusBarManager.applyThemeBasedStatusBar()
            onComplete?.invoke()
        }
    }

    /**
     * 主题颜色切换时更新状态栏
     * @param newThemeColor 新主题颜色
     * @param animated 是否使用动画过渡
     * @param onComplete 动画完成回调
     */
    fun onThemeColorChanged(newThemeColor: String, animated: Boolean = true, onComplete: (() -> Unit)? = null) {
        if (animated) {
            statusBarManager.animateThemeTransition(newThemeColor) {
                onComplete?.invoke()
            }
        } else {
            statusBarManager.applyThemeBasedStatusBar()
            onComplete?.invoke()
        }
    }

    /**
     * 获取状态栏管理器实例
     */
    fun getStatusBarManager(): StatusBarManager {
        return statusBarManager
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        // 仅在Debug版本中显示测试菜单
        if (BuildConfig.DEBUG) {
            menuInflater.inflate(R.menu.debug_menu, menu)
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_status_bar_test -> {
                if (BuildConfig.DEBUG) {
                    startActivity(Intent(this, com.bei.rag.debug.StatusBarTestActivity::class.java))
                }
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
