package com.bei.rag.adapter

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.view.ContextMenu
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.PopupMenu
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R
import com.bei.rag.model.ChatMessage
import io.noties.markwon.Markwon
import java.text.SimpleDateFormat
import java.util.*

class ChatAdapter(
    private val markwon: Markwon,
    private val onRegenerateMessage: ((ChatMessage) -> Unit)? = null,
    private val onPlayAudio: ((ChatMessage) -> Unit)? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    
    private val messages = mutableListOf<ChatMessage>()
    private val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
    
    companion object {
        private const val VIEW_TYPE_USER = 1
        private const val VIEW_TYPE_AI = 2
    }
    
    fun addMessage(message: ChatMessage) {
        messages.add(message)
        notifyItemInserted(messages.size - 1)
    }

    fun updateMessages(newMessages: List<ChatMessage>) {
        messages.clear()
        messages.addAll(newMessages)
        notifyDataSetChanged()
    }

    fun removeLastMessage() {
        if (messages.isNotEmpty()) {
            val lastIndex = messages.size - 1
            messages.removeAt(lastIndex)
            notifyItemRemoved(lastIndex)
        }
    }

    fun updateLastMessage(content: String) {
        if (messages.isNotEmpty()) {
            val lastIndex = messages.size - 1
            val lastMessage = messages[lastIndex]
            messages[lastIndex] = lastMessage.copy(content = content)
            notifyItemChanged(lastIndex)
        }
    }
    
    override fun getItemViewType(position: Int): Int {
        return if (messages[position].isUser) VIEW_TYPE_USER else VIEW_TYPE_AI
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_USER -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_message_user, parent, false)
                UserMessageViewHolder(view)
            }
            VIEW_TYPE_AI -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_message_ai, parent, false)
                AiMessageViewHolder(view)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val message = messages[position]
        when (holder) {
            is UserMessageViewHolder -> holder.bind(message)
            is AiMessageViewHolder -> holder.bind(message)
        }
    }
    
    override fun getItemCount(): Int = messages.size
    
    inner class UserMessageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val messageText: TextView = itemView.findViewById(R.id.tv_message_content)
        private val timeText: TextView = itemView.findViewById(R.id.tv_message_time)

        fun bind(message: ChatMessage) {
            messageText.text = message.content
            timeText.text = timeFormatter.format(Date(message.timestamp))

            // 设置长按复制功能
            setupLongClickCopy(messageText, message.content)
        }
    }
    
    inner class AiMessageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val messageText: TextView = itemView.findViewById(R.id.tv_message_content)
        private val timeText: TextView = itemView.findViewById(R.id.tv_message_time)
        private val sourcesText: TextView = itemView.findViewById(R.id.tv_sources)
        private val refreshButton: ImageButton = itemView.findViewById(R.id.btn_refresh)
        private val copyButton: ImageButton = itemView.findViewById(R.id.btn_copy)
        private val shareButton: ImageButton = itemView.findViewById(R.id.btn_share)
        private val audioButton: ImageButton = itemView.findViewById(R.id.btn_audio)
        private val moreButton: ImageButton = itemView.findViewById(R.id.btn_more)

        fun bind(message: ChatMessage) {
            // 使用Markwon渲染Markdown内容
            markwon.setMarkdown(messageText, message.content)
            timeText.text = timeFormatter.format(Date(message.timestamp))

            // 显示来源信息
            if (message.sources.isNotEmpty()) {
                sourcesText.visibility = View.VISIBLE
                sourcesText.text = "来源: ${message.sources.joinToString(", ")}"
            } else {
                sourcesText.visibility = View.GONE
            }

            // 设置刷新按钮点击事件
            refreshButton.setOnClickListener {
                onRegenerateMessage?.invoke(message)
            }

            // 设置复制按钮点击事件
            copyButton.setOnClickListener {
                copyToClipboard(itemView.context, message.content)
            }

            // 设置分享按钮点击事件
            shareButton.setOnClickListener {
                shareText(itemView.context, message.content)
            }

            // 设置音频播放按钮点击事件
            audioButton.setOnClickListener {
                onPlayAudio?.invoke(message)
            }

            // 更新音频按钮状态
            updateAudioButtonState(message)

            // 设置更多操作按钮
            moreButton.setOnClickListener {
                showMoreOptionsMenu(it, message)
            }

            // 设置长按复制功能
            setupLongClickCopy(messageText, message.content)
        }

        private fun updateAudioButtonState(message: ChatMessage) {
            if (message.isPlaying) {
                audioButton.setImageResource(R.drawable.ic_volume_off)
                audioButton.contentDescription = "停止播放"
            } else {
                audioButton.setImageResource(R.drawable.ic_volume_up)
                audioButton.contentDescription = "播放音频"
            }
        }
    }

    private fun copyToClipboard(context: Context, text: String) {
        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("消息内容", text)
        clipboard.setPrimaryClip(clip)
        Toast.makeText(context, "已复制到剪贴板", Toast.LENGTH_SHORT).show()
    }

    private fun setupLongClickCopy(textView: TextView, content: String) {
        textView.setOnLongClickListener {
            showContextMenu(it, content)
            true
        }

        // 设置文本可选择
        textView.setTextIsSelectable(true)
    }

    private fun showContextMenu(view: View, content: String) {
        val popupMenu = PopupMenu(view.context, view)
        popupMenu.menuInflater.inflate(R.menu.message_context_menu, popupMenu.menu)

        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.action_copy -> {
                    copyToClipboard(view.context, content)
                    true
                }
                R.id.action_select_all -> {
                    // 复制全部内容（因为TextView的选择功能比较复杂）
                    copyToClipboard(view.context, content)
                    Toast.makeText(view.context, "已复制全部内容", Toast.LENGTH_SHORT).show()
                    true
                }
                R.id.action_share -> {
                    shareText(view.context, content)
                    true
                }
                else -> false
            }
        }

        popupMenu.show()
    }

    private fun showMoreOptionsMenu(view: View, message: ChatMessage) {
        val popupMenu = PopupMenu(view.context, view)
        popupMenu.menuInflater.inflate(R.menu.ai_message_options_menu, popupMenu.menu)

        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.action_copy -> {
                    copyToClipboard(view.context, message.content)
                    true
                }
                R.id.action_share -> {
                    shareText(view.context, message.content)
                    true
                }
                R.id.action_regenerate -> {
                    onRegenerateMessage?.invoke(message)
                    true
                }
                else -> false
            }
        }

        popupMenu.show()
    }

    private fun shareText(context: Context, text: String) {
        val shareIntent = android.content.Intent().apply {
            action = android.content.Intent.ACTION_SEND
            type = "text/plain"
            putExtra(android.content.Intent.EXTRA_TEXT, text)
        }
        context.startActivity(android.content.Intent.createChooser(shareIntent, "分享消息"))
    }

    fun getMessageIndex(message: ChatMessage): Int {
        return messages.indexOfFirst { it.id == message.id }
    }

    fun getUserMessageBefore(aiMessageIndex: Int): ChatMessage? {
        // 从AI消息位置向前查找最近的用户消息
        for (i in aiMessageIndex - 1 downTo 0) {
            if (messages[i].isUser) {
                return messages[i]
            }
        }
        return null
    }

    /**
     * 更新消息的播放状态
     */
    fun updateMessagePlayingState(messageId: String, isPlaying: Boolean) {
        val index = messages.indexOfFirst { it.id == messageId }
        if (index != -1) {
            messages[index].isPlaying = isPlaying
            notifyItemChanged(index)
        }
    }

    /**
     * 停止所有消息的播放状态
     */
    fun stopAllAudioPlaying() {
        messages.forEachIndexed { index, message ->
            if (message.isPlaying) {
                message.isPlaying = false
                notifyItemChanged(index)
            }
        }
    }
}
