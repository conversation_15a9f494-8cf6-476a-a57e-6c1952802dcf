package com.bei.rag.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R
import com.bei.rag.model.Conversation
import java.text.SimpleDateFormat
import java.util.*

class ConversationAdapter(
    private val onConversationClick: (Conversation) -> Unit,
    private val onDeleteClick: (Conversation) -> Unit
) : RecyclerView.Adapter<ConversationAdapter.ConversationViewHolder>() {

    private var conversations = listOf<Conversation>()
    private val dateFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())

    fun updateConversations(newConversations: List<Conversation>) {
        conversations = newConversations.sortedByDescending { it.lastMessageTime }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ConversationViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_conversation, parent, false)
        return ConversationViewHolder(view)
    }

    override fun onBindViewHolder(holder: ConversationViewHolder, position: Int) {
        holder.bind(conversations[position])
    }

    override fun getItemCount(): Int = conversations.size

    inner class ConversationViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleText: TextView = itemView.findViewById(R.id.tv_conversation_title)
        private val lastMessageText: TextView = itemView.findViewById(R.id.tv_last_message)
        private val timeText: TextView = itemView.findViewById(R.id.tv_time)
        private val messageCountText: TextView = itemView.findViewById(R.id.tv_message_count)
        private val deleteButton: ImageButton = itemView.findViewById(R.id.btn_delete)

        fun bind(conversation: Conversation) {
            titleText.text = conversation.title
            lastMessageText.text = conversation.lastMessage
            timeText.text = dateFormat.format(Date(conversation.lastMessageTime))
            messageCountText.text = "${conversation.messageCount}"

            itemView.setOnClickListener {
                onConversationClick(conversation)
            }

            deleteButton.setOnClickListener {
                onDeleteClick(conversation)
            }
        }
    }
}
