package com.bei.rag.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R
import com.bei.rag.model.ConversationGroup

class ConversationGroupAdapter(
    private val onGroupClick: (ConversationGroup) -> Unit,
    private val onMoreClick: (ConversationGroup) -> Unit
) : RecyclerView.Adapter<ConversationGroupAdapter.GroupViewHolder>() {

    private var groups = listOf<ConversationGroup>()

    fun updateGroups(newGroups: List<ConversationGroup>) {
        groups = newGroups.sortedBy { it.createdTime }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GroupViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_conversation_group, parent, false)
        return GroupViewHolder(view)
    }

    override fun onBindViewHolder(holder: GroupViewHolder, position: Int) {
        holder.bind(groups[position])
    }

    override fun getItemCount(): Int = groups.size

    inner class GroupViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val groupNameText: TextView = itemView.findViewById(R.id.tv_group_name)
        private val conversationCountText: TextView = itemView.findViewById(R.id.tv_conversation_count)
        private val moreButton: ImageButton = itemView.findViewById(R.id.btn_more)

        fun bind(group: ConversationGroup) {
            groupNameText.text = group.name
            conversationCountText.text = "${group.conversationCount} 个会话"

            itemView.setOnClickListener {
                onGroupClick(group)
            }

            moreButton.setOnClickListener {
                onMoreClick(group)
            }
        }
    }
}
