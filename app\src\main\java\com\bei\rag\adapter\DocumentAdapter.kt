package com.bei.rag.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R
import com.bei.rag.database.entity.DocumentEntity
import java.text.SimpleDateFormat
import java.util.*

class DocumentAdapter(
    private val onDocumentClick: (DocumentEntity) -> Unit,
    private val onDeleteClick: (DocumentEntity) -> Unit,
    private val onProcessClick: ((DocumentEntity) -> Unit)? = null
) : RecyclerView.Adapter<DocumentAdapter.DocumentViewHolder>() {
    
    private var documents = listOf<DocumentEntity>()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    
    fun updateDocuments(newDocuments: List<DocumentEntity>) {
        documents = newDocuments
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DocumentViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_document, parent, false)
        return DocumentViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: DocumentViewHolder, position: Int) {
        holder.bind(documents[position])
    }
    
    override fun getItemCount(): Int = documents.size
    
    inner class DocumentViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val fileIcon: ImageView = itemView.findViewById(R.id.iv_file_icon)
        private val fileName: TextView = itemView.findViewById(R.id.tv_file_name)
        private val fileSize: TextView = itemView.findViewById(R.id.tv_file_size)
        private val uploadTime: TextView = itemView.findViewById(R.id.tv_upload_time)
        private val deleteButton: ImageButton = itemView.findViewById(R.id.btn_delete)
        private val statusText: TextView = itemView.findViewById(R.id.tv_processing_status)
        private val processButton: ImageButton? = itemView.findViewById(R.id.btn_process)
        
        fun bind(document: DocumentEntity) {
            fileName.text = document.fileName
            fileSize.text = formatFileSize(document.fileSize)
            uploadTime.text = dateFormat.format(Date(document.uploadTime))
            
            // 设置文件图标
            val iconRes = when (document.fileType) {
                "pdf" -> R.drawable.ic_file_pdf
                "docx" -> R.drawable.ic_file_doc
                "csv" -> R.drawable.ic_file_csv
                "txt" -> R.drawable.ic_file_txt
                else -> R.drawable.ic_file_unknown
            }
            fileIcon.setImageResource(iconRes)
            
            // 设置点击事件
            itemView.setOnClickListener {
                onDocumentClick(document)
            }
            
            deleteButton.setOnClickListener {
                onDeleteClick(document)
            }
            
            // 显示处理状态
            val statusText = when (document.processingStatus) {
                "pending" -> "待处理"
                "processing" -> "处理中"
                "completed" -> "已完成"
                "failed" -> "处理失败"
                else -> document.processingStatus
            }
            this.statusText.text = statusText
            
            // 设置处理状态颜色
            val statusColor = when (document.processingStatus) {
                "completed" -> android.graphics.Color.GREEN
                "failed" -> android.graphics.Color.RED
                "processing" -> android.graphics.Color.BLUE
                else -> android.graphics.Color.GRAY
            }
            this.statusText.setTextColor(statusColor)
            
            // 设置处理按钮
            processButton?.let { button ->
                if (onProcessClick != null && !document.isVectorized) {
                    button.visibility = View.VISIBLE
                    button.setOnClickListener {
                        onProcessClick.invoke(document)
                    }
                } else {
                    button.visibility = View.GONE
                }
            }
        }
        
        private fun formatFileSize(bytes: Long): String {
            return when {
                bytes < 1024 -> "$bytes B"
                bytes < 1024 * 1024 -> "${bytes / 1024} KB"
                bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
                else -> "${bytes / (1024 * 1024 * 1024)} GB"
            }
        }
    }
}
