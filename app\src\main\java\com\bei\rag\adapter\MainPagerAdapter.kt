package com.bei.rag.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bei.rag.fragment.ChatFragment
import com.bei.rag.fragment.KnowledgeFragment
import com.bei.rag.fragment.ProfileFragment

class MainPagerAdapter(fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {
    
    override fun getItemCount(): Int = 3
    
    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> ChatFragment.newInstance()
            1 -> KnowledgeFragment.newInstance()
            2 -> ProfileFragment.newInstance()
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
}
