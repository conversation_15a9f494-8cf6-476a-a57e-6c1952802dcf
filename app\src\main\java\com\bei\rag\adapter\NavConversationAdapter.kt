package com.bei.rag.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R

data class ConversationItem(
    val id: Long,
    val title: String,
    val timestamp: Long
)

class NavConversationAdapter(
    private val onConversationClick: (ConversationItem) -> Unit
) : RecyclerView.Adapter<NavConversationAdapter.NavConversationViewHolder>() {

    private var conversations = listOf<ConversationItem>()

    fun updateConversations(newConversations: List<ConversationItem>) {
        conversations = newConversations
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NavConversationViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_nav_conversation, parent, false)
        return NavConversationViewHolder(view)
    }

    override fun onBindViewHolder(holder: NavConversationViewHolder, position: Int) {
        holder.bind(conversations[position])
    }

    override fun getItemCount(): Int = conversations.size

    inner class NavConversationViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleText: TextView = itemView.findViewById(R.id.tv_conversation_title)

        fun bind(conversation: ConversationItem) {
            titleText.text = conversation.title

            itemView.setOnClickListener {
                onConversationClick(conversation)
            }
        }
    }
}
