package com.bei.rag.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R
import com.bei.rag.model.ConversationGroup

class NavGroupAdapter(
    private val onGroupClick: (ConversationGroup) -> Unit,
    private val onGroupLongClick: (ConversationGroup) -> Unit
) : RecyclerView.Adapter<NavGroupAdapter.NavGroupViewHolder>() {

    private var groups = listOf<ConversationGroup>()

    fun updateGroups(newGroups: List<ConversationGroup>) {
        groups = newGroups.sortedBy { it.createdTime }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NavGroupViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_nav_group, parent, false)
        return NavGroupViewHolder(view)
    }

    override fun onBindViewHolder(holder: NavGroupViewHolder, position: Int) {
        holder.bind(groups[position])
    }

    override fun getItemCount(): Int = groups.size

    inner class NavGroupViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val groupNameText: TextView = itemView.findViewById(R.id.tv_group_name)
        private val conversationCountText: TextView = itemView.findViewById(R.id.tv_conversation_count)

        fun bind(group: ConversationGroup) {
            groupNameText.text = group.name
            conversationCountText.text = group.conversationCount.toString()
            
            // 如果会话数量为0，隐藏徽章
            conversationCountText.visibility = if (group.conversationCount > 0) {
                View.VISIBLE
            } else {
                View.GONE
            }

            itemView.setOnClickListener {
                onGroupClick(group)
            }

            itemView.setOnLongClickListener {
                onGroupLongClick(group)
                true
            }
        }
    }
}
