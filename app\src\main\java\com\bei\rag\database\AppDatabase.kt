package com.bei.rag.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.bei.rag.database.dao.ChatMessageDao
import com.bei.rag.database.dao.ConversationGroupDao
import com.bei.rag.database.dao.DocumentDao
import com.bei.rag.database.dao.UserDao
import com.bei.rag.database.entity.ChatMessageEntity
import com.bei.rag.database.entity.ConversationGroupEntity
import com.bei.rag.database.entity.DocumentEntity
import com.bei.rag.database.entity.UserEntity
import com.bei.rag.database.converter.StringListConverter

@Database(
    entities = [
        ChatMessageEntity::class,
        ConversationGroupEntity::class,
        DocumentEntity::class,
        UserEntity::class
    ],
    version = 4,
    exportSchema = false
)
@TypeConverters(StringListConverter::class)
abstract class AppDatabase : RoomDatabase() {
    
    abstract fun chatMessageDao(): ChatMessageDao
    abstract fun conversationGroupDao(): ConversationGroupDao
    abstract fun documentDao(): DocumentDao
    abstract fun userDao(): UserDao
    
    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "rag_database"
                )
                    .addMigrations(MIGRATION_1_2, MIGRATION_2_3, MIGRATION_3_4)
                    .fallbackToDestructiveMigration() // 如果迁移失败，重新创建数据库
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }

        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 版本1到版本2的迁移
                // 由于我们只是添加了TypeConverter，表结构没有变化，所以不需要额外的SQL操作
            }
        }

        private val MIGRATION_2_3 = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 创建分组表
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS conversation_groups (
                        id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        name TEXT NOT NULL,
                        description TEXT NOT NULL DEFAULT '',
                        createdTime INTEGER NOT NULL
                    )
                """)

                // 为聊天消息表添加分组ID字段
                database.execSQL("ALTER TABLE chat_messages ADD COLUMN groupId INTEGER NOT NULL DEFAULT 0")
            }
        }

        private val MIGRATION_3_4 = object : Migration(3, 4) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 为documents表添加RAG相关字段
                database.execSQL("ALTER TABLE documents ADD COLUMN isVectorized INTEGER NOT NULL DEFAULT 0")
                database.execSQL("ALTER TABLE documents ADD COLUMN chunkCount INTEGER NOT NULL DEFAULT 0")
                database.execSQL("ALTER TABLE documents ADD COLUMN processingStatus TEXT NOT NULL DEFAULT 'pending'")
                database.execSQL("ALTER TABLE documents ADD COLUMN errorMessage TEXT NOT NULL DEFAULT ''")
                database.execSQL("ALTER TABLE documents ADD COLUMN extractedText TEXT NOT NULL DEFAULT ''")
                database.execSQL("ALTER TABLE documents ADD COLUMN lastProcessedTime INTEGER NOT NULL DEFAULT 0")
            }
        }
        
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // 数据库创建时的初始化操作
            }
        }
    }
}
