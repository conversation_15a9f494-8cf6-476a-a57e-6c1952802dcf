package com.bei.rag.database.dao

import androidx.room.*
import com.bei.rag.database.entity.ChatMessageEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface ChatMessageDao {
    
    @Query("SELECT * FROM chat_messages WHERE conversationId = :conversationId ORDER BY timestamp ASC")
    fun getMessagesByConversation(conversationId: Long): Flow<List<ChatMessageEntity>>
    
    @Query("SELECT * FROM chat_messages ORDER BY timestamp DESC")
    fun getAllMessages(): Flow<List<ChatMessageEntity>>
    
    @Query("SELECT DISTINCT conversationId FROM chat_messages GROUP BY conversationId ORDER BY MAX(timestamp) DESC")
    suspend fun getAllConversationIds(): List<Long>
    
    @Insert
    suspend fun insertMessage(message: ChatMessageEntity): Long
    
    @Delete
    suspend fun deleteMessage(message: ChatMessageEntity)

    @Query("DELETE FROM chat_messages WHERE id = :messageId")
    suspend fun deleteMessage(messageId: Long)

    @Query("DELETE FROM chat_messages WHERE conversationId = :conversationId")
    suspend fun deleteConversation(conversationId: Long)
    
    @Query("DELETE FROM chat_messages")
    suspend fun deleteAllMessages()
    
    @Query("SELECT COUNT(*) FROM chat_messages")
    suspend fun getMessageCount(): Int
    
    @Query("SELECT * FROM chat_messages WHERE content LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    suspend fun searchMessages(query: String): List<ChatMessageEntity>

    // 分组相关查询
    @Query("SELECT DISTINCT conversationId FROM chat_messages WHERE groupId = :groupId GROUP BY conversationId ORDER BY MAX(timestamp) DESC")
    suspend fun getConversationIdsByGroup(groupId: Long): List<Long>

    @Query("SELECT COUNT(DISTINCT conversationId) FROM chat_messages WHERE groupId = :groupId")
    suspend fun getConversationCountByGroup(groupId: Long): Int

    @Query("UPDATE chat_messages SET groupId = :newGroupId WHERE groupId = :oldGroupId")
    suspend fun updateGroupIdForConversations(oldGroupId: Long, newGroupId: Long)

    @Query("UPDATE chat_messages SET groupId = :groupId WHERE conversationId = :conversationId")
    suspend fun updateConversationGroup(conversationId: Long, groupId: Long)
}
