package com.bei.rag.database.dao

import androidx.room.*
import com.bei.rag.database.entity.ConversationGroupEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface ConversationGroupDao {
    
    @Query("SELECT * FROM conversation_groups ORDER BY createdTime ASC")
    fun getAllGroups(): Flow<List<ConversationGroupEntity>>
    
    @Query("SELECT * FROM conversation_groups WHERE id = :groupId")
    suspend fun getGroupById(groupId: Long): ConversationGroupEntity?
    
    @Insert
    suspend fun insertGroup(group: ConversationGroupEntity): Long
    
    @Update
    suspend fun updateGroup(group: ConversationGroupEntity)
    
    @Delete
    suspend fun deleteGroup(group: ConversationGroupEntity)
    
    @Query("DELETE FROM conversation_groups WHERE id = :groupId")
    suspend fun deleteGroupById(groupId: Long)
    
    @Query("SELECT COUNT(*) FROM conversation_groups")
    suspend fun getGroupCount(): Int
}
