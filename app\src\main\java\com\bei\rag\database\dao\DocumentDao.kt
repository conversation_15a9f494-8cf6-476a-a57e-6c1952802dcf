package com.bei.rag.database.dao

import androidx.room.*
import com.bei.rag.database.entity.DocumentEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface DocumentDao {
    
    @Query("SELECT * FROM documents ORDER BY uploadTime DESC")
    fun getAllDocuments(): Flow<List<DocumentEntity>>

    @Query("SELECT * FROM documents ORDER BY uploadTime DESC")
    suspend fun getAllDocumentsSync(): List<DocumentEntity>
    
    @Query("SELECT * FROM documents WHERE fileType = :fileType ORDER BY uploadTime DESC")
    fun getDocumentsByType(fileType: String): Flow<List<DocumentEntity>>
    
    @Query("SELECT * FROM documents WHERE fileName LIKE '%' || :query || '%' ORDER BY uploadTime DESC")
    suspend fun searchDocuments(query: String): List<DocumentEntity>
    
    @Insert
    suspend fun insertDocument(document: DocumentEntity): Long
    
    @Update
    suspend fun updateDocument(document: DocumentEntity)
    
    @Delete
    suspend fun deleteDocument(document: DocumentEntity)
    
    @Query("DELETE FROM documents WHERE id = :documentId")
    suspend fun deleteDocumentById(documentId: Long)

    @Query("DELETE FROM documents")
    suspend fun deleteAllDocuments()
    
    @Query("SELECT COUNT(*) FROM documents")
    suspend fun getDocumentCount(): Int
    
    @Query("SELECT SUM(fileSize) FROM documents")
    suspend fun getTotalFileSize(): Long
    
    @Query("SELECT * FROM documents WHERE id = :documentId")
    suspend fun getDocumentById(documentId: Long): DocumentEntity?

    @Query("SELECT * FROM documents WHERE id = :documentId")
    suspend fun getDocumentByIdSync(documentId: Long): DocumentEntity?
    
    @Query("SELECT * FROM documents WHERE isVectorized = 0 AND processingStatus = 'pending' ORDER BY uploadTime ASC")
    suspend fun getUnprocessedDocuments(): List<DocumentEntity>
    
    @Query("SELECT * FROM documents WHERE isVectorized = 1 ORDER BY uploadTime DESC")
    suspend fun getVectorizedDocuments(): List<DocumentEntity>
    
    @Query("UPDATE documents SET processingStatus = :status WHERE id = :documentId")
    suspend fun updateProcessingStatus(documentId: Long, status: String)
    
    @Query("UPDATE documents SET isVectorized = :isVectorized, chunkCount = :chunkCount, processingStatus = :status, lastProcessedTime = :time WHERE id = :documentId")
    suspend fun updateVectorizedStatus(documentId: Long, isVectorized: Boolean, chunkCount: Int, status: String, time: Long)
    
    @Query("UPDATE documents SET processingStatus = :status, errorMessage = :errorMessage WHERE id = :documentId")
    suspend fun updateProcessingError(documentId: Long, status: String, errorMessage: String)
    
    @Query("UPDATE documents SET extractedText = :text WHERE id = :documentId")
    suspend fun updateExtractedText(documentId: Long, text: String)
    
    @Query("SELECT COUNT(*) FROM documents WHERE isVectorized = 1")
    suspend fun getVectorizedDocumentCount(): Int
}
