package com.bei.rag.database.dao

import androidx.room.*
import com.bei.rag.database.entity.UserEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {
    
    @Query("SELECT * FROM user_info WHERE id = 1")
    fun getUserInfo(): Flow<UserEntity?>
    
    @Query("SELECT * FROM user_info WHERE id = 1")
    suspend fun getUserInfoSync(): UserEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateUser(user: UserEntity)
    
    @Update
    suspend fun updateUser(user: UserEntity)
    
    @Query("UPDATE user_info SET lastLoginTime = :time WHERE id = 1")
    suspend fun updateLastLoginTime(time: Long)
    
    @Query("UPDATE user_info SET nickname = :nickname WHERE id = 1")
    suspend fun updateNickname(nickname: String)
    
    @Query("UPDATE user_info SET email = :email WHERE id = 1")
    suspend fun updateEmail(email: String)
    
    @Query("UPDATE user_info SET avatar = :avatarPath WHERE id = 1")
    suspend fun updateAvatar(avatarPath: String)

    @Query("DELETE FROM user_info")
    suspend fun clearUserInfo()

    @Query("DELETE FROM user_info WHERE id = 1")
    suspend fun deleteUser()
}
