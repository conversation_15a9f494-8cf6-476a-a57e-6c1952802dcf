package com.bei.rag.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "chat_messages")
data class ChatMessageEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val content: String,
    val isUser: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
    val sources: List<String> = emptyList(),
    val conversationId: Long = 0, // 用于分组对话
    val groupId: Long = 0 // 所属分组ID，0表示默认分组
)


