package com.bei.rag.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "documents")
data class DocumentEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val fileName: String,
    val fileType: String, // pdf, docx, csv, txt等
    val filePath: String,
    val fileSize: Long, // 文件大小（字节）
    val uploadTime: Long = System.currentTimeMillis(),
    val isProcessed: Boolean = false, // 是否已处理
    val summary: String = "", // 文档摘要
    val tags: String = "", // 标签，用逗号分隔
    val isVectorized: Boolean = false, // 是否已向量化
    val chunkCount: Int = 0, // 分块数量
    val processingStatus: String = "pending", // 处理状态: pending, processing, completed, failed
    val errorMessage: String = "", // 错误信息
    val extractedText: String = "", // 提取的文本内容
    val lastProcessedTime: Long = 0 // 最后处理时间
)
