package com.bei.rag.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "user_info")
data class UserEntity(
    @PrimaryKey
    val id: Long = 1, // 单用户系统，固定ID为1
    val nickname: String = "张三",
    val email: String = "zhang<PERSON>@example.com",
    val avatar: String = "", // 头像路径
    val createdTime: Long = System.currentTimeMillis(),
    val lastLoginTime: Long = System.currentTimeMillis()
)
