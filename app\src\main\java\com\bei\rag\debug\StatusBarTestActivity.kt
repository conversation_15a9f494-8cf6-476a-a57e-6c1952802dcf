package com.bei.rag.debug

import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.ScrollView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.bei.rag.R
import com.bei.rag.utils.StatusBarTestHelper

/**
 * 状态栏测试Activity
 * 用于开发和调试阶段测试状态栏功能
 * 仅在Debug版本中可用
 */
class StatusBarTestActivity : AppCompatActivity() {

    private lateinit var testHelper: StatusBarTestHelper
    private lateinit var resultTextView: TextView
    private lateinit var runTestButton: Button
    private lateinit var healthCheckButton: Button
    private lateinit var compatibilityButton: Button
    private lateinit var scrollView: ScrollView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_status_bar_test)
        
        initViews()
        initTestHelper()
        setupClickListeners()
    }

    private fun initViews() {
        resultTextView = findViewById(R.id.tv_test_results)
        runTestButton = findViewById(R.id.btn_run_tests)
        healthCheckButton = findViewById(R.id.btn_health_check)
        compatibilityButton = findViewById(R.id.btn_compatibility_report)
        scrollView = findViewById(R.id.scroll_view)
    }

    private fun initTestHelper() {
        testHelper = StatusBarTestHelper(this)
    }

    private fun setupClickListeners() {
        runTestButton.setOnClickListener {
            runFullTestSuite()
        }
        
        healthCheckButton.setOnClickListener {
            runHealthCheck()
        }
        
        compatibilityButton.setOnClickListener {
            generateCompatibilityReport()
        }
    }

    private fun runFullTestSuite() {
        runTestButton.isEnabled = false
        resultTextView.text = "正在运行测试套件..."
        
        // 在后台线程运行测试
        Thread {
            try {
                val results = testHelper.runFullTestSuite(this)
                val report = testHelper.generateTestReport(results)
                
                runOnUiThread {
                    resultTextView.text = report
                    runTestButton.isEnabled = true
                    scrollView.scrollTo(0, 0)
                }
                
            } catch (e: Exception) {
                Log.e("StatusBarTest", "测试套件运行失败", e)
                runOnUiThread {
                    resultTextView.text = "测试运行失败: ${e.message}"
                    runTestButton.isEnabled = true
                }
            }
        }.start()
    }

    private fun runHealthCheck() {
        healthCheckButton.isEnabled = false
        
        Thread {
            try {
                val isHealthy = testHelper.quickHealthCheck(this)
                val message = if (isHealthy) {
                    "✅ 状态栏功能健康检查通过"
                } else {
                    "❌ 状态栏功能健康检查失败"
                }
                
                runOnUiThread {
                    resultTextView.text = message
                    healthCheckButton.isEnabled = true
                }
                
            } catch (e: Exception) {
                Log.e("StatusBarTest", "健康检查失败", e)
                runOnUiThread {
                    resultTextView.text = "健康检查异常: ${e.message}"
                    healthCheckButton.isEnabled = true
                }
            }
        }.start()
    }

    private fun generateCompatibilityReport() {
        compatibilityButton.isEnabled = false
        resultTextView.text = "正在生成兼容性报告..."
        
        Thread {
            try {
                val report = testHelper.generateCompatibilityReport(this)
                
                runOnUiThread {
                    resultTextView.text = report
                    compatibilityButton.isEnabled = true
                    scrollView.scrollTo(0, 0)
                }
                
            } catch (e: Exception) {
                Log.e("StatusBarTest", "兼容性报告生成失败", e)
                runOnUiThread {
                    resultTextView.text = "兼容性报告生成失败: ${e.message}"
                    compatibilityButton.isEnabled = true
                }
            }
        }.start()
    }
}