package com.bei.rag.fragment

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.PopupMenu
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R
import com.bei.rag.adapter.ConversationGroupAdapter
import com.bei.rag.database.AppDatabase
import com.bei.rag.model.ConversationGroup
import com.bei.rag.repository.ConversationGroupRepository
import kotlinx.coroutines.launch

class ConversationGroupsFragment : Fragment() {

    private lateinit var backButton: ImageButton
    private lateinit var addGroupButton: ImageButton
    private lateinit var defaultGroupLayout: LinearLayout
    private lateinit var defaultGroupCountText: TextView
    private lateinit var groupsRecyclerView: RecyclerView
    
    private lateinit var groupAdapter: ConversationGroupAdapter
    private lateinit var groupRepository: ConversationGroupRepository

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_conversation_groups, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        initRepository()
        setupRecyclerView()
        setupClickListeners()
        loadGroups()
    }

    private fun initViews(view: View) {
        backButton = view.findViewById(R.id.btn_back)
        addGroupButton = view.findViewById(R.id.btn_add_group)
        defaultGroupLayout = view.findViewById(R.id.layout_default_group)
        defaultGroupCountText = view.findViewById(R.id.tv_default_group_count)
        groupsRecyclerView = view.findViewById(R.id.rv_groups)
    }

    private fun initRepository() {
        val database = AppDatabase.getDatabase(requireContext())
        groupRepository = ConversationGroupRepository(
            database.conversationGroupDao(),
            database.chatMessageDao()
        )
    }

    private fun setupRecyclerView() {
        groupAdapter = ConversationGroupAdapter(
            onGroupClick = { group ->
                openGroupConversations(group)
            },
            onMoreClick = { group ->
                showGroupOptionsMenu(group)
            }
        )
        
        groupsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = groupAdapter
        }
    }

    private fun setupClickListeners() {
        backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }
        
        addGroupButton.setOnClickListener {
            showCreateGroupDialog()
        }
        
        defaultGroupLayout.setOnClickListener {
            openDefaultGroupConversations()
        }
    }

    private fun loadGroups() {
        lifecycleScope.launch {
            // 加载默认分组会话数量
            val defaultCount = groupRepository.getDefaultGroupConversationCount()
            defaultGroupCountText.text = "$defaultCount 个会话"
            
            // 加载自定义分组
            groupRepository.getAllGroups().collect { groups ->
                groupAdapter.updateGroups(groups)
            }
        }
    }

    private fun showCreateGroupDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_create_group, null)
        
        val nameEditText = dialogView.findViewById<EditText>(R.id.et_group_name)
        val descriptionEditText = dialogView.findViewById<EditText>(R.id.et_group_description)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val createButton = dialogView.findViewById<Button>(R.id.btn_create)
        
        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .create()
        
        cancelButton.setOnClickListener {
            dialog.dismiss()
        }
        
        createButton.setOnClickListener {
            val name = nameEditText.text.toString().trim()
            val description = descriptionEditText.text.toString().trim()
            
            if (name.isEmpty()) {
                Toast.makeText(requireContext(), "请输入分组名称", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            createGroup(name, description)
            dialog.dismiss()
        }
        
        dialog.show()
    }

    private fun createGroup(name: String, description: String) {
        lifecycleScope.launch {
            try {
                groupRepository.createGroup(name, description)
                Toast.makeText(requireContext(), "分组创建成功", Toast.LENGTH_SHORT).show()
                loadGroups()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "创建失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showGroupOptionsMenu(group: ConversationGroup) {
        val popupMenu = PopupMenu(requireContext(), groupsRecyclerView)
        popupMenu.menuInflater.inflate(R.menu.group_options_menu, popupMenu.menu)
        
        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.action_rename -> {
                    showRenameGroupDialog(group)
                    true
                }
                R.id.action_delete -> {
                    showDeleteGroupDialog(group)
                    true
                }
                else -> false
            }
        }
        
        popupMenu.show()
    }

    private fun showRenameGroupDialog(group: ConversationGroup) {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_create_group, null)

        val nameEditText = dialogView.findViewById<EditText>(R.id.et_group_name)
        val descriptionEditText = dialogView.findViewById<EditText>(R.id.et_group_description)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val createButton = dialogView.findViewById<Button>(R.id.btn_create)

        // 预填充当前信息
        nameEditText.setText(group.name)
        descriptionEditText.setText(group.description)

        // 修改按钮文字
        createButton.text = "确认修改"

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .create()

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        createButton.setOnClickListener {
            val newName = nameEditText.text.toString().trim()
            val newDescription = descriptionEditText.text.toString().trim()

            if (newName.isEmpty()) {
                Toast.makeText(requireContext(), "请输入分组名称", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            renameGroup(group, newName, newDescription)
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun showDeleteGroupDialog(group: ConversationGroup) {
        AlertDialog.Builder(requireContext())
            .setTitle("删除分组")
            .setMessage("确定要删除分组「${group.name}」吗？\n\n该分组下的所有会话将移动到默认分组。")
            .setPositiveButton("删除") { _, _ ->
                deleteGroup(group)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun renameGroup(group: ConversationGroup, newName: String, newDescription: String) {
        lifecycleScope.launch {
            try {
                val updatedGroup = group.copy(
                    name = newName,
                    description = newDescription
                )
                groupRepository.updateGroup(updatedGroup)
                Toast.makeText(requireContext(), "分组已重命名", Toast.LENGTH_SHORT).show()
                loadGroups()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "重命名失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun deleteGroup(group: ConversationGroup) {
        lifecycleScope.launch {
            try {
                groupRepository.deleteGroup(group.id)
                Toast.makeText(requireContext(), "分组已删除", Toast.LENGTH_SHORT).show()
                loadGroups()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "删除失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun openGroupConversations(group: ConversationGroup) {
        val fragment = ConversationListFragment.newInstance(group.id)
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .addToBackStack(null)
            .commit()
    }

    private fun openDefaultGroupConversations() {
        val fragment = ConversationListFragment.newInstance(0) // 0 表示默认分组
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .addToBackStack(null)
            .commit()
    }

    companion object {
        fun newInstance() = ConversationGroupsFragment()
    }
}
