package com.bei.rag.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R
import com.bei.rag.adapter.ConversationAdapter
import com.bei.rag.database.AppDatabase
import com.bei.rag.model.Conversation
import com.bei.rag.repository.ChatRepository
import kotlinx.coroutines.launch

class ConversationListFragment : Fragment() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var backButton: ImageButton
    private lateinit var newChatButton: ImageButton
    private lateinit var titleTextView: TextView
    private lateinit var conversationAdapter: ConversationAdapter
    private lateinit var chatRepository: ChatRepository

    private var groupId: Long = 0 // 0表示默认分组，其他值表示特定分组

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_conversation_list, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 获取传入的分组ID
        groupId = arguments?.getLong(ARG_GROUP_ID, 0) ?: 0

        initDatabase()
        setupUI(view)
        setupListeners()
        updateTitle()
        loadConversations()
    }

    private fun initDatabase() {
        val database = AppDatabase.getDatabase(requireContext())
        chatRepository = ChatRepository(database.chatMessageDao())
    }

    private fun setupUI(view: View) {
        recyclerView = view.findViewById(R.id.rv_conversations)
        backButton = view.findViewById(R.id.btn_back)
        newChatButton = view.findViewById(R.id.btn_new_chat)
        titleTextView = view.findViewById(R.id.tv_title)

        conversationAdapter = ConversationAdapter(
            onConversationClick = { conversation ->
                openConversation(conversation.id)
            },
            onDeleteClick = { conversation ->
                showDeleteConfirmDialog(conversation)
            }
        )

        recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = conversationAdapter
        }
    }

    private fun setupListeners() {
        backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        newChatButton.setOnClickListener {
            createNewConversation()
        }
    }

    private fun loadConversations() {
        lifecycleScope.launch {
            // 根据分组ID获取会话列表
            val conversationIds = if (groupId == 0L) {
                chatRepository.getConversationIdsByGroup(0)
            } else {
                chatRepository.getConversationIdsByGroup(groupId)
            }
            val conversations = mutableListOf<Conversation>()

            for (id in conversationIds) {
                val messages = chatRepository.getMessagesByConversationSync(id)
                if (messages.isNotEmpty()) {
                    val lastMessage = messages.last()
                    val firstMessage = messages.first()
                    conversations.add(
                        Conversation(
                            id = id,
                            title = generateConversationTitle(firstMessage.content),
                            lastMessage = lastMessage.content,
                            lastMessageTime = lastMessage.timestamp,
                            messageCount = messages.size
                        )
                    )
                }
            }

            conversationAdapter.updateConversations(conversations)
        }
    }

    private fun updateTitle() {
        lifecycleScope.launch {
            if (groupId == 0L) {
                titleTextView.text = "默认分组"
            } else {
                // 获取分组名称
                val database = AppDatabase.getDatabase(requireContext())
                val groupRepository = com.bei.rag.repository.ConversationGroupRepository(
                    database.conversationGroupDao(),
                    database.chatMessageDao()
                )
                val group = groupRepository.getGroupById(groupId)
                titleTextView.text = group?.name ?: "分组会话"
            }
        }
    }

    private fun generateConversationTitle(firstMessage: String): String {
        return if (firstMessage.length > 20) {
            firstMessage.take(20) + "..."
        } else {
            firstMessage
        }
    }

    private fun createNewConversation() {
        val newConversationId = System.currentTimeMillis()
        openConversation(newConversationId, groupId)
    }

    private fun openConversation(conversationId: Long, groupId: Long = this.groupId) {
        val chatFragment = ChatFragment.newInstance(conversationId, groupId)
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, chatFragment)
            .addToBackStack(null)
            .commit()
    }

    private fun showDeleteConfirmDialog(conversation: Conversation) {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("删除会话")
        builder.setMessage("确定要删除会话「${conversation.title}」吗？\n\n此操作将删除该会话的所有聊天记录，且不可恢复。")

        builder.setPositiveButton("删除") { _, _ ->
            deleteConversation(conversation)
        }
        builder.setNegativeButton("取消", null)

        // 设置删除按钮为红色
        val dialog = builder.create()
        dialog.show()
        dialog.getButton(AlertDialog.BUTTON_POSITIVE)?.setTextColor(
            requireContext().getColor(R.color.ios_red)
        )
    }

    private fun deleteConversation(conversation: Conversation) {
        lifecycleScope.launch {
            try {
                // 删除会话及其所有消息
                chatRepository.deleteConversation(conversation.id)

                // 显示成功提示
                Toast.makeText(requireContext(), "会话已删除", Toast.LENGTH_SHORT).show()

                // 重新加载会话列表
                loadConversations()

            } catch (e: Exception) {
                Toast.makeText(requireContext(), "删除失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    companion object {
        private const val ARG_GROUP_ID = "group_id"

        fun newInstance(groupId: Long = 0) = ConversationListFragment().apply {
            arguments = Bundle().apply {
                putLong(ARG_GROUP_ID, groupId)
            }
        }
    }
}
