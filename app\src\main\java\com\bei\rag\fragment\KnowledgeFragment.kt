package com.bei.rag.fragment

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup

import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R
import com.bei.rag.adapter.DocumentAdapter
import com.bei.rag.database.AppDatabase
import com.bei.rag.database.entity.DocumentEntity
import com.bei.rag.repository.DocumentRepository
import com.bei.rag.service.*
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream

class KnowledgeFragment : Fragment() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var uploadButton: android.widget.Button
    private lateinit var backButton: android.widget.ImageButton
    private lateinit var processButton: android.widget.Button
    private lateinit var statusText: android.widget.TextView
    private lateinit var progressBar: android.widget.ProgressBar
    private lateinit var documentAdapter: DocumentAdapter
    private lateinit var documentRepository: DocumentRepository
    private lateinit var knowledgeBaseManager: KnowledgeBaseManager

    private var allDocuments = listOf<DocumentEntity>()
    private var filteredDocuments = listOf<DocumentEntity>()
    
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleFileSelection(uri)
            }
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_knowledge, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initDatabase()
        setupUI(view)
        setupListeners()
        loadDocuments()
    }
    
    private fun initDatabase() {
        val database = AppDatabase.getDatabase(requireContext())
        documentRepository = DocumentRepository(database.documentDao())
        
        // 初始化Supabase配置
        try {
            SupabaseConfig.initialize(requireContext())
        } catch (e: Exception) {
            // Supabase初始化失败，继续使用本地功能
            android.util.Log.w("KnowledgeFragment", "Supabase初始化失败: ${e.message}")
        }
        
        // 初始化知识库管理器
        val embeddingService = SiliconFlowEmbeddingService()
        val parsingManager = DocumentParsingManager(requireContext())
        val semanticChunker = SemanticChunker()
        
        // 初始化Supabase相关服务（如果可用）
        val supabaseDocumentService = if (SupabaseConfig.isInitialized) {
            try {
                val vectorService = SupabaseVectorService()
                SupabaseDocumentService(
                    context = requireContext(),
                    localDocumentDao = database.documentDao(),
                    vectorService = vectorService,
                    embeddingService = embeddingService,
                    semanticChunker = semanticChunker
                )
            } catch (e: Exception) {
                android.util.Log.w("KnowledgeFragment", "Supabase服务初始化失败: ${e.message}")
                null
            }
        } else null
        
        val vectorSearchService = if (SupabaseConfig.isInitialized && supabaseDocumentService != null) {
            try {
                VectorSearchService(
                    context = requireContext(),
                    localDocumentDao = database.documentDao(),
                    vectorService = SupabaseVectorService(),
                    embeddingService = embeddingService
                )
            } catch (e: Exception) {
                android.util.Log.w("KnowledgeFragment", "向量搜索服务初始化失败: ${e.message}")
                null
            }
        } else null
        
        knowledgeBaseManager = KnowledgeBaseManager(
            context = requireContext(),
            documentDao = database.documentDao(),
            embeddingService = embeddingService,
            parsingManager = parsingManager,
            semanticChunker = semanticChunker,
            supabaseDocumentService = supabaseDocumentService,
            vectorSearchService = vectorSearchService
        )
    }
    
    private fun setupUI(view: View) {
        recyclerView = view.findViewById(R.id.rv_documents)
        uploadButton = view.findViewById(R.id.btn_upload)
        backButton = view.findViewById(R.id.btn_back)
        processButton = view.findViewById(R.id.btn_process_documents)
        statusText = view.findViewById(R.id.tv_processing_status)
        progressBar = view.findViewById(R.id.pb_processing)

        // 设置RecyclerView
        documentAdapter = DocumentAdapter(
            onDocumentClick = { document ->
                handleDocumentClick(document)
            },
            onDeleteClick = { document ->
                handleDocumentDelete(document)
            },
            onProcessClick = { document ->
                handleDocumentProcess(document)
            }
        )

        recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = documentAdapter
        }
    }
    
    private fun setupListeners() {
        uploadButton.setOnClickListener {
            openFilePicker()
        }

        backButton.setOnClickListener {
            // 返回到聊天页面
            parentFragmentManager.popBackStack()
        }

        processButton.setOnClickListener {
            processAllUnprocessedDocuments()
        }
        
        // 同步相关功能可以通过菜单或其他方式触发
        // 暂时注释掉不存在的按钮引用
        
        // 长按处理按钮显示更多选项
        processButton.setOnLongClickListener {
            showProcessingOptions()
            true
        }
    }
    
    private fun openFilePicker() {
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "*/*"
            addCategory(Intent.CATEGORY_OPENABLE)
            putExtra(Intent.EXTRA_MIME_TYPES, arrayOf(
                "application/pdf",
                "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "text/csv",
                "text/plain"
            ))
        }
        filePickerLauncher.launch(Intent.createChooser(intent, "选择文档"))
    }
    
    private fun handleFileSelection(uri: Uri) {
        lifecycleScope.launch {
            try {
                val fileName = getFileName(uri) ?: "unknown_file"
                val fileType = getFileType(fileName)
                val fileSize = getFileSize(uri)
                
                // 复制文件到应用内部存储
                val internalFile = copyFileToInternal(uri, fileName)
                
                // 保存到数据库
                val document = DocumentEntity(
                    fileName = fileName,
                    fileType = fileType,
                    filePath = internalFile.absolutePath,
                    fileSize = fileSize
                )
                
                documentRepository.insertDocument(document)
                Toast.makeText(requireContext(), "文档上传成功", Toast.LENGTH_SHORT).show()

                // 重新加载文档列表
                loadDocuments()

            } catch (e: Exception) {
                Toast.makeText(requireContext(), "文档上传失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun getFileName(uri: Uri): String? {
        val cursor = requireContext().contentResolver.query(uri, null, null, null, null)
        return cursor?.use {
            val nameIndex = it.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
            it.moveToFirst()
            it.getString(nameIndex)
        }
    }
    
    private fun getFileType(fileName: String): String {
        return when {
            fileName.endsWith(".pdf", true) -> "pdf"
            fileName.endsWith(".doc", true) || fileName.endsWith(".docx", true) -> "docx"
            fileName.endsWith(".csv", true) -> "csv"
            fileName.endsWith(".txt", true) -> "txt"
            else -> "unknown"
        }
    }
    
    private fun getFileSize(uri: Uri): Long {
        val cursor = requireContext().contentResolver.query(uri, null, null, null, null)
        return cursor?.use {
            val sizeIndex = it.getColumnIndex(android.provider.OpenableColumns.SIZE)
            it.moveToFirst()
            it.getLong(sizeIndex)
        } ?: 0L
    }
    
    private fun copyFileToInternal(uri: Uri, fileName: String): File {
        val inputStream = requireContext().contentResolver.openInputStream(uri)
        val outputFile = File(requireContext().filesDir, "documents/$fileName")
        outputFile.parentFile?.mkdirs()
        
        inputStream?.use { input ->
            FileOutputStream(outputFile).use { output ->
                input.copyTo(output)
            }
        }
        
        return outputFile
    }
    
    private fun loadDocuments() {
        lifecycleScope.launch {
            documentRepository.getAllDocuments().collect { documents ->
                allDocuments = documents
                documentAdapter.updateDocuments(documents)
            }
        }
    }

    private fun handleDocumentClick(document: DocumentEntity) {
        // 显示文档详情
        showDocumentDetails(document)
    }

    private fun handleDocumentDelete(document: DocumentEntity) {
        // 删除文档
        lifecycleScope.launch {
            try {
                documentRepository.deleteDocument(document)
                Toast.makeText(requireContext(), "文档已删除", Toast.LENGTH_SHORT).show()
                loadDocuments()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "删除失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun handleDocumentProcess(document: DocumentEntity) {
        // 处理单个文档
        lifecycleScope.launch {
            try {
                statusText.text = "正在处理文档：${document.fileName}"
                progressBar.visibility = View.VISIBLE
                
                val result = knowledgeBaseManager.processDocument(document.id)
                result.fold(
                    onSuccess = { processingResult ->
                        statusText.text = "处理完成：${document.fileName}，生成 ${processingResult.chunkCount} 个文档块"
                        Toast.makeText(requireContext(), "文档处理成功", Toast.LENGTH_SHORT).show()
                        loadDocuments() // 刷新列表
                    },
                    onFailure = { exception ->
                        statusText.text = "处理失败：${exception.message}"
                        Toast.makeText(requireContext(), "文档处理失败：${exception.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                statusText.text = "处理异常：${e.message}"
                Toast.makeText(requireContext(), "处理异常：${e.message}", Toast.LENGTH_SHORT).show()
            } finally {
                progressBar.visibility = View.GONE
            }
        }
    }

    private fun processAllUnprocessedDocuments() {
        lifecycleScope.launch {
            try {
                statusText.text = "正在批量处理未处理的文档..."
                progressBar.visibility = View.VISIBLE
                processButton.isEnabled = false
                
                val result = knowledgeBaseManager.processUnprocessedDocuments()
                result.fold(
                    onSuccess = { results ->
                        statusText.text = "批量处理完成，共处理 ${results.size} 个文档"
                        Toast.makeText(requireContext(), "批量处理完成", Toast.LENGTH_SHORT).show()
                        loadDocuments() // 刷新列表
                    },
                    onFailure = { exception ->
                        statusText.text = "批量处理失败：${exception.message}"
                        Toast.makeText(requireContext(), "批量处理失败：${exception.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                statusText.text = "处理异常：${e.message}"
                Toast.makeText(requireContext(), "处理异常：${e.message}", Toast.LENGTH_SHORT).show()
            } finally {
                progressBar.visibility = View.GONE
                processButton.isEnabled = true
            }
        }
    }

    private fun showDocumentDetails(document: DocumentEntity) {
        val details = StringBuilder()
        details.appendLine("文档名称：${document.fileName}")
        details.appendLine("文件类型：${document.fileType}")
        details.appendLine("文件大小：${formatFileSize(document.fileSize)}")
        details.appendLine("上传时间：${formatTime(document.uploadTime)}")
        details.appendLine("处理状态：${getStatusText(document.processingStatus)}")
        details.appendLine("是否已向量化：${if (document.isVectorized) "是" else "否"}")
        if (document.chunkCount > 0) {
            details.appendLine("文档块数量：${document.chunkCount}")
        }
        if (document.errorMessage.isNotEmpty()) {
            details.appendLine("错误信息：${document.errorMessage}")
        }

        android.app.AlertDialog.Builder(requireContext())
            .setTitle("文档详情")
            .setMessage(details.toString())
            .setPositiveButton("确定", null)
            .show()
    }

    private fun formatFileSize(sizeInBytes: Long): String {
        return when {
            sizeInBytes < 1024 -> "${sizeInBytes}B"
            sizeInBytes < 1024 * 1024 -> "${sizeInBytes / 1024}KB"
            else -> "${sizeInBytes / (1024 * 1024)}MB"
        }
    }

    private fun formatTime(timestamp: Long): String {
        val date = java.util.Date(timestamp)
        val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
        return formatter.format(date)
    }

    private fun getStatusText(status: String): String {
        return when (status) {
            "pending" -> "待处理"
            "processing" -> "处理中"
            "completed" -> "已完成"
            "failed" -> "处理失败"
            else -> status
        }
    }

    /**
     * 同步到云端
     */
    private fun syncToCloud() {
        lifecycleScope.launch {
            try {
                statusText.text = "正在同步到云端..."
                progressBar.visibility = View.VISIBLE
                
                knowledgeBaseManager.syncToCloud().fold(
                    onSuccess = { syncStatus ->
                        statusText.text = if (syncStatus.isOnline) {
                            "云端同步完成，待上传：${syncStatus.pendingUploads} 个"
                        } else {
                            "当前离线，无法同步到云端"
                        }
                        Toast.makeText(requireContext(), "同步完成", Toast.LENGTH_SHORT).show()
                    },
                    onFailure = { exception ->
                        statusText.text = "同步失败：${exception.message}"
                        Toast.makeText(requireContext(), "同步失败：${exception.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                statusText.text = "同步异常：${e.message}"
                Toast.makeText(requireContext(), "同步异常：${e.message}", Toast.LENGTH_SHORT).show()
            } finally {
                progressBar.visibility = View.GONE
            }
        }
    }
    
    /**
     * 从云端同步
     */
    private fun syncFromCloud() {
        lifecycleScope.launch {
            try {
                statusText.text = "正在从云端同步..."
                progressBar.visibility = View.VISIBLE
                
                knowledgeBaseManager.syncFromCloud().fold(
                    onSuccess = { syncStatus ->
                        statusText.text = if (syncStatus.isOnline) {
                            "本地同步完成，待下载：${syncStatus.pendingDownloads} 个"
                        } else {
                            "当前离线，无法从云端同步"
                        }
                        Toast.makeText(requireContext(), "同步完成", Toast.LENGTH_SHORT).show()
                        loadDocuments() // 刷新文档列表
                    },
                    onFailure = { exception ->
                        statusText.text = "同步失败：${exception.message}"
                        Toast.makeText(requireContext(), "同步失败：${exception.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                statusText.text = "同步异常：${e.message}"
                Toast.makeText(requireContext(), "同步异常：${e.message}", Toast.LENGTH_SHORT).show()
            } finally {
                progressBar.visibility = View.GONE
            }
        }
    }
    
    /**
     * 显示处理选项菜单
     */
    private fun showProcessingOptions() {
        val options = arrayOf(
            "批量处理未处理文档",
            "同步到云端",
            "从云端同步",
            "查看同步状态",
            "测试向量搜索"
        )
        
        android.app.AlertDialog.Builder(requireContext())
            .setTitle("处理选项")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> processAllUnprocessedDocuments()
                    1 -> syncToCloud()
                    2 -> syncFromCloud()
                    3 -> showSyncStatus()
                    4 -> testVectorSearch()
                }
            }
            .show()
    }
    
    /**
     * 显示同步状态
     */
    private fun showSyncStatus() {
        lifecycleScope.launch {
            try {
                knowledgeBaseManager.getSyncStatus().fold(
                    onSuccess = { syncStatus ->
                        val statusMessage = """
                            在线状态：${if (syncStatus.isOnline) "在线" else "离线"}
                            最后同步时间：${if (syncStatus.lastSyncTime > 0) formatTime(syncStatus.lastSyncTime) else "从未同步"}
                            待上传文档：${syncStatus.pendingUploads} 个
                            待下载文档：${syncStatus.pendingDownloads} 个
                            同步进行中：${if (syncStatus.syncInProgress) "是" else "否"}
                        """.trimIndent()
                        
                        android.app.AlertDialog.Builder(requireContext())
                            .setTitle("同步状态")
                            .setMessage(statusMessage)
                            .setPositiveButton("确定", null)
                            .show()
                    },
                    onFailure = { exception ->
                        Toast.makeText(requireContext(), "获取同步状态失败：${exception.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "获取同步状态异常：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    /**
     * 测试向量搜索
     */
    private fun testVectorSearch() {
        lifecycleScope.launch {
            try {
                statusText.text = "正在测试向量搜索..."
                progressBar.visibility = View.VISIBLE
                
                val result = knowledgeBaseManager.searchKnowledgeBase("测试", 3)
                result.fold(
                    onSuccess = { searchResults ->
                        val resultMessage = if (searchResults.isNotEmpty()) {
                            "搜索测试成功，找到 ${searchResults.size} 个结果：\n" +
                            searchResults.take(2).joinToString("\n") { "• ${it.fileName}" }
                        } else {
                            "搜索测试成功，但未找到相关结果"
                        }
                        
                        statusText.text = resultMessage
                        Toast.makeText(requireContext(), "搜索测试完成", Toast.LENGTH_SHORT).show()
                    },
                    onFailure = { exception ->
                        statusText.text = "搜索测试失败：${exception.message}"
                        Toast.makeText(requireContext(), "搜索测试失败：${exception.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                statusText.text = "搜索测试异常：${e.message}"
                Toast.makeText(requireContext(), "搜索测试异常：${e.message}", Toast.LENGTH_SHORT).show()
            } finally {
                progressBar.visibility = View.GONE
            }
        }
    }

    companion object {
        fun newInstance() = KnowledgeFragment()
    }
}
