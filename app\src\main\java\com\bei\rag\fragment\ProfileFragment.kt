package com.bei.rag.fragment

import android.app.AlertDialog
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bei.rag.R
import com.bei.rag.database.AppDatabase
import com.bei.rag.database.entity.UserEntity
import com.bei.rag.repository.UserRepository
import kotlinx.coroutines.launch

class ProfileFragment : Fragment() {
    
    private lateinit var avatarImageView: ImageView
    private lateinit var nicknameTextView: TextView
    private lateinit var emailTextView: TextView
    private lateinit var editProfileLayout: LinearLayout
    private lateinit var notificationsLayout: LinearLayout
    private lateinit var clearDataLayout: LinearLayout
    private lateinit var aboutLayout: LinearLayout
    private lateinit var logoutButton: Button

    private lateinit var userRepository: UserRepository
    private lateinit var sharedPreferences: SharedPreferences
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_profile, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initDatabase()
        setupUI(view)
        setupListeners()
        loadUserInfo()
    }
    
    private fun initDatabase() {
        val database = AppDatabase.getDatabase(requireContext())
        userRepository = UserRepository(database.userDao())
        sharedPreferences = requireContext().getSharedPreferences("app_settings", Context.MODE_PRIVATE)
    }

    private fun setupUI(view: View) {
        avatarImageView = view.findViewById(R.id.iv_avatar)
        nicknameTextView = view.findViewById(R.id.tv_nickname)
        emailTextView = view.findViewById(R.id.tv_email)
        editProfileLayout = view.findViewById(R.id.layout_edit_profile)
        notificationsLayout = view.findViewById(R.id.layout_notifications)
        clearDataLayout = view.findViewById(R.id.layout_clear_data)
        aboutLayout = view.findViewById(R.id.layout_about)
        logoutButton = view.findViewById(R.id.btn_logout)
    }
    
    private fun setupListeners() {
        editProfileLayout.setOnClickListener {
            showEditProfileDialog()
        }

        notificationsLayout.setOnClickListener {
            showNotificationSettingsDialog()
        }

        clearDataLayout.setOnClickListener {
            showClearDataDialog()
        }

        aboutLayout.setOnClickListener {
            showAboutDialog()
        }

        logoutButton.setOnClickListener {
            showLogoutDialog()
        }
    }
    
    private fun loadUserInfo() {
        lifecycleScope.launch {
            userRepository.getUserInfo().collect { user ->
                if (user != null) {
                    updateUI(user)
                } else {
                    // 创建默认用户
                    val defaultUser = UserEntity()
                    userRepository.insertOrUpdateUser(defaultUser)
                }
            }
        }
    }
    
    private fun updateUI(user: UserEntity) {
        nicknameTextView.text = user.nickname
        emailTextView.text = user.email
        // 这里可以加载头像
    }
    
    private fun showEditProfileDialog() {
        val dialogView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_edit_profile, null)
        val nicknameEdit = dialogView.findViewById<EditText>(R.id.et_nickname)
        val emailEdit = dialogView.findViewById<EditText>(R.id.et_email)

        // 填充当前信息
        nicknameEdit.setText(nicknameTextView.text)
        emailEdit.setText(emailTextView.text)

        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("编辑个人资料")
        builder.setView(dialogView)
        builder.setPositiveButton("保存") { _, _ ->
            val newNickname = nicknameEdit.text.toString().trim()
            val newEmail = emailEdit.text.toString().trim()

            if (newNickname.isNotEmpty() && newEmail.isNotEmpty()) {
                updateUserProfile(newNickname, newEmail)
            } else {
                Toast.makeText(requireContext(), "请填写完整信息", Toast.LENGTH_SHORT).show()
            }
        }
        builder.setNegativeButton("取消", null)
        builder.show()
    }

    private fun updateUserProfile(nickname: String, email: String) {
        lifecycleScope.launch {
            try {
                val userEntity = UserEntity(nickname = nickname, email = email)
                userRepository.insertOrUpdateUser(userEntity)
                Toast.makeText(requireContext(), "个人资料更新成功", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "更新失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showNotificationSettingsDialog() {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("通知设置")

        val options = arrayOf("消息通知", "系统通知", "声音提醒")
        val checkedItems = booleanArrayOf(
            sharedPreferences.getBoolean("message_notifications", true),
            sharedPreferences.getBoolean("system_notifications", true),
            sharedPreferences.getBoolean("sound_notifications", true)
        )

        builder.setMultiChoiceItems(options, checkedItems) { _, which, isChecked ->
            checkedItems[which] = isChecked
        }

        builder.setPositiveButton("保存") { _, _ ->
            with(sharedPreferences.edit()) {
                putBoolean("message_notifications", checkedItems[0])
                putBoolean("system_notifications", checkedItems[1])
                putBoolean("sound_notifications", checkedItems[2])
                apply()
            }
            Toast.makeText(requireContext(), "通知设置已保存", Toast.LENGTH_SHORT).show()
        }
        builder.setNegativeButton("取消", null)
        builder.show()
    }
    
    private fun showClearDataDialog() {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("清除数据")
        builder.setMessage("确定要清除所有聊天记录和文档吗？此操作不可恢复。")
        builder.setPositiveButton("确定") { _, _ ->
            clearAllData()
        }
        builder.setNegativeButton("取消", null)
        builder.show()
    }
    
    private fun showAboutDialog() {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("关于我们")
        builder.setMessage("RAG Android v1.0\n\n基于检索增强生成技术的智能聊天应用")
        builder.setPositiveButton("确定", null)
        builder.show()
    }
    
    private fun showLogoutDialog() {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("退出登录")
        builder.setMessage("确定要退出登录吗？")
        builder.setPositiveButton("确定") { _, _ ->
            // 这里可以添加退出登录逻辑
            Toast.makeText(requireContext(), "已退出登录", Toast.LENGTH_SHORT).show()
        }
        builder.setNegativeButton("取消", null)
        builder.show()
    }
    
    private fun clearAllData() {
        lifecycleScope.launch {
            try {
                val database = AppDatabase.getDatabase(requireContext())
                database.chatMessageDao().deleteAllMessages()
                // 这里可以添加清除文档的逻辑
                Toast.makeText(requireContext(), "数据清除成功", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "数据清除失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    companion object {
        fun newInstance() = ProfileFragment()
    }
}
