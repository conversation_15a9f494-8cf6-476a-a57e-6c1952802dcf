package com.bei.rag.fragment

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.widget.SwitchCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bei.rag.R
import com.bei.rag.MainActivity
import com.bei.rag.database.AppDatabase
import com.bei.rag.utils.DataManager
import com.bei.rag.utils.TtsConfig
import kotlinx.coroutines.launch

class SettingsFragment : Fragment() {

    private lateinit var backButton: ImageButton
    private lateinit var settingTheme: LinearLayout
    private lateinit var settingVoice: LinearLayout
    private lateinit var settingAutoPlay: LinearLayout
    private lateinit var switchAutoPlay: SwitchCompat
    private lateinit var settingSystem: LinearLayout
    private lateinit var logoutButton: LinearLayout
    
    private lateinit var dataManager: DataManager
    private lateinit var ttsConfig: TtsConfig

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        initDatabase()
        setupListeners()
        loadSettings()
    }

    private fun initViews(view: View) {
        backButton = view.findViewById(R.id.btn_back)
        settingTheme = view.findViewById(R.id.setting_theme)
        settingVoice = view.findViewById(R.id.setting_voice)
        settingAutoPlay = view.findViewById(R.id.setting_auto_play)
        switchAutoPlay = view.findViewById(R.id.switch_auto_play)
        settingSystem = view.findViewById(R.id.setting_system)
        logoutButton = view.findViewById(R.id.btn_logout)
    }

    private fun initDatabase() {
        dataManager = DataManager(requireContext())
        ttsConfig = TtsConfig(requireContext())
    }

    private fun setupListeners() {
        backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        settingTheme.setOnClickListener {
            showThemeSettings()
        }

        settingVoice.setOnClickListener {
            navigateToVoiceEngineSettings()
        }

        // 自动播放设置
        settingAutoPlay.setOnClickListener {
            switchAutoPlay.toggle()
        }

        switchAutoPlay.setOnCheckedChangeListener { _, isChecked ->
            ttsConfig.setAutoPlayAiReply(isChecked)
            val message = if (isChecked) {
                getString(R.string.auto_play_enabled)
            } else {
                getString(R.string.auto_play_disabled)
            }
            Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
        }

        settingSystem.setOnClickListener {
            showSystemSettings()
        }

        logoutButton.setOnClickListener {
            showLogoutDialog()
        }
    }



    private fun showThemeSettings() {
        val themeSettingsFragment = ThemeSettingsFragment.newInstance()
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, themeSettingsFragment)
            .addToBackStack(null)
            .commit()
    }

    private fun showSystemSettings() {
        val systemSettingsFragment = SystemSettingsFragment.newInstance()
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, systemSettingsFragment)
            .addToBackStack(null)
            .commit()
    }

    private fun loadSettings() {
        // 加载自动播放设置
        switchAutoPlay.isChecked = ttsConfig.getAutoPlayAiReply()
    }

    private fun navigateToVoiceEngineSettings() {
        val voiceEngineSettingsFragment = VoiceEngineSettingsFragment.newInstance()
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, voiceEngineSettingsFragment)
            .addToBackStack(null)
            .commit()
    }



    private fun showLogoutDialog() {
        val options = arrayOf("退出登录", "退出登录并清除数据")
        
        AlertDialog.Builder(requireContext())
            .setTitle("退出登录")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> logout(false)
                    1 -> logout(true)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }



    private fun logout(clearData: Boolean) {
        lifecycleScope.launch {
            try {
                if (clearData) {
                    // 清除数据逻辑
                }

                // 跳转到主页面
                val intent = Intent(requireContext(), MainActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                startActivity(intent)
                requireActivity().finish()

            } catch (e: Exception) {
                Toast.makeText(requireContext(), "退出失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    companion object {
        private const val TAG = "SettingsFragment"
        fun newInstance() = SettingsFragment()
    }
}
