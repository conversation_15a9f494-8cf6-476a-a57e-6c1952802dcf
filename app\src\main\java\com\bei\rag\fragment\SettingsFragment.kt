package com.bei.rag.fragment

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.widget.SwitchCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bei.rag.R
import com.bei.rag.MainActivity
import com.bei.rag.database.AppDatabase
import com.bei.rag.utils.DataManager
import com.bei.rag.utils.TtsConfig
import kotlinx.coroutines.launch

class SettingsFragment : Fragment() {

    private lateinit var backButton: ImageButton
    private lateinit var settingTheme: LinearLayout
    private lateinit var settingFontSize: LinearLayout
    private lateinit var settingLanguage: LinearLayout
    private lateinit var settingVoice: LinearLayout
    private lateinit var settingAutoPlay: LinearLayout
    private lateinit var switchAutoPlay: SwitchCompat
    private lateinit var settingAccount: LinearLayout
    private lateinit var settingData: LinearLayout
    private lateinit var settingSystem: LinearLayout
    private lateinit var settingHelp: LinearLayout
    private lateinit var settingFeedback: LinearLayout
    private lateinit var settingAboutApp: LinearLayout
    private lateinit var logoutButton: LinearLayout
    
    private lateinit var dataManager: DataManager
    private lateinit var ttsConfig: TtsConfig

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        initDatabase()
        setupListeners()
        loadSettings()
    }

    private fun initViews(view: View) {
        backButton = view.findViewById(R.id.btn_back)
        settingTheme = view.findViewById(R.id.setting_theme)
        settingFontSize = view.findViewById(R.id.setting_font_size)
        settingLanguage = view.findViewById(R.id.setting_language)
        settingVoice = view.findViewById(R.id.setting_voice)
        settingAutoPlay = view.findViewById(R.id.setting_auto_play)
        switchAutoPlay = view.findViewById(R.id.switch_auto_play)
        settingAccount = view.findViewById(R.id.setting_account)
        settingData = view.findViewById(R.id.setting_data)
        settingSystem = view.findViewById(R.id.setting_system)
        settingHelp = view.findViewById(R.id.setting_help)
        settingFeedback = view.findViewById(R.id.setting_feedback)
        settingAboutApp = view.findViewById(R.id.setting_about_app)
        logoutButton = view.findViewById(R.id.btn_logout)
    }

    private fun initDatabase() {
        dataManager = DataManager(requireContext())
        ttsConfig = TtsConfig(requireContext())
    }

    private fun setupListeners() {
        backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        settingTheme.setOnClickListener {
            showThemeSettings()
        }

        settingFontSize.setOnClickListener {
            Toast.makeText(requireContext(), "字号设置功能开发中", Toast.LENGTH_SHORT).show()
        }

        settingLanguage.setOnClickListener {
            Toast.makeText(requireContext(), "语言设置功能开发中", Toast.LENGTH_SHORT).show()
        }

        settingVoice.setOnClickListener {
            navigateToVoiceEngineSettings()
        }

        // 自动播放设置
        settingAutoPlay.setOnClickListener {
            switchAutoPlay.toggle()
        }

        switchAutoPlay.setOnCheckedChangeListener { _, isChecked ->
            ttsConfig.setAutoPlayAiReply(isChecked)
            val message = if (isChecked) {
                getString(R.string.auto_play_enabled)
            } else {
                getString(R.string.auto_play_disabled)
            }
            Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
        }

        settingAccount.setOnClickListener {
            showAccountSettings()
        }

        settingData.setOnClickListener {
            showDataManagement()
        }

        settingSystem.setOnClickListener {
            showSystemSettings()
        }

        settingHelp.setOnClickListener {
            Toast.makeText(requireContext(), "使用帮助功能开发中", Toast.LENGTH_SHORT).show()
        }

        settingFeedback.setOnClickListener {
            Toast.makeText(requireContext(), "意见反馈功能开发中", Toast.LENGTH_SHORT).show()
        }

        settingAboutApp.setOnClickListener {
            showAboutApp()
        }

        logoutButton.setOnClickListener {
            showLogoutDialog()
        }
    }

    private fun showAccountSettings() {
        Toast.makeText(requireContext(), "账号设置功能开发中", Toast.LENGTH_SHORT).show()
    }

    private fun showDataManagement() {
        val options = arrayOf("清除聊天记录", "清除知识库", "清除所有数据")
        
        AlertDialog.Builder(requireContext())
            .setTitle("数据管理")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> showClearChatDialog()
                    1 -> showClearKnowledgeDialog()
                    2 -> showClearAllDataDialog()
                }
            }
            .show()
    }

    private fun showThemeSettings() {
        Log.d(TAG, "showThemeSettings: 准备跳转到主题设置页面")
        try {
            val themeSettingsFragment = ThemeSettingsFragment.newInstance()
            Log.d(TAG, "showThemeSettings: ThemeSettingsFragment实例创建成功")

            parentFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, themeSettingsFragment)
                .addToBackStack(null)
                .commit()

            Log.d(TAG, "showThemeSettings: Fragment事务提交成功")
        } catch (e: Exception) {
            Log.e(TAG, "showThemeSettings: 跳转失败", e)
            Toast.makeText(requireContext(), "跳转到主题设置失败：${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showSystemSettings() {
        val systemSettingsFragment = SystemSettingsFragment.newInstance()
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, systemSettingsFragment)
            .addToBackStack(null)
            .commit()
    }

    private fun loadSettings() {
        // 加载自动播放设置
        switchAutoPlay.isChecked = ttsConfig.getAutoPlayAiReply()
    }

    private fun navigateToVoiceEngineSettings() {
        val voiceEngineSettingsFragment = VoiceEngineSettingsFragment.newInstance()
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, voiceEngineSettingsFragment)
            .addToBackStack(null)
            .commit()
    }

    private fun showAboutApp() {
        AlertDialog.Builder(requireContext())
            .setTitle("关于RAG助手")
            .setMessage("RAG助手 V2.18.0\n\n一个基于检索增强生成技术的智能助手应用。")
            .setPositiveButton("确定", null)
            .show()
    }

    private fun showLogoutDialog() {
        val options = arrayOf("退出登录", "退出登录并清除数据")
        
        AlertDialog.Builder(requireContext())
            .setTitle("退出登录")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> logout(false)
                    1 -> logout(true)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showClearChatDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("清除聊天记录")
            .setMessage("确定要清除所有聊天记录吗？此操作不可恢复。")
            .setPositiveButton("确定") { _, _ ->
                clearChatHistory()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showClearKnowledgeDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("清除知识库")
            .setMessage("确定要清除所有知识库数据吗？此操作不可恢复。")
            .setPositiveButton("确定") { _, _ ->
                clearKnowledgeBase()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showClearAllDataDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("清除所有数据")
            .setMessage("确定要清除所有数据吗？包括聊天记录、知识库、用户信息等。此操作不可恢复。")
            .setPositiveButton("确定") { _, _ ->
                clearAllData()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun clearChatHistory() {
        lifecycleScope.launch {
            try {
                // 清除聊天记录的逻辑
                Toast.makeText(requireContext(), "聊天记录已清除", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "清除失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun clearKnowledgeBase() {
        lifecycleScope.launch {
            try {
                // 清除知识库的逻辑
                Toast.makeText(requireContext(), "知识库已清除", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "清除失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun clearAllData() {
        lifecycleScope.launch {
            try {
                // 清除所有数据的逻辑
                Toast.makeText(requireContext(), "所有数据已清除", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "清除失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun logout(clearData: Boolean) {
        lifecycleScope.launch {
            try {
                if (clearData) {
                    // 清除数据逻辑
                }

                // 跳转到主页面
                val intent = Intent(requireContext(), MainActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                startActivity(intent)
                requireActivity().finish()

            } catch (e: Exception) {
                Toast.makeText(requireContext(), "退出失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    companion object {
        private const val TAG = "SettingsFragment"
        fun newInstance() = SettingsFragment()
    }
}
