package com.bei.rag.fragment

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.Switch
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.bei.rag.R

class SystemSettingsFragment : Fragment() {

    private lateinit var backButton: ImageButton
    private lateinit var notificationsSwitch: Switch
    private lateinit var personalizationSwitch: Switch
    private lateinit var clearCacheSetting: LinearLayout

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_system_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        setupListeners()
    }

    private fun initViews(view: View) {
        backButton = view.findViewById(R.id.btn_back)
        notificationsSwitch = view.findViewById(R.id.switch_notifications)
        personalizationSwitch = view.findViewById(R.id.switch_personalization)
        clearCacheSetting = view.findViewById(R.id.setting_clear_cache)
    }

    private fun setupListeners() {
        backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        notificationsSwitch.setOnCheckedChangeListener { _, isChecked ->
            Toast.makeText(
                requireContext(),
                if (isChecked) "已开启消息通知" else "已关闭消息通知",
                Toast.LENGTH_SHORT
            ).show()
        }

        personalizationSwitch.setOnCheckedChangeListener { _, isChecked ->
            Toast.makeText(
                requireContext(),
                if (isChecked) "已开启个性化推荐" else "已关闭个性化推荐",
                Toast.LENGTH_SHORT
            ).show()
        }

        clearCacheSetting.setOnClickListener {
            showClearCacheDialog()
        }
    }

    private fun showClearCacheDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("清除缓存")
            .setMessage("确定要清除应用缓存吗？")
            .setPositiveButton("确定") { _, _ ->
                clearCache()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun clearCache() {
        try {
            // 这里可以添加实际的缓存清理逻辑
            Toast.makeText(requireContext(), "缓存已清除", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "清除缓存失败：${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    companion object {
        fun newInstance() = SystemSettingsFragment()
    }
}
