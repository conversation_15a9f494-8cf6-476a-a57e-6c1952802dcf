package com.bei.rag.fragment

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.bei.rag.R
import com.bei.rag.utils.ThemeManager

class ThemeColorSettingsFragment : Fragment() {

    private lateinit var backButton: ImageButton
    private lateinit var colorBlue: LinearLayout
    private lateinit var colorGreen: LinearLayout
    private lateinit var colorRed: LinearLayout
    private lateinit var colorPurple: LinearLayout
    private lateinit var colorOrange: LinearLayout
    private lateinit var colorPink: LinearLayout
    private lateinit var colorGray: LinearLayout
    
    private lateinit var checkBlue: ImageView
    private lateinit var checkGreen: ImageView
    private lateinit var checkRed: ImageView
    private lateinit var checkPurple: ImageView
    private lateinit var checkOrange: ImageView
    private lateinit var checkPink: ImageView
    private lateinit var checkGray: ImageView
    
    private lateinit var themeManager: ThemeManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        Log.d(TAG, "onCreateView: 开始创建主题颜色设置页面")
        val view = inflater.inflate(R.layout.fragment_theme_color_settings, container, false)
        Log.d(TAG, "onCreateView: 主题颜色设置布局加载完成")
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "onViewCreated: 开始初始化主题颜色设置视图")

        try {
            initViews(view)
            initThemeManager()
            setupListeners()
            updateUI()
            Log.d(TAG, "onViewCreated: 主题颜色设置视图初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "onViewCreated: 主题颜色设置初始化失败", e)
        }
    }

    private fun initViews(view: View) {
        backButton = view.findViewById(R.id.btn_back)
        colorBlue = view.findViewById(R.id.color_blue)
        colorGreen = view.findViewById(R.id.color_green)
        colorRed = view.findViewById(R.id.color_red)
        colorPurple = view.findViewById(R.id.color_purple)
        colorOrange = view.findViewById(R.id.color_orange)
        colorPink = view.findViewById(R.id.color_pink)
        colorGray = view.findViewById(R.id.color_gray)
        
        checkBlue = view.findViewById(R.id.check_blue)
        checkGreen = view.findViewById(R.id.check_green)
        checkRed = view.findViewById(R.id.check_red)
        checkPurple = view.findViewById(R.id.check_purple)
        checkOrange = view.findViewById(R.id.check_orange)
        checkPink = view.findViewById(R.id.check_pink)
        checkGray = view.findViewById(R.id.check_gray)
    }

    private fun initThemeManager() {
        themeManager = ThemeManager(requireContext())
    }

    private fun setupListeners() {
        backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        colorBlue.setOnClickListener {
            setThemeColor(ThemeManager.THEME_COLOR_BLUE)
        }

        colorGreen.setOnClickListener {
            setThemeColor(ThemeManager.THEME_COLOR_GREEN)
        }

        colorRed.setOnClickListener {
            setThemeColor(ThemeManager.THEME_COLOR_RED)
        }

        colorPurple.setOnClickListener {
            setThemeColor(ThemeManager.THEME_COLOR_PURPLE)
        }

        colorOrange.setOnClickListener {
            setThemeColor(ThemeManager.THEME_COLOR_ORANGE)
        }

        colorPink.setOnClickListener {
            setThemeColor(ThemeManager.THEME_COLOR_PINK)
        }

        colorGray.setOnClickListener {
            setThemeColor(ThemeManager.THEME_COLOR_GRAY)
        }
    }

    private fun setThemeColor(color: String) {
        themeManager.setThemeColor(color)
        updateUI()
        Toast.makeText(requireContext(), "主题色已设置为${themeManager.getThemeColorName(color)}", Toast.LENGTH_SHORT).show()

        // 重新创建Activity以应用新主题
        requireActivity().recreate()
    }

    private fun updateUI() {
        val currentColor = themeManager.getThemeColor()
        
        // 隐藏所有勾选图标
        checkBlue.visibility = View.GONE
        checkGreen.visibility = View.GONE
        checkRed.visibility = View.GONE
        checkPurple.visibility = View.GONE
        checkOrange.visibility = View.GONE
        checkPink.visibility = View.GONE
        checkGray.visibility = View.GONE
        
        // 显示当前选中的颜色的勾选图标
        when (currentColor) {
            ThemeManager.THEME_COLOR_BLUE -> checkBlue.visibility = View.VISIBLE
            ThemeManager.THEME_COLOR_GREEN -> checkGreen.visibility = View.VISIBLE
            ThemeManager.THEME_COLOR_RED -> checkRed.visibility = View.VISIBLE
            ThemeManager.THEME_COLOR_PURPLE -> checkPurple.visibility = View.VISIBLE
            ThemeManager.THEME_COLOR_ORANGE -> checkOrange.visibility = View.VISIBLE
            ThemeManager.THEME_COLOR_PINK -> checkPink.visibility = View.VISIBLE
            ThemeManager.THEME_COLOR_GRAY -> checkGray.visibility = View.VISIBLE
        }
    }

    companion object {
        private const val TAG = "ThemeColorSettingsFragment"
        fun newInstance() = ThemeColorSettingsFragment()
    }
}
