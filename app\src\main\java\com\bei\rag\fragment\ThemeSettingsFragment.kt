package com.bei.rag.fragment

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.bei.rag.R
import com.bei.rag.utils.ThemeManager

class ThemeSettingsFragment : Fragment() {

    private lateinit var backButton: ImageButton
    private lateinit var radioFollowSystem: RadioButton
    private lateinit var radioLightMode: RadioButton
    private lateinit var radioDarkMode: RadioButton
    private lateinit var settingThemeColor: LinearLayout
    private lateinit var tvCurrentThemeColor: TextView

    private lateinit var themeManager: ThemeManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        Log.d(TAG, "onCreateView: 开始创建主题设置页面")
        val view = inflater.inflate(R.layout.fragment_theme_settings, container, false)
        Log.d(TAG, "onCreateView: 布局加载完成")
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "onViewCreated: 开始初始化视图")

        try {
            initViews(view)
            initThemeManager()
            setupListeners()
            updateUI()
            Log.d(TAG, "onViewCreated: 视图初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "onViewCreated: 初始化失败", e)
        }
    }

    private fun initViews(view: View) {
        Log.d(TAG, "initViews: 开始初始化视图组件")
        try {
            backButton = view.findViewById(R.id.btn_back)
            radioFollowSystem = view.findViewById(R.id.radio_follow_system)
            radioLightMode = view.findViewById(R.id.radio_light_mode)
            radioDarkMode = view.findViewById(R.id.radio_dark_mode)
            settingThemeColor = view.findViewById(R.id.setting_theme_color)
            tvCurrentThemeColor = view.findViewById(R.id.tv_current_theme_color)

            Log.d(TAG, "initViews: 所有视图组件初始化完成")

            // 检查关键视图是否为null
            if (backButton == null) Log.e(TAG, "initViews: backButton is null")
            if (radioFollowSystem == null) Log.e(TAG, "initViews: radioFollowSystem is null")
            if (settingThemeColor == null) Log.e(TAG, "initViews: settingThemeColor is null")
            if (tvCurrentThemeColor == null) Log.e(TAG, "initViews: tvCurrentThemeColor is null")

        } catch (e: Exception) {
            Log.e(TAG, "initViews: 初始化视图组件失败", e)
            throw e
        }
    }

    private fun initThemeManager() {
        Log.d(TAG, "initThemeManager: 初始化主题管理器")
        try {
            themeManager = ThemeManager(requireContext())
            Log.d(TAG, "initThemeManager: 主题管理器初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "initThemeManager: 主题管理器初始化失败", e)
            throw e
        }
    }

    private fun setupListeners() {
        backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        radioFollowSystem.setOnClickListener {
            setThemeMode(ThemeManager.THEME_MODE_SYSTEM)
        }

        radioLightMode.setOnClickListener {
            setThemeMode(ThemeManager.THEME_MODE_LIGHT)
        }

        radioDarkMode.setOnClickListener {
            setThemeMode(ThemeManager.THEME_MODE_DARK)
        }

        settingThemeColor.setOnClickListener {
            showThemeColorSettings()
        }
    }

    private fun setThemeMode(mode: Int) {
        themeManager.setThemeMode(mode)
        updateThemeModeRadios(mode)
        updateThemeColorAvailability(mode)
    }

    private fun updateThemeModeRadios(mode: Int) {
        radioFollowSystem.isChecked = mode == ThemeManager.THEME_MODE_SYSTEM
        radioLightMode.isChecked = mode == ThemeManager.THEME_MODE_LIGHT
        radioDarkMode.isChecked = mode == ThemeManager.THEME_MODE_DARK
    }

    private fun updateUI() {
        Log.d(TAG, "updateUI: 开始更新UI")
        try {
            val currentMode = themeManager.getThemeMode()
            Log.d(TAG, "updateUI: 当前主题模式 = $currentMode")

            updateThemeModeRadios(currentMode)
            updateThemeColorAvailability(currentMode)

            val currentColor = themeManager.getThemeColor()
            Log.d(TAG, "updateUI: 当前主题颜色 = $currentColor")

            val colorName = themeManager.getThemeColorName(currentColor)
            tvCurrentThemeColor.text = colorName
            Log.d(TAG, "updateUI: 主题颜色名称设置为 = $colorName")

            Log.d(TAG, "updateUI: UI更新完成")
        } catch (e: Exception) {
            Log.e(TAG, "updateUI: 更新UI失败", e)
        }
    }

    private fun updateThemeColorAvailability(mode: Int) {
        val isDarkMode = mode == ThemeManager.THEME_MODE_DARK
        settingThemeColor.isEnabled = !isDarkMode
        settingThemeColor.alpha = if (isDarkMode) 0.5f else 1.0f

        // 更新提示文字
        if (isDarkMode) {
            tvCurrentThemeColor.text = "深色模式下不可选择"
        } else {
            val currentColor = themeManager.getThemeColor()
            tvCurrentThemeColor.text = themeManager.getThemeColorName(currentColor)
        }
    }

    private fun showThemeColorSettings() {
        // 只有在非深色模式下才允许进入主题色设置
        val currentMode = themeManager.getThemeMode()
        if (currentMode != ThemeManager.THEME_MODE_DARK) {
            val themeColorFragment = ThemeColorSettingsFragment.newInstance()
            parentFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, themeColorFragment)
                .addToBackStack(null)
                .commit()
        }
    }

    override fun onResume() {
        super.onResume()
        // 从主题色设置页面返回时更新UI
        updateUI()
    }

    companion object {
        private const val TAG = "ThemeSettingsFragment"
        fun newInstance() = ThemeSettingsFragment()
    }
}
