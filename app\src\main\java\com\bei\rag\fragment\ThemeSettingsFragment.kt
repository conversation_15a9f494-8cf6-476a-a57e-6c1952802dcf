package com.bei.rag.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.bei.rag.R
import com.bei.rag.utils.ThemeManager

class ThemeSettingsFragment : Fragment() {

    private lateinit var backButton: ImageButton
    private lateinit var radioFollowSystem: RadioButton
    private lateinit var radioLightMode: RadioButton
    private lateinit var radioDarkMode: RadioButton
    private lateinit var settingThemeColor: LinearLayout
    private lateinit var tvCurrentThemeColor: TextView
    private lateinit var ivSystemCheck: ImageView
    private lateinit var ivLightCheck: ImageView
    private lateinit var ivDarkCheck: ImageView

    private lateinit var themeManager: ThemeManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_theme_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews(view)
        initThemeManager()
        setupListeners()
        updateUI()
    }

    private fun initViews(view: View) {
        backButton = view.findViewById(R.id.btn_back)
        radioFollowSystem = view.findViewById(R.id.radio_follow_system)
        radioLightMode = view.findViewById(R.id.radio_light_mode)
        radioDarkMode = view.findViewById(R.id.radio_dark_mode)
        settingThemeColor = view.findViewById(R.id.setting_theme_color)
        tvCurrentThemeColor = view.findViewById(R.id.tv_current_theme_color)
        ivSystemCheck = view.findViewById(R.id.iv_system_check)
        ivLightCheck = view.findViewById(R.id.iv_light_check)
        ivDarkCheck = view.findViewById(R.id.iv_dark_check)
    }

    private fun initThemeManager() {
        themeManager = ThemeManager(requireContext())
    }

    private fun setupListeners() {
        backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        radioFollowSystem.setOnClickListener {
            setThemeMode(ThemeManager.THEME_MODE_SYSTEM)
        }

        radioLightMode.setOnClickListener {
            setThemeMode(ThemeManager.THEME_MODE_LIGHT)
        }

        radioDarkMode.setOnClickListener {
            setThemeMode(ThemeManager.THEME_MODE_DARK)
        }

        settingThemeColor.setOnClickListener {
            showThemeColorSettings()
        }
    }

    private fun setThemeMode(mode: Int) {
        themeManager.setThemeMode(mode)
        updateThemeModeRadios(mode)
        updateThemeColorAvailability(mode)
    }

    private fun updateThemeModeRadios(mode: Int) {
        radioFollowSystem.isChecked = mode == ThemeManager.THEME_MODE_SYSTEM
        radioLightMode.isChecked = mode == ThemeManager.THEME_MODE_LIGHT
        radioDarkMode.isChecked = mode == ThemeManager.THEME_MODE_DARK

        // 更新勾选图标显示
        ivSystemCheck.visibility = if (mode == ThemeManager.THEME_MODE_SYSTEM) View.VISIBLE else View.GONE
        ivLightCheck.visibility = if (mode == ThemeManager.THEME_MODE_LIGHT) View.VISIBLE else View.GONE
        ivDarkCheck.visibility = if (mode == ThemeManager.THEME_MODE_DARK) View.VISIBLE else View.GONE
    }

    private fun updateUI() {
        val currentMode = themeManager.getThemeMode()
        updateThemeModeRadios(currentMode)
        updateThemeColorAvailability(currentMode)

        val currentColor = themeManager.getThemeColor()
        tvCurrentThemeColor.text = themeManager.getThemeColorName(currentColor)
    }

    private fun updateThemeColorAvailability(mode: Int) {
        val isDarkMode = mode == ThemeManager.THEME_MODE_DARK
        settingThemeColor.isEnabled = !isDarkMode
        settingThemeColor.alpha = if (isDarkMode) 0.5f else 1.0f

        // 更新提示文字
        if (isDarkMode) {
            tvCurrentThemeColor.text = "深色模式下不可选择"
        } else {
            val currentColor = themeManager.getThemeColor()
            tvCurrentThemeColor.text = themeManager.getThemeColorName(currentColor)
        }
    }

    private fun showThemeColorSettings() {
        // 只有在非深色模式下才允许进入主题色设置
        val currentMode = themeManager.getThemeMode()
        if (currentMode != ThemeManager.THEME_MODE_DARK) {
            val themeColorFragment = ThemeColorSettingsFragment.newInstance()
            parentFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, themeColorFragment)
                .addToBackStack(null)
                .commit()
        }
    }

    override fun onResume() {
        super.onResume()
        // 从主题色设置页面返回时更新UI
        updateUI()
    }

    companion object {
        private const val TAG = "ThemeSettingsFragment"
        fun newInstance() = ThemeSettingsFragment()
    }
}
