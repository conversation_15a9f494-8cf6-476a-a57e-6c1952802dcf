package com.bei.rag.fragment

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bei.rag.R
import com.bei.rag.service.TextToSpeechService
import com.bei.rag.utils.TtsConfig
import com.bei.rag.utils.TtsEngineType
import kotlinx.coroutines.launch

class VoiceEngineSettingsFragment : Fragment() {

    private lateinit var backButton: ImageButton
    private lateinit var engineSelectionLayout: LinearLayout
    private lateinit var currentEngineText: TextView
    private lateinit var speechRateLayout: LinearLayout
    private lateinit var speechRateText: TextView
    private lateinit var speechRateSeekBar: SeekBar
    private lateinit var pitchLayout: LinearLayout
    private lateinit var pitchText: TextView
    private lateinit var pitchSeekBar: SeekBar
    private lateinit var languageLayout: LinearLayout
    private lateinit var currentLanguageText: TextView
    private lateinit var testVoiceButton: Button
    private lateinit var resetSettingsButton: Button

    private lateinit var ttsConfig: TtsConfig
    private lateinit var textToSpeechService: TextToSpeechService



    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_voice_engine_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        initServices()
        setupListeners()
        loadCurrentSettings()
    }

    private fun initViews(view: View) {
        backButton = view.findViewById(R.id.btn_back)
        engineSelectionLayout = view.findViewById(R.id.layout_engine_selection)
        currentEngineText = view.findViewById(R.id.text_current_engine)
        speechRateLayout = view.findViewById(R.id.layout_speech_rate)
        speechRateText = view.findViewById(R.id.text_speech_rate)
        speechRateSeekBar = view.findViewById(R.id.seekbar_speech_rate)
        pitchLayout = view.findViewById(R.id.layout_pitch)
        pitchText = view.findViewById(R.id.text_pitch)
        pitchSeekBar = view.findViewById(R.id.seekbar_pitch)
        languageLayout = view.findViewById(R.id.layout_language)
        currentLanguageText = view.findViewById(R.id.text_current_language)
        testVoiceButton = view.findViewById(R.id.btn_test_voice)
        resetSettingsButton = view.findViewById(R.id.btn_reset_settings)
    }

    private fun initServices() {
        ttsConfig = TtsConfig(requireContext())
        textToSpeechService = TextToSpeechService(requireContext())
    }

    private fun setupListeners() {
        backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        engineSelectionLayout.setOnClickListener {
            showEngineSelectionDialog()
        }

        speechRateSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val rate = (progress + 50) / 100.0f // 0.5 - 2.0
                    speechRateText.text = "语速: ${String.format("%.1f", rate)}"
                    ttsConfig.setSpeechRate(rate)
                    textToSpeechService.setSpeechRate(rate)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        pitchSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val pitch = (progress + 50) / 100.0f // 0.5 - 2.0
                    pitchText.text = "音调: ${String.format("%.1f", pitch)}"
                    ttsConfig.setPitch(pitch)
                    textToSpeechService.setPitch(pitch)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        languageLayout.setOnClickListener {
            showLanguageSelectionDialog()
        }

        testVoiceButton.setOnClickListener {
            testVoice()
        }

        resetSettingsButton.setOnClickListener {
            showResetConfirmDialog()
        }
    }

    private fun loadCurrentSettings() {
        // 加载当前引擎
        val currentEngine = ttsConfig.getEngineType()
        currentEngineText.text = currentEngine.displayName

        // 加载语速设置
        val speechRate = ttsConfig.getSpeechRate()
        speechRateText.text = "语速: ${String.format("%.1f", speechRate)}"
        speechRateSeekBar.progress = ((speechRate * 100) - 50).toInt()

        // 加载音调设置
        val pitch = ttsConfig.getPitch()
        pitchText.text = "音调: ${String.format("%.1f", pitch)}"
        pitchSeekBar.progress = ((pitch * 100) - 50).toInt()

        // 加载语言设置
        val language = ttsConfig.getLanguage()
        currentLanguageText.text = getLanguageDisplayName(language)
    }

    private fun showEngineSelectionDialog() {
        val engines = TtsEngineType.values()
        val engineNames = engines.map { "${it.displayName}\n${it.description}" }.toTypedArray()
        val currentEngine = ttsConfig.getEngineType()
        val currentIndex = engines.indexOf(currentEngine)

        AlertDialog.Builder(requireContext())
            .setTitle("选择语音引擎")
            .setSingleChoiceItems(engineNames, currentIndex) { dialog, which ->
                val selectedEngine = engines[which]
                if (selectedEngine != currentEngine) {
                    switchToEngine(selectedEngine)
                }
                dialog.dismiss()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun switchToEngine(engineType: TtsEngineType) {
        lifecycleScope.launch {
            try {
                val result = textToSpeechService.switchEngine(engineType)
                if (result.isSuccess) {
                    currentEngineText.text = engineType.displayName
                    Toast.makeText(requireContext(), "已切换到${engineType.displayName}", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(requireContext(), "切换引擎失败: ${result.exceptionOrNull()?.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "切换引擎时出错: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun showLanguageSelectionDialog() {
        val languages = arrayOf(
            "zh-CN" to "中文（简体）",
            "zh-TW" to "中文（繁体）",
            "en-US" to "英语（美国）",
            "en-GB" to "英语（英国）"
        )
        
        val languageNames = languages.map { it.second }.toTypedArray()
        val currentLanguage = ttsConfig.getLanguage()
        val currentIndex = languages.indexOfFirst { it.first == currentLanguage }

        AlertDialog.Builder(requireContext())
            .setTitle("选择语言")
            .setSingleChoiceItems(languageNames, currentIndex) { dialog, which ->
                val selectedLanguage = languages[which]
                setLanguage(selectedLanguage.first, selectedLanguage.second)
                dialog.dismiss()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun setLanguage(languageCode: String, displayName: String) {
        lifecycleScope.launch {
            try {
                val result = textToSpeechService.setLanguage(languageCode)
                if (result.isSuccess) {
                    currentLanguageText.text = displayName
                    Toast.makeText(requireContext(), "语言已设置为$displayName", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(requireContext(), "设置语言失败: ${result.exceptionOrNull()?.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "设置语言时出错: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun getLanguageDisplayName(languageCode: String): String {
        return when (languageCode) {
            "zh-CN" -> "中文（简体）"
            "zh-TW" -> "中文（繁体）"
            "en-US" -> "英语（美国）"
            "en-GB" -> "英语（英国）"
            else -> languageCode
        }
    }

    private fun testVoice() {
        if (textToSpeechService.isSpeaking()) {
            textToSpeechService.stopSpeaking()
            testVoiceButton.text = "测试语音"
            return
        }

        val testText = when (ttsConfig.getLanguage()) {
            "zh-CN", "zh-TW" -> "你好，这是语音测试。"
            else -> "Hello, this is a voice test."
        }

        lifecycleScope.launch {
            try {
                testVoiceButton.text = "停止测试"
                val result = textToSpeechService.speakText(testText) {
                    testVoiceButton.text = "测试语音"
                }
                
                if (result.isFailure) {
                    testVoiceButton.text = "测试语音"
                    Toast.makeText(requireContext(), "语音测试失败: ${result.exceptionOrNull()?.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                testVoiceButton.text = "测试语音"
                Toast.makeText(requireContext(), "语音测试出错: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun showResetConfirmDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("重置设置")
            .setMessage("确定要将所有语音设置重置为默认值吗？")
            .setPositiveButton("确定") { _, _ ->
                resetSettings()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun resetSettings() {
        try {
            ttsConfig.resetToDefault()
            loadCurrentSettings()
            
            // 重新初始化TTS服务
            lifecycleScope.launch {
                textToSpeechService.switchEngine(TtsEngineType.getDefault())
            }
            
            Toast.makeText(requireContext(), "设置已重置为默认值", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "重置设置时出错: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::textToSpeechService.isInitialized) {
            textToSpeechService.cleanup()
        }
    }

    companion object {
        fun newInstance() = VoiceEngineSettingsFragment()
    }
} 