package com.bei.rag.model

import com.google.gson.annotations.SerializedName

// 请求数据模型
data class ChatRequest(
    @SerializedName("model")
    val model: String,
    @SerializedName("messages")
    val messages: List<Message>
)

data class Message(
    @SerializedName("role")
    val role: String,
    @SerializedName("content")
    val content: List<Content>
)

data class Content(
    @SerializedName("type")
    val type: String,
    @SerializedName("text")
    val text: String
)

// 响应数据模型
data class ChatResponse(
    @SerializedName("id")
    val id: String,
    @SerializedName("provider")
    val provider: String,
    @SerializedName("model")
    val model: String,
    @SerializedName("object")
    val objectType: String,
    @SerializedName("created")
    val created: Long,
    @SerializedName("choices")
    val choices: List<Choice>,
    @SerializedName("usage")
    val usage: Usage
)

data class Choice(
    @SerializedName("logprobs")
    val logprobs: Any?,
    @SerializedName("finish_reason")
    val finishReason: String,
    @SerializedName("native_finish_reason")
    val nativeFinishReason: String,
    @SerializedName("index")
    val index: Int,
    @SerializedName("message")
    val message: ResponseMessage
)

data class ResponseMessage(
    @SerializedName("role")
    val role: String,
    @SerializedName("content")
    val content: String,
    @SerializedName("refusal")
    val refusal: Any?,
    @SerializedName("reasoning")
    val reasoning: Any?
)

data class Usage(
    @SerializedName("prompt_tokens")
    val promptTokens: Int,
    @SerializedName("completion_tokens")
    val completionTokens: Int,
    @SerializedName("total_tokens")
    val totalTokens: Int
)
