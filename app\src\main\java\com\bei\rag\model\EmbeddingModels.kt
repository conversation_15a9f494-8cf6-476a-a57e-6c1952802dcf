package com.bei.rag.model

import com.google.gson.annotations.SerializedName

/**
 * 嵌入API请求模型
 */
data class EmbeddingRequest(
    @SerializedName("model")
    val model: String = "BAAI/bge-m3",
    @SerializedName("input")
    val input: String,
    @SerializedName("encoding_format")
    val encodingFormat: String = "float"
)

/**
 * 嵌入API响应模型
 */
data class EmbeddingResponse(
    @SerializedName("object")
    val objectType: String,
    @SerializedName("data")
    val data: List<EmbeddingData>,
    @SerializedName("model")
    val model: String,
    @SerializedName("usage")
    val usage: EmbeddingUsage
)

/**
 * 嵌入数据模型
 */
data class EmbeddingData(
    @SerializedName("object")
    val objectType: String,
    @SerializedName("embedding")
    val embedding: List<Float>,
    @SerializedName("index")
    val index: Int
)

/**
 * 嵌入使用情况模型
 */
data class EmbeddingUsage(
    @SerializedName("prompt_tokens")
    val promptTokens: Int,
    @SerializedName("completion_tokens")
    val completionTokens: Int,
    @SerializedName("total_tokens")
    val totalTokens: Int
)

/**
 * 文档块模型
 */
data class DocumentChunk(
    val id: String = "",
    val documentId: Long,
    val content: String,
    val embedding: FloatArray? = null,
    val startIndex: Int,
    val endIndex: Int,
    val chunkIndex: Int,
    val metadata: Map<String, Any> = emptyMap()
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as DocumentChunk

        if (id != other.id) return false
        if (documentId != other.documentId) return false
        if (content != other.content) return false
        if (embedding != null) {
            if (other.embedding == null) return false
            if (!embedding.contentEquals(other.embedding)) return false
        } else if (other.embedding != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + documentId.hashCode()
        result = 31 * result + content.hashCode()
        result = 31 * result + (embedding?.contentHashCode() ?: 0)
        return result
    }
}

/**
 * 文档内容模型
 */
data class DocumentContent(
    val text: String,
    val metadata: Map<String, Any> = emptyMap()
)

 