package com.bei.rag.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Supabase文档表模型
 */
@Serializable
data class SupabaseDocument(
    val id: Long,
    @SerialName("file_name")
    val fileName: String,
    @SerialName("file_type")
    val fileType: String,
    @SerialName("file_size")
    val fileSize: Long,
    @SerialName("upload_time")
    val uploadTime: Long,
    @SerialName("is_vectorized")
    val isVectorized: Boolean = false,
    @SerialName("chunk_count")
    val chunkCount: Int = 0,
    @SerialName("processing_status")
    val processingStatus: String = "pending",
    @SerialName("error_message")
    val errorMessage: String = "",
    @SerialName("extracted_text")
    val extractedText: String = "",
    @SerialName("last_processed_time")
    val lastProcessedTime: Long = 0,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("updated_at")
    val updatedAt: String? = null
)

/**
 * Supabase文档块表模型
 */
@Serializable
data class SupabaseDocumentChunk(
    val id: Long? = null,
    @SerialName("document_id")
    val documentId: Long,
    @SerialName("chunk_index")
    val chunkIndex: Int,
    val content: String,
    val embedding: List<Float>,
    val metadata: Map<String, String> = emptyMap(),
    @SerialName("created_at")
    val createdAt: String? = null
)

/**
 * 向量搜索请求
 */
@Serializable
data class VectorSearchRequest(
    val queryEmbedding: List<Float>,
    val limit: Int = 5,
    val threshold: Double = 0.7,
    val documentIds: List<Long>? = null
)

/**
 * 向量搜索结果
 */
@Serializable
data class VectorSearchResult(
    val chunkId: Long,
    val documentId: Long,
    val content: String,
    val similarity: Double,
    val metadata: Map<String, String> = emptyMap(),
    val documentName: String? = null
)

/**
 * RAG查询请求
 */
data class RagQueryRequest(
    val query: String,
    val maxResults: Int = 5,
    val similarityThreshold: Double = 0.3,
    val documentIds: List<Long>? = null,
    val useTopKSearch: Boolean = false
)

/**
 * RAG查询响应
 */
data class RagQueryResponse(
    val query: String,
    val results: List<VectorSearchResult>,
    val context: String,
    val totalResults: Int
)

/**
 * 数据同步状态
 */
data class SyncStatus(
    val isOnline: Boolean,
    val lastSyncTime: Long,
    val pendingUploads: Int,
    val pendingDownloads: Int,
    val syncInProgress: Boolean = false
)

/**
 * 批量操作结果
 */
data class BatchOperationResult<T>(
    val successful: List<T>,
    val failed: List<Pair<T, String>>,
    val totalProcessed: Int
) {
    val successCount: Int get() = successful.size
    val failureCount: Int get() = failed.size
    val isAllSuccessful: Boolean get() = failed.isEmpty()
} 