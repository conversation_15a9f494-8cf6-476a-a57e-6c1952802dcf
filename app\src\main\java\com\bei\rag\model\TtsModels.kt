package com.bei.rag.model

import com.google.gson.annotations.SerializedName

/**
 * TTS请求数据模型
 */
data class TtsRequest(
    @SerializedName("contents")
    val contents: List<TtsContent>,
    @SerializedName("generationConfig")
    val generationConfig: TtsGenerationConfig? = null
)

/**
 * TTS内容
 */
data class TtsContent(
    @SerializedName("parts")
    val parts: List<TtsPart>
)

/**
 * TTS内容部分
 */
data class TtsPart(
    @SerializedName("text")
    val text: String
)

/**
 * TTS生成配置
 */
data class TtsGenerationConfig(
    @SerializedName("speechConfig")
    val speechConfig: TtsSpeechConfig
)

/**
 * TTS语音配置
 */
data class TtsSpeechConfig(
    @SerializedName("voiceConfig")
    val voiceConfig: TtsVoiceConfig
)

/**
 * TTS语音配置
 */
data class TtsVoiceConfig(
    @SerializedName("prebuiltVoiceConfig")
    val prebuiltVoiceConfig: TtsPrebuiltVoiceConfig
)

/**
 * TTS预构建语音配置
 */
data class TtsPrebuiltVoiceConfig(
    @SerializedName("voiceName")
    val voiceName: String = "en-US-Journey-D"
)

/**
 * TTS响应数据模型
 */
data class TtsResponse(
    @SerializedName("candidates")
    val candidates: List<TtsCandidate>? = null,
    @SerializedName("error")
    val error: TtsError? = null
)

/**
 * TTS候选结果
 */
data class TtsCandidate(
    @SerializedName("content")
    val content: TtsResponseContent? = null
)

/**
 * TTS响应内容
 */
data class TtsResponseContent(
    @SerializedName("parts")
    val parts: List<TtsResponsePart>? = null
)

/**
 * TTS响应部分
 */
data class TtsResponsePart(
    @SerializedName("inlineData")
    val inlineData: TtsInlineData? = null
)

/**
 * TTS内联数据
 */
data class TtsInlineData(
    @SerializedName("mimeType")
    val mimeType: String? = null,
    @SerializedName("data")
    val data: String? = null
)

/**
 * TTS错误响应
 */
data class TtsError(
    @SerializedName("code")
    val code: Int? = null,
    @SerializedName("message")
    val message: String? = null,
    @SerializedName("status")
    val status: String? = null
) 