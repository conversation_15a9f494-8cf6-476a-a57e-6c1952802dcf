package com.bei.rag.model

/**
 * SiliconFlow语音转文字API响应数据类
 */
data class VoiceToTextResponse(
    val text: String
)

/**
 * 语音转文字状态
 */
sealed class VoiceToTextState {
    object Idle : VoiceToTextState()
    object Recording : VoiceToTextState()
    object Processing : VoiceToTextState()
    data class Success(val text: String) : VoiceToTextState()
    data class Error(val message: String) : VoiceToTextState()
}
