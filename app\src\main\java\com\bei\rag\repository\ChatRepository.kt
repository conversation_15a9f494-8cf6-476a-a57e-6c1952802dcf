package com.bei.rag.repository

import android.util.Log
import com.bei.rag.database.dao.ChatMessageDao
import com.bei.rag.database.entity.ChatMessageEntity
import com.bei.rag.model.ChatMessage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

class ChatRepository(private val chatMessageDao: ChatMessageDao) {
    
    fun getMessagesByConversation(conversationId: Long): Flow<List<ChatMessage>> {
        return chatMessageDao.getMessagesByConversation(conversationId).map { entities ->
            entities.map { entity ->
                ChatMessage(
                    id = entity.id.toString(),
                    content = entity.content,
                    isUser = entity.isUser,
                    timestamp = entity.timestamp,
                    sources = entity.sources
                )
            }
        }
    }
    
    suspend fun insertMessage(message: ChatMessage, conversationId: Long = 0, groupId: Long = 0): Long {
        Log.d("ChatRepository", "插入消息: ${message.content}, 用户: ${message.isUser}, 会话ID: $conversationId, 分组ID: $groupId")
        val entity = ChatMessageEntity(
            content = message.content,
            isUser = message.isUser,
            timestamp = message.timestamp,
            sources = message.sources,
            conversationId = conversationId,
            groupId = groupId
        )
        val insertedId = chatMessageDao.insertMessage(entity)
        Log.d("ChatRepository", "消息插入成功，ID: $insertedId")
        return insertedId
    }
    
    suspend fun deleteConversation(conversationId: Long) {
        chatMessageDao.deleteConversation(conversationId)
    }
    
    suspend fun getAllConversationIds(): List<Long> {
        return chatMessageDao.getAllConversationIds()
    }
    
    suspend fun searchMessages(query: String): List<ChatMessage> {
        return chatMessageDao.searchMessages(query).map { entity ->
            ChatMessage(
                id = entity.id.toString(),
                content = entity.content,
                isUser = entity.isUser,
                timestamp = entity.timestamp,
                sources = entity.sources
            )
        }
    }
    
    suspend fun getMessageCount(): Int {
        return chatMessageDao.getMessageCount()
    }
    
    suspend fun deleteAllMessages() {
        chatMessageDao.deleteAllMessages()
    }

    suspend fun deleteMessage(message: ChatMessage) {
        val messageId = message.id.toLongOrNull()
        if (messageId != null) {
            chatMessageDao.deleteMessage(messageId)
        }
    }

    suspend fun getMessagesByConversationSync(conversationId: Long): List<ChatMessage> {
        return chatMessageDao.getMessagesByConversation(conversationId).map { entities ->
            entities.map { entity ->
                ChatMessage(
                    id = entity.id.toString(),
                    content = entity.content,
                    isUser = entity.isUser,
                    timestamp = entity.timestamp,
                    sources = entity.sources
                )
            }
        }.first() // 获取第一个发射的值（同步获取）
    }

    // 分组相关方法
    suspend fun getConversationIdsByGroup(groupId: Long): List<Long> {
        return chatMessageDao.getConversationIdsByGroup(groupId)
    }

    suspend fun updateConversationGroup(conversationId: Long, groupId: Long) {
        chatMessageDao.updateConversationGroup(conversationId, groupId)
    }
}
