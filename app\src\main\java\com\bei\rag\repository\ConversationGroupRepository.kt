package com.bei.rag.repository

import com.bei.rag.database.dao.ChatMessageDao
import com.bei.rag.database.dao.ConversationGroupDao
import com.bei.rag.database.entity.ConversationGroupEntity
import com.bei.rag.model.ConversationGroup
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class ConversationGroupRepository(
    private val conversationGroupDao: ConversationGroupDao,
    private val chatMessageDao: ChatMessageDao
) {
    
    fun getAllGroups(): Flow<List<ConversationGroup>> {
        return conversationGroupDao.getAllGroups().map { entities ->
            entities.map { entity ->
                val conversationCount = getConversationCountByGroup(entity.id)
                ConversationGroup(
                    id = entity.id,
                    name = entity.name,
                    description = entity.description,
                    createdTime = entity.createdTime,
                    conversationCount = conversationCount
                )
            }
        }
    }
    
    suspend fun getGroupById(groupId: Long): ConversationGroup? {
        val entity = conversationGroupDao.getGroupById(groupId)
        return entity?.let {
            val conversationCount = getConversationCountByGroup(it.id)
            ConversationGroup(
                id = it.id,
                name = it.name,
                description = it.description,
                createdTime = it.createdTime,
                conversationCount = conversationCount
            )
        }
    }
    
    suspend fun createGroup(name: String, description: String = ""): Long {
        val entity = ConversationGroupEntity(
            name = name,
            description = description,
            createdTime = System.currentTimeMillis()
        )
        return conversationGroupDao.insertGroup(entity)
    }
    
    suspend fun updateGroup(group: ConversationGroup) {
        val entity = ConversationGroupEntity(
            id = group.id,
            name = group.name,
            description = group.description,
            createdTime = group.createdTime
        )
        conversationGroupDao.updateGroup(entity)
    }
    
    suspend fun deleteGroup(groupId: Long) {
        // 先将该分组下的所有会话移动到默认分组（groupId = 0）
        chatMessageDao.updateGroupIdForConversations(groupId, 0)
        // 然后删除分组
        conversationGroupDao.deleteGroupById(groupId)
    }
    
    suspend fun getConversationCountByGroup(groupId: Long): Int {
        return chatMessageDao.getConversationCountByGroup(groupId)
    }
    
    suspend fun getDefaultGroupConversationCount(): Int {
        return chatMessageDao.getConversationCountByGroup(0)
    }
}
