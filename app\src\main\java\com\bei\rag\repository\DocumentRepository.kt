package com.bei.rag.repository

import com.bei.rag.database.dao.DocumentDao
import com.bei.rag.database.entity.DocumentEntity
import kotlinx.coroutines.flow.Flow

class DocumentRepository(private val documentDao: DocumentDao) {
    
    fun getAllDocuments(): Flow<List<DocumentEntity>> {
        return documentDao.getAllDocuments()
    }
    
    fun getDocumentsByType(fileType: String): Flow<List<DocumentEntity>> {
        return documentDao.getDocumentsByType(fileType)
    }
    
    suspend fun insertDocument(document: DocumentEntity): Long {
        return documentDao.insertDocument(document)
    }
    
    suspend fun updateDocument(document: DocumentEntity) {
        documentDao.updateDocument(document)
    }
    
    suspend fun deleteDocument(document: DocumentEntity) {
        documentDao.deleteDocument(document)
    }
    
    suspend fun deleteDocumentById(documentId: Long) {
        documentDao.deleteDocumentById(documentId)
    }
    
    suspend fun searchDocuments(query: String): List<DocumentEntity> {
        return documentDao.searchDocuments(query)
    }
    
    suspend fun getDocumentCount(): Int {
        return documentDao.getDocumentCount()
    }
    
    suspend fun getTotalFileSize(): Long {
        return documentDao.getTotalFileSize()
    }
    
    suspend fun getDocumentById(documentId: Long): DocumentEntity? {
        return documentDao.getDocumentById(documentId)
    }
    
    suspend fun getUnprocessedDocuments(): List<DocumentEntity> {
        return documentDao.getUnprocessedDocuments()
    }
    
    suspend fun getVectorizedDocuments(): List<DocumentEntity> {
        return documentDao.getVectorizedDocuments()
    }
    
    suspend fun updateProcessingStatus(documentId: Long, status: String) {
        documentDao.updateProcessingStatus(documentId, status)
    }
    
    suspend fun updateVectorizedStatus(documentId: Long, isVectorized: Boolean, chunkCount: Int, status: String, time: Long) {
        documentDao.updateVectorizedStatus(documentId, isVectorized, chunkCount, status, time)
    }
    
    suspend fun updateProcessingError(documentId: Long, status: String, errorMessage: String) {
        documentDao.updateProcessingError(documentId, status, errorMessage)
    }
    
    suspend fun updateExtractedText(documentId: Long, text: String) {
        documentDao.updateExtractedText(documentId, text)
    }
    
    suspend fun getVectorizedDocumentCount(): Int {
        return documentDao.getVectorizedDocumentCount()
    }
}
