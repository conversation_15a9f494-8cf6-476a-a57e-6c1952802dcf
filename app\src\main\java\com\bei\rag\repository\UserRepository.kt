package com.bei.rag.repository

import com.bei.rag.database.dao.UserDao
import com.bei.rag.database.entity.UserEntity
import kotlinx.coroutines.flow.Flow

class UserRepository(private val userDao: UserDao) {
    
    fun getUserInfo(): Flow<UserEntity?> {
        return userDao.getUserInfo()
    }
    
    suspend fun getUserInfoSync(): UserEntity? {
        return userDao.getUserInfoSync()
    }
    
    suspend fun insertOrUpdateUser(user: UserEntity) {
        userDao.insertOrUpdateUser(user)
    }
    
    suspend fun updateUser(user: UserEntity) {
        userDao.updateUser(user)
    }
    
    suspend fun updateLastLoginTime(time: Long) {
        userDao.updateLastLoginTime(time)
    }
    
    suspend fun updateNickname(nickname: String) {
        userDao.updateNickname(nickname)
    }
    
    suspend fun updateEmail(email: String) {
        userDao.updateEmail(email)
    }
    
    suspend fun updateAvatar(avatarPath: String) {
        userDao.updateAvatar(avatarPath)
    }

    suspend fun clearUserInfo() {
        userDao.clearUserInfo()
    }

    suspend fun deleteUser() {
        userDao.deleteUser()
    }

    suspend fun resetToDefault() {
        // 删除现有用户信息
        userDao.deleteUser()
        // 创建默认用户
        val defaultUser = UserEntity()
        userDao.insertOrUpdateUser(defaultUser)
    }
}
