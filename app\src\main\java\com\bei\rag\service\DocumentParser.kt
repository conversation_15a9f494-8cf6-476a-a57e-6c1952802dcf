package com.bei.rag.service

import android.content.Context
import com.bei.rag.model.DocumentContent
import java.io.File
import java.io.FileInputStream
import java.io.IOException

/**
 * 文档解析器接口
 */
interface DocumentParser {
    suspend fun parseDocument(filePath: String): DocumentContent
    fun getSupportedExtensions(): List<String>
}

/**
 * 文档解析器工厂
 */
class DocumentParserFactory {
    companion object {
        fun getParser(fileType: String, context: Context): DocumentParser? {
            return when (fileType.lowercase()) {
                "txt" -> TxtParser()
                "pdf" -> PdfParser(context)
                "docx", "doc" -> DocxParser(context)
                "csv" -> CsvParser()
                else -> null
            }
        }
    }
}

/**
 * TXT文档解析器
 */
class TxtParser : DocumentParser {
    override suspend fun parseDocument(filePath: String): DocumentContent {
        try {
            val file = File(filePath)
            val text = file.readText(Charsets.UTF_8)
            return DocumentContent(
                text = text,
                metadata = mapOf(
                    "fileSize" to file.length(),
                    "lineCount" to text.lines().size
                )
            )
        } catch (e: Exception) {
            throw IOException("TXT文件解析失败: ${e.message}")
        }
    }

    override fun getSupportedExtensions(): List<String> = listOf("txt")
}

/**
 * PDF文档解析器
 * 注意：这是一个简化版本，实际项目中需要添加PDF解析库如iText或PDFBox
 */
class PdfParser(private val context: Context) : DocumentParser {
    override suspend fun parseDocument(filePath: String): DocumentContent {
        try {
            // TODO: 实现PDF解析功能
            // 这里需要集成PDF解析库，如iText或Apache PDFBox
            // 由于Android环境限制，建议使用Android兼容的PDF库
            
            // 临时实现：返回文件基本信息
            val file = File(filePath)
            return DocumentContent(
                text = "PDF文档解析功能待实现。文件名: ${file.name}",
                metadata = mapOf(
                    "fileSize" to file.length(),
                    "fileType" to "pdf",
                    "needsImplementation" to true
                )
            )
        } catch (e: Exception) {
            throw IOException("PDF文件解析失败: ${e.message}")
        }
    }

    override fun getSupportedExtensions(): List<String> = listOf("pdf")
}

/**
 * DOCX文档解析器
 * 注意：这是一个简化版本，实际项目中需要添加DOCX解析库如Apache POI
 */
class DocxParser(private val context: Context) : DocumentParser {
    override suspend fun parseDocument(filePath: String): DocumentContent {
        try {
            // TODO: 实现DOCX解析功能
            // 这里需要集成DOCX解析库，如Apache POI
            // 需要注意Android环境下的兼容性
            
            // 临时实现：返回文件基本信息
            val file = File(filePath)
            return DocumentContent(
                text = "DOCX文档解析功能待实现。文件名: ${file.name}",
                metadata = mapOf(
                    "fileSize" to file.length(),
                    "fileType" to "docx",
                    "needsImplementation" to true
                )
            )
        } catch (e: Exception) {
            throw IOException("DOCX文件解析失败: ${e.message}")
        }
    }

    override fun getSupportedExtensions(): List<String> = listOf("docx", "doc")
}

/**
 * CSV文档解析器
 */
class CsvParser : DocumentParser {
    override suspend fun parseDocument(filePath: String): DocumentContent {
        try {
            val file = File(filePath)
            val lines = file.readLines(Charsets.UTF_8)
            
            if (lines.isEmpty()) {
                return DocumentContent("", mapOf("rowCount" to 0))
            }
            
            // 将CSV转换为可读文本
            val header = lines.firstOrNull() ?: ""
            val dataRows = lines.drop(1)
            
            val textBuilder = StringBuilder()
            textBuilder.appendLine("CSV文档内容:")
            textBuilder.appendLine("表头: $header")
            textBuilder.appendLine("数据行数: ${dataRows.size}")
            textBuilder.appendLine()
            
            // 添加前几行数据作为示例
            dataRows.take(10).forEachIndexed { index, row ->
                textBuilder.appendLine("第${index + 1}行: $row")
            }
            
            if (dataRows.size > 10) {
                textBuilder.appendLine("... 还有${dataRows.size - 10}行数据")
            }
            
            return DocumentContent(
                text = textBuilder.toString(),
                metadata = mapOf(
                    "fileSize" to file.length(),
                    "rowCount" to lines.size,
                    "columnCount" to header.split(",").size,
                    "hasHeader" to true
                )
            )
        } catch (e: Exception) {
            throw IOException("CSV文件解析失败: ${e.message}")
        }
    }

    override fun getSupportedExtensions(): List<String> = listOf("csv")
}

/**
 * 文档解析管理器
 */
class DocumentParsingManager(private val context: Context) {
    
    /**
     * 解析文档
     * @param filePath 文件路径
     * @param fileType 文件类型
     * @return 解析结果
     */
    suspend fun parseDocument(filePath: String, fileType: String): Result<DocumentContent> {
        return try {
            val parser = DocumentParserFactory.getParser(fileType, context)
                ?: return Result.failure(Exception("不支持的文件类型: $fileType"))
            
            val content = parser.parseDocument(filePath)
            Result.success(content)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取支持的文件类型
     */
    fun getSupportedFileTypes(): List<String> {
        return listOf("txt", "pdf", "docx", "doc", "csv")
    }
    
    /**
     * 检查文件类型是否支持
     */
    fun isFileTypeSupported(fileType: String): Boolean {
        return getSupportedFileTypes().contains(fileType.lowercase())
    }
} 