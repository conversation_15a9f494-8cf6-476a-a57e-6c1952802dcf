package com.bei.rag.service

import com.bei.rag.model.TtsRequest
import com.bei.rag.model.TtsResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * Gemini TTS API服务接口
 */
interface GeminiTtsApiService {
    
    /**
     * 生成语音
     */
    @POST("v1beta/models/gemini-2.0-flash-exp:generateSpeech")
    suspend fun generateSpeech(
        @Body request: TtsRequest,
        @Query("key") apiKey: String
    ): Response<TtsResponse>
} 