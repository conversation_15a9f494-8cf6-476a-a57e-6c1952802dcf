package com.bei.rag.service

import android.content.Context
import android.util.Log
import com.bei.rag.database.dao.DocumentDao
import com.bei.rag.database.entity.DocumentEntity
import com.bei.rag.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 知识库管理器
 * 统一管理文档处理、分块、向量化等功能，支持本地和云端同步
 */
class KnowledgeBaseManager(
    private val context: Context,
    private val documentDao: DocumentDao,
    private val embeddingService: SiliconFlowEmbeddingService,
    private val parsingManager: DocumentParsingManager,
    private val semanticChunker: SemanticChunker,
    private val supabaseDocumentService: SupabaseDocumentService? = null,
    private val vectorSearchService: VectorSearchService? = null
) {
    
    companion object {
        private const val TAG = "KnowledgeBaseManager"
    }

    /**
     * 处理单个文档的完整流程
     * @param documentId 文档ID
     * @return 处理结果
     */
    suspend fun processDocument(documentId: Long): Result<ProcessingResult> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始处理文档 ID: $documentId")
            
            // 1. 获取文档信息
            val document = documentDao.getDocumentById(documentId)
                ?: return@withContext Result.failure(Exception("文档不存在: $documentId"))
            
            // 2. 更新处理状态
            documentDao.updateProcessingStatus(documentId, "processing")
            
            // 3. 解析文档内容
            Log.d(TAG, "解析文档内容: ${document.fileName}")
            val parseResult = parsingManager.parseDocument(document.filePath, document.fileType)
            
            val documentContent = parseResult.getOrElse { exception ->
                Log.e(TAG, "文档解析失败: ${exception.message}")
                documentDao.updateProcessingError(documentId, "failed", "文档解析失败: ${exception.message}")
                return@withContext Result.failure(exception)
            }
            
            // 4. 更新提取的文本内容
            documentDao.updateExtractedText(documentId, documentContent.text)
            
            // 5. 语义分块
            Log.d(TAG, "开始语义分块")
            val chunks = semanticChunker.chunkDocument(documentContent.text, documentId)
            val optimizedChunks = semanticChunker.optimizeChunks(chunks)
            
            Log.d(TAG, "分块完成，共 ${optimizedChunks.size} 个块")
            
            // 6. 生成向量嵌入
            Log.d(TAG, "开始生成向量嵌入")
            val embeddedChunks = generateEmbeddingsForChunks(optimizedChunks)
            
            embeddedChunks.getOrElse { exception ->
                Log.e(TAG, "向量生成失败: ${exception.message}")
                documentDao.updateProcessingError(documentId, "failed", "向量生成失败: ${exception.message}")
                return@withContext Result.failure(exception)
            }
            
            // 7. 更新文档状态
            documentDao.updateVectorizedStatus(
                documentId = documentId,
                isVectorized = true,
                chunkCount = optimizedChunks.size,
                status = "completed",
                time = System.currentTimeMillis()
            )
            
            // 8. 同步到云端（如果可用）
            supabaseDocumentService?.let { syncService ->
                try {
                    Log.d(TAG, "同步文档到云端: ${document.fileName}")
                    val updatedDocument = documentDao.getDocumentById(documentId)!!
                    syncService.uploadDocumentToSupabase(updatedDocument).getOrThrow()
                    Log.d(TAG, "云端同步成功: ${document.fileName}")
                } catch (e: Exception) {
                    Log.w(TAG, "云端同步失败: ${document.fileName}, 错误: ${e.message}")
                    // 不影响本地处理结果
                }
            }
            
            Log.d(TAG, "文档处理完成: ${document.fileName}")
            
            Result.success(ProcessingResult(
                documentId = documentId,
                fileName = document.fileName,
                chunkCount = optimizedChunks.size,
                processingTime = System.currentTimeMillis() - document.uploadTime
            ))
            
        } catch (e: Exception) {
            Log.e(TAG, "文档处理异常: ${e.message}", e)
            documentDao.updateProcessingError(documentId, "failed", "处理异常: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * 批量处理未处理的文档
     * @return 处理结果列表
     */
    suspend fun processUnprocessedDocuments(): Result<List<ProcessingResult>> = withContext(Dispatchers.IO) {
        try {
            val unprocessedDocuments = documentDao.getUnprocessedDocuments()
            Log.d(TAG, "发现 ${unprocessedDocuments.size} 个未处理文档")
            
            val results = mutableListOf<ProcessingResult>()
            
            for (document in unprocessedDocuments) {
                val result = processDocument(document.id)
                result.fold(
                    onSuccess = { processingResult ->
                        results.add(processingResult)
                        Log.d(TAG, "文档处理成功: ${document.fileName}")
                    },
                    onFailure = { exception ->
                        Log.e(TAG, "文档处理失败: ${document.fileName}, 错误: ${exception.message}")
                        // 继续处理下一个文档
                    }
                )
                
                // 添加延迟以避免API限制
                kotlinx.coroutines.delay(500)
            }
            
            Result.success(results)
        } catch (e: Exception) {
            Log.e(TAG, "批量处理文档异常: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 为文档块生成向量嵌入
     */
    private suspend fun generateEmbeddingsForChunks(chunks: List<DocumentChunk>): Result<List<DocumentChunk>> {
        try {
            val embeddedChunks = mutableListOf<DocumentChunk>()
            
            for (chunk in chunks) {
                val embeddingResult = embeddingService.generateEmbedding(chunk.content)
                
                embeddingResult.fold(
                    onSuccess = { embedding ->
                        val embeddedChunk = chunk.copy(embedding = embedding)
                        embeddedChunks.add(embeddedChunk)
                        Log.d(TAG, "块向量化成功: ${chunk.chunkIndex}")
                    },
                    onFailure = { exception ->
                        Log.e(TAG, "块向量化失败: ${chunk.chunkIndex}, 错误: ${exception.message}")
                        return Result.failure(exception)
                    }
                )
                
                // API限制延迟
                kotlinx.coroutines.delay(200)
            }
            
            return Result.success(embeddedChunks)
        } catch (e: Exception) {
            return Result.failure(e)
        }
    }

    /**
     * 搜索知识库
     * @param query 查询文本
     * @param limit 返回结果数量限制
     * @return 搜索结果
     */
    suspend fun searchKnowledgeBase(query: String, limit: Int = 5): Result<List<SearchResult>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "搜索知识库: $query")
            
            // 优先使用向量搜索服务（如果可用）
            vectorSearchService?.let { searchService ->
                try {
                    val request = RagQueryRequest(
                        query = query,
                        maxResults = limit,
                        similarityThreshold = 0.3
                    )
                    val response = searchService.search(request).getOrThrow()
                    
                    val searchResults = response.results.map { result ->
                        SearchResult(
                            documentId = result.documentId,
                            fileName = result.documentName ?: "未知文档",
                            content = result.content,
                            similarity = result.similarity.toFloat(),
                            metadata = result.metadata.plus("source" to "vector_search")
                        )
                    }
                    
                    Log.d(TAG, "向量搜索完成，找到 ${searchResults.size} 个结果")
                    return@withContext Result.success(searchResults)
                    
                } catch (e: Exception) {
                    Log.w(TAG, "向量搜索失败，降级到文本搜索: ${e.message}")
                    // 继续执行下面的文本搜索
                }
            }
            
            // 降级到文本搜索
            Log.d(TAG, "使用文本搜索")
            
            // 1. 生成查询向量（用于未来的相似度计算）
            val queryEmbeddingResult = embeddingService.generateEmbedding(query)
            val queryEmbedding = queryEmbeddingResult.getOrElse { exception ->
                Log.e(TAG, "查询向量生成失败: ${exception.message}")
                return@withContext Result.failure(exception)
            }
            
            // 2. 获取所有已向量化的文档
            val vectorizedDocuments = documentDao.getVectorizedDocuments()
            
            // 3. 文本相似度搜索
            val searchResults = mutableListOf<SearchResult>()
            
            for (document in vectorizedDocuments) {
                if (document.extractedText.isNotEmpty()) {
                    val similarity = calculateTextSimilarity(query, document.extractedText)
                    if (similarity > 0.3f) { // 相似度阈值
                        searchResults.add(SearchResult(
                            documentId = document.id,
                            fileName = document.fileName,
                            content = document.extractedText.take(200) + "...",
                            similarity = similarity,
                            metadata = mapOf(
                                "fileType" to document.fileType,
                                "source" to "text_search",
                                "uploadTime" to document.uploadTime,
                                "chunkCount" to document.chunkCount
                            )
                        ))
                    }
                }
            }
            
            // 4. 按相似度排序并限制结果数量
            val sortedResults = searchResults
                .sortedByDescending { it.similarity }
                .take(limit)
            
            Log.d(TAG, "搜索完成，返回 ${sortedResults.size} 个结果")
            Result.success(sortedResults)
            
        } catch (e: Exception) {
            Log.e(TAG, "知识库搜索异常: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 简单的文本相似度计算（实际项目中应使用向量相似度）
     */
    private fun calculateTextSimilarity(query: String, text: String): Float {
        val queryWords = query.lowercase().split(" ").toSet()
        val textWords = text.lowercase().split(" ").toSet()
        
        val intersection = queryWords.intersect(textWords)
        val union = queryWords.union(textWords)
        
        return if (union.isEmpty()) 0f else intersection.size.toFloat() / union.size.toFloat()
    }

    /**
     * 同步本地文档到云端
     */
    suspend fun syncToCloud(): Result<SyncStatus> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始同步本地文档到云端")
            
            supabaseDocumentService?.let { syncService ->
                val syncResult = syncService.syncLocalToSupabase().getOrThrow()
                Log.d(TAG, "云端同步完成")
                return@withContext Result.success(syncResult)
            }
            
            // 如果没有同步服务，返回离线状态
            Result.success(SyncStatus(
                isOnline = false,
                lastSyncTime = 0,
                pendingUploads = 0,
                pendingDownloads = 0,
                syncInProgress = false
            ))
            
        } catch (e: Exception) {
            Log.e(TAG, "云端同步失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 从云端同步文档到本地
     */
    suspend fun syncFromCloud(): Result<SyncStatus> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始从云端同步文档到本地")
            
            supabaseDocumentService?.let { syncService ->
                val syncResult = syncService.syncSupabaseToLocal().getOrThrow()
                Log.d(TAG, "本地同步完成")
                return@withContext Result.success(syncResult)
            }
            
            // 如果没有同步服务，返回离线状态
            Result.success(SyncStatus(
                isOnline = false,
                lastSyncTime = 0,
                pendingUploads = 0,
                pendingDownloads = 0,
                syncInProgress = false
            ))
            
        } catch (e: Exception) {
            Log.e(TAG, "本地同步失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取同步状态
     */
    suspend fun getSyncStatus(): Result<SyncStatus> = withContext(Dispatchers.IO) {
        try {
            supabaseDocumentService?.let { syncService ->
                return@withContext syncService.getSyncStatus()
            }
            
            // 如果没有同步服务，返回离线状态
            Result.success(SyncStatus(
                isOnline = false,
                lastSyncTime = 0,
                pendingUploads = 0,
                pendingDownloads = 0,
                syncInProgress = false
            ))
            
        } catch (e: Exception) {
            Log.e(TAG, "获取同步状态失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 获取知识库统计信息
     */
    suspend fun getKnowledgeBaseStats(): KnowledgeBaseStats = withContext(Dispatchers.IO) {
        try {
            val totalDocuments = documentDao.getDocumentCount()
            val vectorizedDocuments = documentDao.getVectorizedDocumentCount()
            val unprocessedDocuments = documentDao.getUnprocessedDocuments().size
            val totalSize = documentDao.getTotalFileSize()
            
            KnowledgeBaseStats(
                totalDocuments = totalDocuments,
                vectorizedDocuments = vectorizedDocuments,
                unprocessedDocuments = unprocessedDocuments,
                totalSize = totalSize,
                processingRate = if (totalDocuments > 0) (vectorizedDocuments.toFloat() / totalDocuments) else 0f
            )
        } catch (e: Exception) {
            Log.e(TAG, "获取知识库统计信息失败: ${e.message}")
            KnowledgeBaseStats()
        }
    }

    /**
     * 重新处理文档
     */
    suspend fun reprocessDocument(documentId: Long): Result<ProcessingResult> {
        // 重置文档状态
        documentDao.updateProcessingStatus(documentId, "pending")
        documentDao.updateVectorizedStatus(documentId, false, 0, "pending", 0)
        
        // 重新处理
        return processDocument(documentId)
    }

    /**
     * 删除文档及其相关数据
     */
    suspend fun deleteDocument(documentId: Long): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // TODO: 删除向量存储中的相关数据
            
            // 删除数据库记录
            documentDao.deleteDocumentById(documentId)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

/**
 * 处理结果数据类
 */
data class ProcessingResult(
    val documentId: Long,
    val fileName: String,
    val chunkCount: Int,
    val processingTime: Long
)

/**
 * 搜索结果数据类
 */
data class SearchResult(
    val documentId: Long,
    val fileName: String,
    val content: String,
    val similarity: Float,
    val metadata: Map<String, Any> = emptyMap()
)

/**
 * 知识库统计信息数据类
 */
data class KnowledgeBaseStats(
    val totalDocuments: Int = 0,
    val vectorizedDocuments: Int = 0,
    val unprocessedDocuments: Int = 0,
    val totalSize: Long = 0,
    val processingRate: Float = 0f
) 