package com.bei.rag.service

import com.bei.rag.model.*
import com.bei.rag.utils.ApiConfig
import com.bei.rag.utils.PromptBuilder
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.logging.HttpLoggingInterceptor
import java.io.IOException
import java.util.concurrent.TimeUnit

class OpenRouterApiService {

    // 使用ApiConfig安全获取配置
    private val apiKey: String by lazy { ApiConfig.getOpenRouterApiKey() }
    private val model: String by lazy { ApiConfig.getGeminiModel() }
    
    private val gson = Gson()
    private val client: OkHttpClient
    
    init {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        
        client = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }
    
    suspend fun sendMessage(userMessage: String): Result<String> = withContext(Dispatchers.IO) {
        try {
            // 检查API密钥是否已配置
            if (!ApiConfig.isApiKeyConfigured()) {
                return@withContext Result.failure(Exception("API密钥未配置，请在local.properties中设置OPENROUTER_API_KEY"))
            }

            // 为直接调用添加标准提示词格式
            val enhancedMessage = PromptBuilder.buildNoContextPrompt(userMessage)

            val request = createChatRequest(enhancedMessage)
            val requestBody = gson.toJson(request)
                .toRequestBody("application/json".toMediaType())

            val httpRequest = Request.Builder()
                .url("${ApiConfig.OPENROUTER_BASE_URL}chat/completions")
                .addHeader("Authorization", "Bearer $apiKey")
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build()
            
            val response = client.newCall(httpRequest).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val chatResponse = gson.fromJson(responseBody, ChatResponse::class.java)
                    val aiMessage = chatResponse.choices.firstOrNull()?.message?.content
                    if (aiMessage != null) {
                        Result.success(aiMessage)
                    } else {
                        Result.failure(Exception("AI回复为空"))
                    }
                } else {
                    Result.failure(Exception("响应体为空"))
                }
            } else {
                val errorBody = response.body?.string() ?: "未知错误"
                Result.failure(Exception("API请求失败: ${response.code} - $errorBody"))
            }
        } catch (e: IOException) {
            Result.failure(Exception("网络连接失败: ${e.message}"))
        } catch (e: Exception) {
            Result.failure(Exception("请求处理失败: ${e.message}"))
        }
    }

    /**
     * 发送原始消息（已经包含完整提示词的消息）
     * 用于RAG系统内部调用，不会再次添加提示词格式
     */
    suspend fun sendRawMessage(fullPrompt: String): Result<String> = withContext(Dispatchers.IO) {
        try {
            // 检查API密钥是否已配置
            if (!ApiConfig.isApiKeyConfigured()) {
                return@withContext Result.failure(Exception("API密钥未配置，请在local.properties中设置OPENROUTER_API_KEY"))
            }

            val request = createChatRequest(fullPrompt)
            val requestBody = gson.toJson(request)
                .toRequestBody("application/json".toMediaType())

            val httpRequest = Request.Builder()
                .url("${ApiConfig.OPENROUTER_BASE_URL}chat/completions")
                .addHeader("Authorization", "Bearer $apiKey")
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build()
            
            val response = client.newCall(httpRequest).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val chatResponse = gson.fromJson(responseBody, ChatResponse::class.java)
                    val aiMessage = chatResponse.choices.firstOrNull()?.message?.content
                    if (aiMessage != null) {
                        Result.success(aiMessage)
                    } else {
                        Result.failure(Exception("AI回复为空"))
                    }
                } else {
                    Result.failure(Exception("响应体为空"))
                }
            } else {
                val errorBody = response.body?.string() ?: "未知错误"
                Result.failure(Exception("API请求失败: ${response.code} - $errorBody"))
            }
        } catch (e: IOException) {
            Result.failure(Exception("网络连接失败: ${e.message}"))
        } catch (e: Exception) {
            Result.failure(Exception("请求处理失败: ${e.message}"))
        }
    }
    
    private fun createChatRequest(userMessage: String): ChatRequest {
        val content = Content(
            type = "text",
            text = userMessage
        )
        
        val message = Message(
            role = "user",
            content = listOf(content)
        )
        
        return ChatRequest(
            model = model,
            messages = listOf(message)
        )
    }
}
