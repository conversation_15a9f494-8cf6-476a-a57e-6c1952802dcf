package com.bei.rag.service

import android.content.Context
import android.util.Log
import com.bei.rag.model.*
import com.bei.rag.utils.PromptBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * RAG查询引擎
 * 整合向量搜索和AI回答生成
 */
class RagQueryEngine(
    private val context: Context,
    private val vectorSearchService: VectorSearchService,
    private val openRouterApiService: OpenRouterApiService
) {
    
    companion object {
        private const val TAG = "RagQueryEngine"
    }
    
    /**
     * 执行RAG查询
     */
    suspend fun query(request: RagQueryRequest): Result<RagQueryResponse> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting RAG query: ${request.query}")
            
            // 1. 向量搜索获取相关文档
            val searchResponse = vectorSearchService.search(request).getOrThrow()
            
            // 2. 根据搜索模式和结果处理响应
            val finalResponse = if (searchResponse.results.isEmpty()) {
                if (request.useTopKSearch) {
                    // TOP-K模式下，如果仍然没有结果，说明知识库为空
                    Log.d(TAG, "TOP-K search found no documents (knowledge base might be empty)")
                    RagQueryResponse(
                        query = request.query,
                        results = emptyList(),
                        context = "知识库中暂无相关文档。",
                        totalResults = 0
                    )
                } else {
                    // 阈值模式下没有结果，尝试降级到TOP-K搜索
                    Log.d(TAG, "Threshold search found no results, attempting TOP-K fallback")
                    val fallbackRequest = request.copy(useTopKSearch = true)
                    val fallbackResponse = vectorSearchService.search(fallbackRequest).getOrNull()
                    
                    if (fallbackResponse != null && fallbackResponse.results.isNotEmpty()) {
                        Log.d(TAG, "TOP-K fallback found ${fallbackResponse.results.size} results")
                        val enhancedContext = buildEnhancedContextForLowSimilarity(
                            fallbackResponse.context, 
                            request.query,
                            fallbackResponse.results.maxOfOrNull { it.similarity } ?: 0.0
                        )
                        fallbackResponse.copy(context = enhancedContext)
                    } else {
                        Log.d(TAG, "No relevant documents found even with TOP-K fallback")
                        RagQueryResponse(
                            query = request.query,
                            results = emptyList(),
                            context = "",
                            totalResults = 0
                        )
                    }
                }
            } else {
                // 有搜索结果的情况
                val enhancedContext = if (request.useTopKSearch) {
                    // TOP-K模式：检查最高相似度，如果较低则特殊处理
                    val maxSimilarity = searchResponse.results.maxOfOrNull { it.similarity } ?: 0.0
                    if (maxSimilarity < 0.5) {
                        buildEnhancedContextForLowSimilarity(searchResponse.context, request.query, maxSimilarity)
                    } else {
                        buildEnhancedContext(searchResponse.context, request.query)
                    }
                } else {
                    // 阈值模式：使用标准上下文构建
                    buildEnhancedContext(searchResponse.context, request.query)
                }
                
                searchResponse.copy(context = enhancedContext)
            }
            
            Log.d(TAG, "RAG query completed with ${finalResponse.results.size} results")
            Result.success(finalResponse)
            
        } catch (e: Exception) {
            Log.e(TAG, "RAG query failed: ${request.query}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 执行RAG查询并生成AI回答
     */
    suspend fun queryWithAiResponse(request: RagQueryRequest): Result<Pair<RagQueryResponse, String>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting RAG query with AI response: ${request.query}")
            
            // 1. 执行RAG查询
            val ragResponse = query(request).getOrThrow()
            
            // 2. 生成AI回答
            val aiResponse = if (ragResponse.results.isNotEmpty()) {
                generateAiResponseWithContext(request.query, ragResponse.context).getOrThrow()
            } else {
                generateAiResponseWithoutContext(request.query).getOrThrow()
            }
            
            Log.d(TAG, "RAG query with AI response completed")
            Result.success(Pair(ragResponse, aiResponse))
            
        } catch (e: Exception) {
            Log.e(TAG, "RAG query with AI response failed: ${request.query}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 构建增强的上下文
     */
    private fun buildEnhancedContext(originalContext: String, query: String): String {
        return PromptBuilder.buildStandardPrompt(originalContext, query)
    }
    
    /**
     * 为低相似度结果构建特殊的上下文
     */
    private fun buildEnhancedContextForLowSimilarity(originalContext: String, query: String, maxSimilarity: Double): String {
        return PromptBuilder.buildLowSimilarityPrompt(originalContext, query, maxSimilarity)
    }
    
    /**
     * 使用上下文生成AI回答
     */
    private suspend fun generateAiResponseWithContext(query: String, context: String): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating AI response with context")
            
            val prompt = buildEnhancedContext(context, query)
            val result = openRouterApiService.sendRawMessage(prompt).getOrThrow()
            
            Result.success(result)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to generate AI response with context", e)
            Result.failure(e)
        }
    }
    
    /**
     * 不使用上下文生成AI回答
     */
    private suspend fun generateAiResponseWithoutContext(query: String): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating AI response without context")
            
            val prompt = PromptBuilder.buildNoContextPrompt(query)
            val result = openRouterApiService.sendRawMessage(prompt).getOrThrow()
            
            Result.success(result)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to generate AI response without context", e)
            Result.failure(e)
        }
    }
    
    /**
     * 批量查询
     */
    suspend fun batchQuery(queries: List<String>): Result<List<RagQueryResponse>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting batch query for ${queries.size} queries")
            
            val results = mutableListOf<RagQueryResponse>()
            
            queries.forEach { query ->
                try {
                    val request = RagQueryRequest(query = query)
                    val response = query(request).getOrThrow()
                    results.add(response)
                } catch (e: Exception) {
                    Log.w(TAG, "Batch query failed for: $query", e)
                    // 添加空响应以保持索引一致性
                    results.add(
                        RagQueryResponse(
                            query = query,
                            results = emptyList(),
                            context = "",
                            totalResults = 0
                        )
                    )
                }
            }
            
            Log.d(TAG, "Batch query completed: ${results.size} results")
            Result.success(results)
            
        } catch (e: Exception) {
            Log.e(TAG, "Batch query failed", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取查询建议
     */
    suspend fun getQuerySuggestions(partialQuery: String): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting query suggestions for: $partialQuery")
            
            // 简单的查询建议逻辑
            val suggestions = mutableListOf<String>()
            
            // 基于部分查询生成建议
            val keywords = partialQuery.split("\\s+".toRegex()).filter { it.isNotEmpty() }
            
            if (keywords.isNotEmpty()) {
                val lastKeyword = keywords.last()
                
                // 一些常用的查询模式
                val patterns = listOf(
                    "${partialQuery}是什么？",
                    "${partialQuery}怎么办？",
                    "${partialQuery}的作用",
                    "${partialQuery}的原理",
                    "如何${partialQuery}？"
                )
                
                suggestions.addAll(patterns.take(3))
            }
            
            // 添加一些通用建议
            if (suggestions.size < 5) {
                suggestions.addAll(
                    listOf(
                        "请详细解释",
                        "有什么注意事项？",
                        "相关的最佳实践"
                    ).take(5 - suggestions.size)
                )
            }
            
            Result.success(suggestions)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get query suggestions", e)
            Result.failure(e)
        }
    }
    
    /**
     * 验证查询质量
     */
    fun validateQuery(query: String): QueryValidationResult {
        val trimmedQuery = query.trim()
        
        return when {
            trimmedQuery.isEmpty() -> QueryValidationResult(
                isValid = false,
                message = "查询不能为空"
            )
            trimmedQuery.length < 2 -> QueryValidationResult(
                isValid = false,
                message = "查询过短，请输入更详细的问题"
            )
            trimmedQuery.length > 500 -> QueryValidationResult(
                isValid = false,
                message = "查询过长，请简化您的问题"
            )
            else -> QueryValidationResult(
                isValid = true,
                message = "查询格式正确"
            )
        }
    }
    
    /**
     * 获取引擎统计信息
     */
    suspend fun getEngineStatistics(): Result<Map<String, Any>> = withContext(Dispatchers.IO) {
        try {
            val searchStats = vectorSearchService.getSearchStatistics().getOrDefault(emptyMap())
            
            val engineStats = mutableMapOf<String, Any>()
            engineStats.putAll(searchStats)
            engineStats["ragEngineVersion"] = "1.0.0"
            engineStats["maxContextLength"] = PromptBuilder.getMaxContextLength()
            
            Result.success(engineStats)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get engine statistics", e)
            Result.failure(e)
        }
    }
}

/**
 * 查询验证结果
 */
data class QueryValidationResult(
    val isValid: Boolean,
    val message: String
) 