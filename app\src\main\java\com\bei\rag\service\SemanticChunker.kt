package com.bei.rag.service

import com.bei.rag.model.DocumentChunk
import java.util.*
import kotlin.math.min

/**
 * 语义分块器
 * 将长文本按语义单元进行智能分块
 */
class SemanticChunker {
    
    companion object {
        private const val DEFAULT_MAX_TOKENS = 512
        private const val DEFAULT_OVERLAP_TOKENS = 50
        private const val MIN_CHUNK_SIZE = 50
        private const val SENTENCE_SEPARATORS = ".!?。！？"
        private const val PARAGRAPH_SEPARATORS = "\n\n"
    }

    /**
     * 对文档内容进行语义分块
     * @param content 文档内容
     * @param documentId 文档ID
     * @param maxTokens 每块最大token数
     * @param overlapTokens 块之间重叠的token数
     * @return 分块结果列表
     */
    fun chunkDocument(
        content: String,
        documentId: Long,
        maxTokens: Int = DEFAULT_MAX_TOKENS,
        overlapTokens: Int = DEFAULT_OVERLAP_TOKENS
    ): List<DocumentChunk> {
        if (content.isBlank()) {
            return emptyList()
        }

        // 首先按段落分割
        val paragraphs = splitByParagraphs(content)
        val chunks = mutableListOf<DocumentChunk>()
        var currentChunk = StringBuilder()
        var currentStartIndex = 0
        var chunkIndex = 0

        for (paragraph in paragraphs) {
            val paragraphTokens = estimateTokenCount(paragraph)
            val currentTokens = estimateTokenCount(currentChunk.toString())

            // 如果当前段落加入后会超过最大长度
            if (currentTokens + paragraphTokens > maxTokens && currentChunk.isNotEmpty()) {
                // 保存当前块
                val chunkText = currentChunk.toString().trim()
                if (chunkText.length >= MIN_CHUNK_SIZE) {
                    chunks.add(createDocumentChunk(
                        documentId = documentId,
                        content = chunkText,
                        startIndex = currentStartIndex,
                        endIndex = currentStartIndex + chunkText.length,
                        chunkIndex = chunkIndex++
                    ))
                }

                // 开始新块，考虑重叠
                val overlapText = getOverlapText(chunkText, overlapTokens)
                currentChunk = StringBuilder(overlapText)
                currentStartIndex = content.indexOf(paragraph)
            }

            // 如果单个段落就超过最大长度，需要进一步分割
            if (paragraphTokens > maxTokens) {
                val sentenceChunks = splitLongParagraph(paragraph, maxTokens, overlapTokens)
                for (sentenceChunk in sentenceChunks) {
                    if (sentenceChunk.trim().length >= MIN_CHUNK_SIZE) {
                        chunks.add(createDocumentChunk(
                            documentId = documentId,
                            content = sentenceChunk.trim(),
                            startIndex = content.indexOf(sentenceChunk.trim()),
                            endIndex = content.indexOf(sentenceChunk.trim()) + sentenceChunk.trim().length,
                            chunkIndex = chunkIndex++
                        ))
                    }
                }
            } else {
                // 添加段落到当前块
                if (currentChunk.isNotEmpty()) {
                    currentChunk.append("\n\n")
                }
                currentChunk.append(paragraph)
            }
        }

        // 处理最后一个块
        val finalChunk = currentChunk.toString().trim()
        if (finalChunk.length >= MIN_CHUNK_SIZE) {
            chunks.add(createDocumentChunk(
                documentId = documentId,
                content = finalChunk,
                startIndex = currentStartIndex,
                endIndex = currentStartIndex + finalChunk.length,
                chunkIndex = chunkIndex
            ))
        }

        return chunks
    }

    /**
     * 按段落分割文本
     */
    private fun splitByParagraphs(text: String): List<String> {
        return text.split(PARAGRAPH_SEPARATORS)
            .map { it.trim() }
            .filter { it.isNotEmpty() }
    }

    /**
     * 分割过长的段落
     */
    private fun splitLongParagraph(
        paragraph: String,
        maxTokens: Int,
        overlapTokens: Int
    ): List<String> {
        val sentences = splitBySentences(paragraph)
        val chunks = mutableListOf<String>()
        var currentChunk = StringBuilder()

        for (sentence in sentences) {
            val sentenceTokens = estimateTokenCount(sentence)
            val currentTokens = estimateTokenCount(currentChunk.toString())

            if (currentTokens + sentenceTokens > maxTokens && currentChunk.isNotEmpty()) {
                chunks.add(currentChunk.toString().trim())
                
                // 开始新块，考虑重叠
                val overlapText = getOverlapText(currentChunk.toString(), overlapTokens)
                currentChunk = StringBuilder(overlapText)
            }

            if (currentChunk.isNotEmpty()) {
                currentChunk.append(" ")
            }
            currentChunk.append(sentence)
        }

        if (currentChunk.isNotEmpty()) {
            chunks.add(currentChunk.toString().trim())
        }

        return chunks
    }

    /**
     * 按句子分割文本
     */
    private fun splitBySentences(text: String): List<String> {
        val sentences = mutableListOf<String>()
        var currentSentence = StringBuilder()

        for (char in text) {
            currentSentence.append(char)
            
            if (SENTENCE_SEPARATORS.contains(char)) {
                val sentence = currentSentence.toString().trim()
                if (sentence.isNotEmpty()) {
                    sentences.add(sentence)
                }
                currentSentence = StringBuilder()
            }
        }

        // 处理最后一个句子
        val lastSentence = currentSentence.toString().trim()
        if (lastSentence.isNotEmpty()) {
            sentences.add(lastSentence)
        }

        return sentences
    }

    /**
     * 获取重叠文本
     */
    private fun getOverlapText(text: String, overlapTokens: Int): String {
        if (overlapTokens <= 0) return ""
        
        val words = text.split(" ")
        val overlapWords = words.takeLast(min(overlapTokens, words.size / 2))
        return overlapWords.joinToString(" ")
    }

    /**
     * 估算token数量（简化版本）
     * 实际项目中可以使用更精确的tokenizer
     */
    private fun estimateTokenCount(text: String): Int {
        if (text.isBlank()) return 0
        
        // 简化的token估算：中文字符按1个token，英文单词按1个token
        var tokenCount = 0
        var i = 0
        
        while (i < text.length) {
            val char = text[i]
            if (char.code > 127) {
                // 中文字符
                tokenCount++
            } else if (char.isLetter()) {
                // 英文单词，跳过整个单词
                while (i < text.length && (text[i].isLetter() || text[i].isDigit())) {
                    i++
                }
                tokenCount++
                continue
            }
            i++
        }
        
        return tokenCount
    }

    /**
     * 创建文档块对象
     */
    private fun createDocumentChunk(
        documentId: Long,
        content: String,
        startIndex: Int,
        endIndex: Int,
        chunkIndex: Int
    ): DocumentChunk {
        val metadata = mapOf(
            "tokenCount" to estimateTokenCount(content),
            "characterCount" to content.length,
            "wordCount" to content.split(" ").size
        )

        return DocumentChunk(
            id = UUID.randomUUID().toString(),
            documentId = documentId,
            content = content,
            startIndex = startIndex,
            endIndex = endIndex,
            chunkIndex = chunkIndex,
            metadata = metadata
        )
    }

    /**
     * 优化分块结果
     * 合并过小的块，分割过大的块
     */
    fun optimizeChunks(chunks: List<DocumentChunk>): List<DocumentChunk> {
        val optimizedChunks = mutableListOf<DocumentChunk>()
        var i = 0

        while (i < chunks.size) {
            val currentChunk = chunks[i]
            val currentTokens = estimateTokenCount(currentChunk.content)

            // 如果块太小，尝试与下一个块合并
            if (currentTokens < MIN_CHUNK_SIZE && i + 1 < chunks.size) {
                val nextChunk = chunks[i + 1]
                val combinedTokens = currentTokens + estimateTokenCount(nextChunk.content)

                if (combinedTokens <= DEFAULT_MAX_TOKENS) {
                    // 合并块
                    val combinedContent = "${currentChunk.content}\n\n${nextChunk.content}"
                    val combinedChunk = createDocumentChunk(
                        documentId = currentChunk.documentId,
                        content = combinedContent,
                        startIndex = currentChunk.startIndex,
                        endIndex = nextChunk.endIndex,
                        chunkIndex = currentChunk.chunkIndex
                    )
                    optimizedChunks.add(combinedChunk)
                    i += 2 // 跳过下一个块
                    continue
                }
            }

            optimizedChunks.add(currentChunk)
            i++
        }

        return optimizedChunks
    }
} 