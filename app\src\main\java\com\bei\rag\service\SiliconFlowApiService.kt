package com.bei.rag.service

import com.bei.rag.model.VoiceToTextResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.*

/**
 * SiliconFlow API接口
 */
interface SiliconFlowApiService {
    
    @Multipart
    @POST("/v1/audio/transcriptions")
    suspend fun transcribeAudio(
        @Header("Authorization") authorization: String,
        @Part("model") model: RequestBody,
        @Part file: MultipartBody.Part
    ): Response<VoiceToTextResponse>
}
