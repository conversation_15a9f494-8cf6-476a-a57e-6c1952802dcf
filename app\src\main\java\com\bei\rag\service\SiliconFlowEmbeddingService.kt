package com.bei.rag.service

import com.bei.rag.model.EmbeddingRequest
import com.bei.rag.model.EmbeddingResponse
import com.bei.rag.utils.ApiConfig
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.logging.HttpLoggingInterceptor
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * 硅基流动嵌入服务
 * 提供文本向量化功能
 */
class SiliconFlowEmbeddingService {

    private val apiKey: String by lazy { ApiConfig.getSiliconFlowApiKey() }
    private val gson = Gson()
    private val client: OkHttpClient

    init {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        client = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build()
    }

    /**
     * 生成单个文本的向量嵌入
     * @param text 要嵌入的文本
     * @param model 使用的模型，默认为BAAI/bge-m3
     * @return 向量嵌入结果
     */
    suspend fun generateEmbedding(
        text: String,
        model: String = "BAAI/bge-m3"
    ): Result<FloatArray> = withContext(Dispatchers.IO) {
        try {
            // 检查API密钥是否已配置
            if (!ApiConfig.isSiliconFlowApiKeyConfigured()) {
                return@withContext Result.failure(Exception("硅基流动API密钥未配置"))
            }

            // 构建请求
            val request = EmbeddingRequest(
                model = model,
                input = text,
                encodingFormat = "float"
            )

            val requestBody = gson.toJson(request)
                .toRequestBody("application/json".toMediaType())

            val httpRequest = Request.Builder()
                .url("${ApiConfig.SILICONFLOW_BASE_URL}embeddings")
                .addHeader("Authorization", "Bearer $apiKey")
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build()

            val response = client.newCall(httpRequest).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val embeddingResponse = gson.fromJson(responseBody, EmbeddingResponse::class.java)
                    val embedding = embeddingResponse.data.firstOrNull()?.embedding
                    if (embedding != null) {
                        Result.success(embedding.toFloatArray())
                    } else {
                        Result.failure(Exception("嵌入响应为空"))
                    }
                } else {
                    Result.failure(Exception("响应体为空"))
                }
            } else {
                val errorBody = response.body?.string() ?: "未知错误"
                Result.failure(Exception("嵌入API请求失败: ${response.code} - $errorBody"))
            }
        } catch (e: IOException) {
            Result.failure(Exception("网络连接失败: ${e.message}"))
        } catch (e: Exception) {
            Result.failure(Exception("嵌入处理失败: ${e.message}"))
        }
    }

    /**
     * 批量生成文本的向量嵌入
     * @param texts 要嵌入的文本列表
     * @param model 使用的模型，默认为BAAI/bge-m3
     * @return 向量嵌入结果列表
     */
    suspend fun batchGenerateEmbeddings(
        texts: List<String>,
        model: String = "BAAI/bge-m3"
    ): Result<List<FloatArray>> = withContext(Dispatchers.IO) {
        try {
            val embeddings = mutableListOf<FloatArray>()
            
            // 由于API限制，逐个处理文本
            for (text in texts) {
                val result = generateEmbedding(text, model)
                result.fold(
                    onSuccess = { embedding ->
                        embeddings.add(embedding)
                    },
                    onFailure = { exception ->
                        return@withContext Result.failure(exception)
                    }
                )
                
                // 添加短暂延迟以避免API限制
                kotlinx.coroutines.delay(100)
            }
            
            Result.success(embeddings)
        } catch (e: Exception) {
            Result.failure(Exception("批量嵌入处理失败: ${e.message}"))
        }
    }

    /**
     * 计算两个向量之间的余弦相似度
     * @param vector1 第一个向量
     * @param vector2 第二个向量
     * @return 余弦相似度值 (0-1之间)
     */
    fun calculateCosineSimilarity(vector1: FloatArray, vector2: FloatArray): Float {
        if (vector1.size != vector2.size) {
            throw IllegalArgumentException("向量维度不匹配")
        }

        var dotProduct = 0.0f
        var norm1 = 0.0f
        var norm2 = 0.0f

        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            norm1 += vector1[i] * vector1[i]
            norm2 += vector2[i] * vector2[i]
        }

        return if (norm1 == 0.0f || norm2 == 0.0f) {
            0.0f
        } else {
            dotProduct / (kotlin.math.sqrt(norm1) * kotlin.math.sqrt(norm2))
        }
    }

    /**
     * 检查服务是否可用
     * @return true如果服务可用
     */
    fun isServiceAvailable(): Boolean {
        return ApiConfig.isSiliconFlowApiKeyConfigured()
    }
} 