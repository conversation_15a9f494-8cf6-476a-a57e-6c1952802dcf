package com.bei.rag.service

import android.content.Context
import android.util.Log
import com.bei.rag.BuildConfig
import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.gotrue.Auth
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.realtime.Realtime
import io.github.jan.supabase.storage.Storage

/**
 * Supabase配置和客户端管理
 */
object SupabaseConfig {
    
    private const val TAG = "SupabaseConfig"
    
    private var _client: SupabaseClient? = null
    
    /**
     * 获取Supabase客户端实例
     */
    val client: SupabaseClient
        get() = _client ?: throw IllegalStateException("Supabase client not initialized. Call initialize() first.")
    
    /**
     * 检查是否已初始化
     */
    val isInitialized: Boolean
        get() = _client != null
    
    /**
     * 初始化Supabase客户端
     */
    fun initialize(context: Context) {
        if (_client != null) {
            Log.d(TAG, "Supabase client already initialized")
            return
        }
        
        try {
            val supabaseUrl = BuildConfig.SUPABASE_URL
            val supabaseKey = BuildConfig.SUPABASE_ANON_KEY
            
            if (supabaseUrl.isEmpty() || supabaseKey.isEmpty()) {
                throw IllegalStateException("Supabase URL or key not configured in BuildConfig")
            }
            
            Log.d(TAG, "Initializing Supabase client with URL: $supabaseUrl")
            
            _client = createSupabaseClient(
                supabaseUrl = supabaseUrl,
                supabaseKey = supabaseKey
            ) {
                install(Postgrest) {
                    // PostgreSQL REST API配置
                }
                install(Auth) {
                    // 认证配置（暂时不使用）
                }
                install(Realtime) {
                    // 实时功能配置（暂时不使用）
                }
                install(Storage) {
                    // 存储功能配置（暂时不使用）
                }
            }
            
            Log.d(TAG, "Supabase client initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Supabase client", e)
            throw e
        }
    }
    
    /**
     * 获取配置信息
     */
    fun getConfig(): Map<String, String> {
        return mapOf(
            "url" to BuildConfig.SUPABASE_URL,
            "hasKey" to (BuildConfig.SUPABASE_ANON_KEY.isNotEmpty()).toString(),
            "initialized" to isInitialized.toString()
        )
    }
    
    /**
     * 测试连接
     */
    suspend fun testConnection(): Result<Boolean> {
        return try {
            if (!isInitialized) {
                throw IllegalStateException("Client not initialized")
            }
            
            // 执行一个简单的查询来测试连接
            client.from("documents").select() {
                limit(1)
            }
            
            Log.d(TAG, "Supabase connection test successful")
            Result.success(true)
            
        } catch (e: Exception) {
            Log.e(TAG, "Supabase connection test failed", e)
            Result.failure(e)
        }
    }
    
    /**
     * 清理资源
     */
    suspend fun cleanup() {
        _client?.close()
        _client = null
        Log.d(TAG, "Supabase client cleaned up")
    }
} 