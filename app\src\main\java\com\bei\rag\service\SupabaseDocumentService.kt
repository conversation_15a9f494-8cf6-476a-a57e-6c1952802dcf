package com.bei.rag.service

import android.content.Context
import android.util.Log
import com.bei.rag.database.entity.DocumentEntity
import com.bei.rag.database.dao.DocumentDao
import com.bei.rag.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Supabase文档同步服务
 * 负责本地SQLite和Supabase之间的数据同步
 */
class SupabaseDocumentService(
    private val context: Context,
    private val localDocumentDao: DocumentDao,
    private val vectorService: SupabaseVectorService,
    private val embeddingService: SiliconFlowEmbeddingService,
    private val semanticChunker: SemanticChunker
) {
    
    companion object {
        private const val TAG = "SupabaseDocumentService"
    }
    
    /**
     * 同步本地文档到Supabase
     */
    suspend fun syncLocalToSupabase(): Result<SyncStatus> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting local to Supabase sync")
            
            // 获取本地已向量化但未同步到云端的文档
            val localVectorizedDocs = localDocumentDao.getVectorizedDocuments()
            
            var uploadedCount = 0
            var failedCount = 0
            
            localVectorizedDocs.forEach { localDoc ->
                try {
                    // 检查云端是否已存在
                    val cloudDocs = vectorService.getDocuments(1000).getOrThrow()
                    val existingDoc = cloudDocs.find { it.fileName == localDoc.fileName && it.fileSize == localDoc.fileSize }
                    
                    if (existingDoc == null) {
                        // 上传到云端
                        uploadDocumentToSupabase(localDoc).getOrThrow()
                        uploadedCount++
                    }
                    
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to sync document: ${localDoc.fileName}", e)
                    failedCount++
                }
            }
            
            val syncStatus = SyncStatus(
                isOnline = true,
                lastSyncTime = System.currentTimeMillis(),
                pendingUploads = failedCount,
                pendingDownloads = 0,
                syncInProgress = false
            )
            
            Log.d(TAG, "Sync completed: uploaded=$uploadedCount, failed=$failedCount")
            Result.success(syncStatus)
            
        } catch (e: Exception) {
            Log.e(TAG, "Sync failed", e)
            Result.failure(e)
        }
    }
    
    /**
     * 从Supabase同步文档到本地
     */
    suspend fun syncSupabaseToLocal(): Result<SyncStatus> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting Supabase to local sync")
            
            // 获取云端文档列表
            val cloudDocs = vectorService.getDocuments(1000).getOrThrow()
            val localDocs = localDocumentDao.getAllDocumentsSync()
            
            var downloadedCount = 0
            var failedCount = 0
            
            cloudDocs.forEach { cloudDoc ->
                try {
                    // 检查本地是否已存在
                    val existingLocal = localDocs.find { it.fileName == cloudDoc.fileName && it.fileSize == cloudDoc.fileSize }
                    
                    if (existingLocal == null) {
                        // 下载到本地（仅元数据，不下载文件内容）
                        downloadDocumentFromSupabase(cloudDoc).getOrThrow()
                        downloadedCount++
                    }
                    
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to download document: ${cloudDoc.fileName}", e)
                    failedCount++
                }
            }
            
            val syncStatus = SyncStatus(
                isOnline = true,
                lastSyncTime = System.currentTimeMillis(),
                pendingUploads = 0,
                pendingDownloads = failedCount,
                syncInProgress = false
            )
            
            Log.d(TAG, "Download sync completed: downloaded=$downloadedCount, failed=$failedCount")
            Result.success(syncStatus)
            
        } catch (e: Exception) {
            Log.e(TAG, "Download sync failed", e)
            Result.failure(e)
        }
    }
    
    /**
     * 上传单个文档到Supabase
     */
    suspend fun uploadDocumentToSupabase(localDoc: DocumentEntity): Result<SupabaseDocument> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Uploading document to Supabase: ${localDoc.fileName}")
            
            // 转换为Supabase格式
            val supabaseDoc = SupabaseDocument(
                id = localDoc.id,
                fileName = localDoc.fileName,
                fileType = localDoc.fileType,
                fileSize = localDoc.fileSize,
                uploadTime = localDoc.uploadTime,
                isVectorized = localDoc.isVectorized,
                chunkCount = localDoc.chunkCount,
                processingStatus = localDoc.processingStatus,
                errorMessage = localDoc.errorMessage,
                extractedText = localDoc.extractedText,
                lastProcessedTime = localDoc.lastProcessedTime
            )
            
            // 插入文档记录
            val insertedDoc = vectorService.insertDocument(supabaseDoc).getOrThrow()
            
            // 如果文档已向量化，上传向量块
            if (localDoc.isVectorized && localDoc.extractedText.isNotEmpty()) {
                uploadDocumentChunks(insertedDoc.id, localDoc.extractedText).getOrThrow()
            }
            
            Log.d(TAG, "Document uploaded successfully: ${localDoc.fileName}")
            Result.success(insertedDoc)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to upload document: ${localDoc.fileName}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 从Supabase下载文档元数据到本地
     */
    private suspend fun downloadDocumentFromSupabase(cloudDoc: SupabaseDocument): Result<DocumentEntity> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Downloading document metadata: ${cloudDoc.fileName}")
            
            // 转换为本地格式（注意：不包含实际文件，只有元数据）
            val localDoc = DocumentEntity(
                id = cloudDoc.id,
                fileName = cloudDoc.fileName,
                fileType = cloudDoc.fileType,
                filePath = "", // 云端同步的文档没有本地文件路径
                fileSize = cloudDoc.fileSize,
                uploadTime = cloudDoc.uploadTime,
                isVectorized = cloudDoc.isVectorized,
                chunkCount = cloudDoc.chunkCount,
                processingStatus = cloudDoc.processingStatus,
                errorMessage = cloudDoc.errorMessage,
                extractedText = cloudDoc.extractedText,
                lastProcessedTime = cloudDoc.lastProcessedTime
            )
            
            // 插入到本地数据库
            localDocumentDao.insertDocument(localDoc)
            
            Log.d(TAG, "Document metadata downloaded successfully: ${cloudDoc.fileName}")
            Result.success(localDoc)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to download document metadata: ${cloudDoc.fileName}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 上传文档的向量块
     */
    private suspend fun uploadDocumentChunks(documentId: Long, extractedText: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Uploading chunks for document: $documentId")
            
            // 重新分块和向量化
            val chunks = semanticChunker.chunkDocument(extractedText, documentId)
            val optimizedChunks = semanticChunker.optimizeChunks(chunks)
            
            val supabaseChunks = mutableListOf<SupabaseDocumentChunk>()
            
            // 为每个块生成向量
            optimizedChunks.forEach { chunk ->
                val embeddingResult = embeddingService.generateEmbedding(chunk.content).getOrThrow()
                
                val supabaseChunk = SupabaseDocumentChunk(
                    documentId = documentId,
                    chunkIndex = chunk.chunkIndex,
                    content = chunk.content,
                    embedding = embeddingResult.toList(),
                    metadata = mapOf(
                        "startIndex" to chunk.startIndex.toString(),
                        "endIndex" to chunk.endIndex.toString(),
                        "length" to chunk.content.length.toString()
                    )
                )
                
                supabaseChunks.add(supabaseChunk)
            }
            
            // 批量插入块
            val result = vectorService.insertDocumentChunks(supabaseChunks).getOrThrow()
            
            Log.d(TAG, "Uploaded ${result.successCount} chunks for document: $documentId")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to upload chunks for document: $documentId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 检查网络连接状态
     */
    suspend fun checkOnlineStatus(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            if (!SupabaseConfig.isInitialized) {
                return@withContext Result.success(false)
            }
            
            val result = SupabaseConfig.testConnection().getOrThrow()
            Result.success(result)
            
        } catch (e: Exception) {
            Log.w(TAG, "Network check failed", e)
            Result.success(false)
        }
    }
    
    /**
     * 获取同步状态
     */
    suspend fun getSyncStatus(): Result<SyncStatus> = withContext(Dispatchers.IO) {
        try {
            val isOnline = checkOnlineStatus().getOrDefault(false)
            
            // 计算待同步的项目
            val localDocs = localDocumentDao.getVectorizedDocuments()
            val pendingUploads = if (isOnline) {
                try {
                    val cloudDocs = vectorService.getDocuments(1000).getOrDefault(emptyList())
                    localDocs.count { localDoc ->
                        cloudDocs.none { it.fileName == localDoc.fileName && it.fileSize == localDoc.fileSize }
                    }
                } catch (e: Exception) {
                    localDocs.size
                }
            } else {
                localDocs.size
            }
            
            val syncStatus = SyncStatus(
                isOnline = isOnline,
                lastSyncTime = 0, // TODO: 从本地存储获取
                pendingUploads = pendingUploads,
                pendingDownloads = 0, // TODO: 计算待下载项目
                syncInProgress = false
            )
            
            Result.success(syncStatus)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get sync status", e)
            Result.failure(e)
        }
    }
} 