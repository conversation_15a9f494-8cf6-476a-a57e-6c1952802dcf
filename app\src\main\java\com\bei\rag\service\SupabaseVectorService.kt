package com.bei.rag.service

import android.util.Log
import com.bei.rag.model.*
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.postgrest.query.Order
import io.github.jan.supabase.postgrest.query.Count
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * Supabase向量数据操作服务
 */
class SupabaseVectorService {
    
    companion object {
        private const val TAG = "SupabaseVectorService"
    }
    
    /**
     * 计算两个向量的余弦相似度
     */
    private fun calculateCosineSimilarity(vector1: List<Float>, vector2: List<Float>): Float {
        if (vector1.size != vector2.size) {
            throw IllegalArgumentException("向量维度不匹配: ${vector1.size} vs ${vector2.size}")
        }
        
        var dotProduct = 0.0f
        var norm1 = 0.0f
        var norm2 = 0.0f
        
        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            norm1 += vector1[i] * vector1[i]
            norm2 += vector2[i] * vector2[i]
        }
        
        val denominator = kotlin.math.sqrt(norm1) * kotlin.math.sqrt(norm2)
        return if (denominator == 0.0f) 0.0f else dotProduct / denominator
    }
    
    private val client get() = SupabaseConfig.client
    
    /**
     * 插入文档记录
     */
    suspend fun insertDocument(document: SupabaseDocument): Result<SupabaseDocument> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Inserting document: ${document.fileName}")
            
            val result = client.from("documents")
                .insert(document) {
                    select()
                }
                .decodeSingle<SupabaseDocument>()
            
            Log.d(TAG, "Document inserted successfully with ID: ${result.id}")
            Result.success(result)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to insert document: ${document.fileName}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 更新文档状态
     */
    suspend fun updateDocumentStatus(
        documentId: Long,
        isVectorized: Boolean,
        chunkCount: Int,
        status: String,
        errorMessage: String = ""
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Updating document status: $documentId -> $status")
            
            client.from("documents")
                .update(
                    mapOf(
                        "is_vectorized" to isVectorized,
                        "chunk_count" to chunkCount,
                        "processing_status" to status,
                        "error_message" to errorMessage,
                        "last_processed_time" to System.currentTimeMillis()
                    )
                ) {
                    filter {
                        eq("id", documentId)
                    }
                }
            
            Log.d(TAG, "Document status updated successfully")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update document status: $documentId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 批量插入文档块
     */
    suspend fun insertDocumentChunks(chunks: List<SupabaseDocumentChunk>): Result<BatchOperationResult<SupabaseDocumentChunk>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Inserting ${chunks.size} document chunks")
            
            val successful = mutableListOf<SupabaseDocumentChunk>()
            val failed = mutableListOf<Pair<SupabaseDocumentChunk, String>>()
            
            // 分批处理，每批50个块
            val batchSize = 50
            chunks.chunked(batchSize).forEach { batch ->
                try {
                    val results = client.from("document_chunks")
                        .insert(batch) {
                            select()
                        }
                        .decodeList<SupabaseDocumentChunk>()
                    
                    successful.addAll(results)
                    
                } catch (e: Exception) {
                    Log.w(TAG, "Batch insert failed, trying individual inserts", e)
                    
                    // 如果批量插入失败，尝试逐个插入
                    batch.forEach { chunk ->
                        try {
                            val result = client.from("document_chunks")
                                .insert(chunk) {
                                    select()
                                }
                                .decodeSingle<SupabaseDocumentChunk>()
                            
                            successful.add(result)
                            
                        } catch (individualError: Exception) {
                            failed.add(chunk to individualError.message.orEmpty())
                        }
                    }
                }
            }
            
            Log.d(TAG, "Chunks insertion completed: ${successful.size} successful, ${failed.size} failed")
            
            Result.success(
                BatchOperationResult(
                    successful = successful,
                    failed = failed,
                    totalProcessed = chunks.size
                )
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to insert document chunks", e)
            Result.failure(e)
        }
    }
    
    /**
     * 向量相似度搜索
     */
    suspend fun searchSimilarChunks(
        queryEmbedding: List<Float>,
        similarityThreshold: Double = 0.7,
        maxResults: Int = 5,
        documentIds: List<Long>? = null
    ): Result<List<VectorSearchResult>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching similar chunks with threshold: $similarityThreshold, maxResults: $maxResults")
            
            // 获取所有文档块进行客户端相似度计算
            // 注意：我们需要使用原始查询，然后在客户端处理embedding字符串
            val allChunks = client.from("document_chunks")
                .select {
                    if (documentIds != null) {
                        filter {
                            isIn("document_id", documentIds)
                        }
                    }
                }
                .decodeList<Map<String, Any>>()
            
            Log.d(TAG, "Retrieved ${allChunks.size} chunks for similarity calculation")
            
            // 计算相似度并筛选结果
            val candidateResults = mutableListOf<VectorSearchResult>()
            
            for (chunk in allChunks) {
                try {
                    val chunkId = (chunk["id"] as? Number)?.toLong() ?: continue
                    val documentId = (chunk["document_id"] as? Number)?.toLong() ?: continue
                    val content = chunk["content"] as? String ?: continue
                    val embeddingData = chunk["embedding"] ?: continue
                    val metadata = (chunk["metadata"] as? Map<String, String>) ?: emptyMap()
                    
                    // 获取文档名称 - 由于没有join，我们需要单独查询
                    val documentName = try {
                        val doc = client.from("documents")
                            .select {
                                filter {
                                    eq("id", documentId)
                                }
                                limit(1)
                            }
                            .decodeSingleOrNull<Map<String, Any>>()
                        doc?.get("file_name") as? String ?: "Unknown"
                    } catch (e: Exception) {
                        "Unknown"
                    }
                    
                    // 转换embedding为Float列表
                    // 处理两种情况：字符串格式 "[1.0,2.0,3.0]" 或数组格式
                    val embedding = when (embeddingData) {
                        is String -> {
                            // 解析字符串格式的向量 "[1.0,2.0,3.0]"
                            try {
                                embeddingData
                                    .trim('[', ']')
                                    .split(',')
                                    .mapNotNull { it.trim().toFloatOrNull() }
                            } catch (e: Exception) {
                                Log.w(TAG, "Failed to parse embedding string for chunk $chunkId: ${e.message}")
                                emptyList()
                            }
                        }
                        is List<*> -> {
                            // 处理数组格式
                            embeddingData.mapNotNull { 
                                when (it) {
                                    is Number -> it.toFloat()
                                    is String -> it.toFloatOrNull()
                                    else -> null
                                }
                            }
                        }
                        else -> {
                            Log.w(TAG, "Unknown embedding format for chunk $chunkId: ${embeddingData::class.simpleName}")
                            emptyList()
                        }
                    }
                    
                    if (embedding.size != queryEmbedding.size) {
                        Log.w(TAG, "Embedding dimension mismatch for chunk $chunkId: ${embedding.size} vs ${queryEmbedding.size}")
                        continue
                    }
                    
                    // 计算相似度
                    val similarity = calculateCosineSimilarity(queryEmbedding, embedding)
                    
                    // 检查是否超过阈值
                    if (similarity >= similarityThreshold) {
                        candidateResults.add(
                            VectorSearchResult(
                                chunkId = chunkId,
                                documentId = documentId,
                                content = content,
                                similarity = similarity.toDouble(),
                                metadata = metadata,
                                documentName = documentName
                            )
                        )
                    }
                    
                } catch (e: Exception) {
                    Log.w(TAG, "Error processing chunk: ${e.message}")
                    continue
                }
            }
            
            // 按相似度排序并限制结果数量
            val searchResults = candidateResults
                .sortedByDescending { it.similarity }
                .take(maxResults)
            
            Log.d(TAG, "Found ${searchResults.size} similar chunks")
            Result.success(searchResults)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to search similar chunks", e)
            Result.failure(e)
        }
    }
    
    /**
     * TOP-K向量相似度搜索（ANN检索）
     * 返回最相似的K个结果，不使用相似度阈值过滤
     */
    suspend fun searchTopKSimilarChunks(
        queryEmbedding: List<Float>,
        maxResults: Int = 5,
        documentIds: List<Long>? = null
    ): Result<List<VectorSearchResult>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching TOP-K similar chunks, maxResults: $maxResults")
            
            // 获取所有文档块进行客户端相似度计算
            val allChunks = client.from("document_chunks")
                .select {
                    if (documentIds != null) {
                        filter {
                            isIn("document_id", documentIds)
                        }
                    }
                }
                .decodeList<Map<String, Any>>()
            
            Log.d(TAG, "Retrieved ${allChunks.size} chunks for TOP-K similarity calculation")
            
            // 计算所有块的相似度，不进行阈值过滤
            val allResults = mutableListOf<VectorSearchResult>()
            
            for (chunk in allChunks) {
                try {
                    val chunkId = (chunk["id"] as? Number)?.toLong() ?: continue
                    val documentId = (chunk["document_id"] as? Number)?.toLong() ?: continue
                    val content = chunk["content"] as? String ?: continue
                    val embeddingData = chunk["embedding"] ?: continue
                    val metadata = (chunk["metadata"] as? Map<String, String>) ?: emptyMap()
                    
                    // 获取文档名称
                    val documentName = try {
                        val doc = client.from("documents")
                            .select {
                                filter {
                                    eq("id", documentId)
                                }
                                limit(1)
                            }
                            .decodeSingleOrNull<Map<String, Any>>()
                        doc?.get("file_name") as? String ?: "Unknown"
                    } catch (e: Exception) {
                        "Unknown"
                    }
                    
                    // 转换embedding为Float列表
                    val embedding = when (embeddingData) {
                        is String -> {
                            try {
                                embeddingData
                                    .trim('[', ']')
                                    .split(',')
                                    .mapNotNull { it.trim().toFloatOrNull() }
                            } catch (e: Exception) {
                                Log.w(TAG, "Failed to parse embedding string for chunk $chunkId: ${e.message}")
                                emptyList()
                            }
                        }
                        is List<*> -> {
                            embeddingData.mapNotNull { 
                                when (it) {
                                    is Number -> it.toFloat()
                                    is String -> it.toFloatOrNull()
                                    else -> null
                                }
                            }
                        }
                        else -> {
                            Log.w(TAG, "Unknown embedding format for chunk $chunkId: ${embeddingData::class.simpleName}")
                            emptyList()
                        }
                    }
                    
                    if (embedding.size != queryEmbedding.size) {
                        Log.w(TAG, "Embedding dimension mismatch for chunk $chunkId: ${embedding.size} vs ${queryEmbedding.size}")
                        continue
                    }
                    
                    // 计算相似度（不使用阈值过滤）
                    val similarity = calculateCosineSimilarity(queryEmbedding, embedding)
                    
                    allResults.add(
                        VectorSearchResult(
                            chunkId = chunkId,
                            documentId = documentId,
                            content = content,
                            similarity = similarity.toDouble(),
                            metadata = metadata,
                            documentName = documentName
                        )
                    )
                    
                } catch (e: Exception) {
                    Log.w(TAG, "Error processing chunk: ${e.message}")
                    continue
                }
            }
            
            // 按相似度排序并获取TOP-K结果
            val topKResults = allResults
                .sortedByDescending { it.similarity }
                .take(maxResults)
            
            Log.d(TAG, "Found TOP-K ${topKResults.size} chunks, best similarity: ${topKResults.firstOrNull()?.similarity ?: 0.0}")
            Result.success(topKResults)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to search TOP-K similar chunks", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取文档列表
     */
    suspend fun getDocuments(limit: Int = 100): Result<List<SupabaseDocument>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Fetching documents with limit: $limit")
            
            val documents = client.from("documents")
                .select() {
                    order("created_at", Order.DESCENDING)
                    limit(limit.toLong())
                }
                .decodeList<SupabaseDocument>()
            
            Log.d(TAG, "Fetched ${documents.size} documents")
            Result.success(documents)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to fetch documents", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取文档的块
     */
    suspend fun getDocumentChunks(documentId: Long): Result<List<SupabaseDocumentChunk>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Fetching chunks for document: $documentId")
            
            val chunks = client.from("document_chunks")
                .select() {
                    filter {
                        eq("document_id", documentId)
                    }
                    order("chunk_index", Order.ASCENDING)
                }
                .decodeList<SupabaseDocumentChunk>()
            
            Log.d(TAG, "Fetched ${chunks.size} chunks for document: $documentId")
            Result.success(chunks)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to fetch chunks for document: $documentId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 删除文档及其所有块
     */
    suspend fun deleteDocument(documentId: Long): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting document: $documentId")
            
            // 由于设置了ON DELETE CASCADE，删除文档会自动删除相关的chunks
            client.from("documents")
                .delete() {
                    filter {
                        eq("id", documentId)
                    }
                }
            
            Log.d(TAG, "Document deleted successfully: $documentId")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete document: $documentId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取统计信息
     */
    suspend fun getStatistics(): Result<Map<String, Any>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Fetching statistics")
            
            // 获取文档总数
            val totalDocsResult = client.from("documents")
                .select() {
                    count(Count.EXACT)
                }
            val totalDocs = totalDocsResult.countOrNull() ?: 0
            
            // 获取已向量化文档数
            val vectorizedDocsResult = client.from("documents")
                .select() {
                    count(Count.EXACT)
                    filter {
                        eq("is_vectorized", true)
                    }
                }
            val vectorizedDocs = vectorizedDocsResult.countOrNull() ?: 0
            
            // 获取总块数
            val totalChunksResult = client.from("document_chunks")
                .select() {
                    count(Count.EXACT)
                }
            val totalChunks = totalChunksResult.countOrNull() ?: 0
            
            val stats = mapOf(
                "totalDocuments" to totalDocs,
                "vectorizedDocuments" to vectorizedDocs,
                "totalChunks" to totalChunks,
                "vectorizationRate" to if (totalDocs > 0) (vectorizedDocs.toDouble() / totalDocs * 100) else 0.0
            )
            
            Log.d(TAG, "Statistics: $stats")
            Result.success(stats)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to fetch statistics", e)
            Result.failure(e)
        }
    }
} 