package com.bei.rag.service

import android.content.Context
import android.util.Log
import com.bei.rag.service.tts.AndroidTtsEngine
import com.bei.rag.service.tts.GeminiTtsEngine
import com.bei.rag.service.tts.TtsEngine
import com.bei.rag.utils.TtsConfig
import com.bei.rag.utils.TtsEngineType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 文字转语音服务类
 * 负责管理不同的TTS引擎并提供统一的接口
 */
class TextToSpeechService(private val context: Context) {

    private val ttsConfig = TtsConfig(context)
    private var currentEngine: TtsEngine? = null
    private var isInitialized = false

    companion object {
        private const val TAG = "TextToSpeechService"
    }

    /**
     * 初始化TTS服务
     */
    suspend fun initialize(): Result<Unit> {
        return try {
            val engineType = ttsConfig.getEngineType()
            val engine = createEngine(engineType)
            
            val initResult = engine.initialize()
            if (initResult.isSuccess) {
                currentEngine?.cleanup()
                currentEngine = engine
                
                // 应用配置设置
                applyEngineSettings(engine)
                
                isInitialized = true
                Log.d(TAG, "TTS服务初始化成功，使用引擎: ${engineType.displayName}")
                Result.success(Unit)
            } else {
                Log.e(TAG, "TTS引擎初始化失败: ${initResult.exceptionOrNull()?.message}")
                // 如果当前引擎初始化失败，尝试使用默认引擎
                if (engineType != TtsEngineType.getDefault()) {
                    Log.d(TAG, "尝试使用默认引擎")
                    initializeWithEngine(TtsEngineType.getDefault())
                } else {
                    initResult
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "TTS服务初始化异常", e)
            Result.failure(e)
        }
    }

    /**
     * 使用指定引擎初始化
     */
    private suspend fun initializeWithEngine(engineType: TtsEngineType): Result<Unit> {
        return try {
            val engine = createEngine(engineType)
            val initResult = engine.initialize()
            
            if (initResult.isSuccess) {
                currentEngine?.cleanup()
                currentEngine = engine
                ttsConfig.setEngineType(engineType)
                applyEngineSettings(engine)
                isInitialized = true
                Log.d(TAG, "使用备用引擎初始化成功: ${engineType.displayName}")
            }
            
            initResult
        } catch (e: Exception) {
            Log.e(TAG, "备用引擎初始化异常", e)
            Result.failure(e)
        }
    }

    /**
     * 创建TTS引擎实例
     */
    private fun createEngine(engineType: TtsEngineType): TtsEngine {
        return when (engineType) {
            TtsEngineType.ANDROID_TTS -> AndroidTtsEngine(context)
            TtsEngineType.GEMINI_TTS -> GeminiTtsEngine(context)
        }
    }

    /**
     * 应用引擎设置
     */
    private suspend fun applyEngineSettings(engine: TtsEngine) {
        try {
            engine.setSpeechRate(ttsConfig.getSpeechRate())
            engine.setPitch(ttsConfig.getPitch())
            engine.setLanguage(ttsConfig.getLanguage())
        } catch (e: Exception) {
            Log.w(TAG, "应用引擎设置时出错", e)
        }
    }

    /**
     * 将文字转换为语音并播放
     */
    suspend fun speakText(text: String, onCompleted: (() -> Unit)? = null): Result<Unit> {
        if (!isInitialized) {
            val initResult = initialize()
            if (initResult.isFailure) {
                return initResult
            }
        }

        val engine = currentEngine
        if (engine == null || !engine.isAvailable()) {
            return Result.failure(Exception("TTS引擎不可用"))
        }

        return try {
            engine.speakText(
                text = text,
                onComplete = onCompleted,
                onError = { error ->
                    Log.e(TAG, "TTS播放错误: $error")
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "调用TTS引擎时出错", e)
            Result.failure(e)
        }
    }

    /**
     * 切换TTS引擎
     */
    suspend fun switchEngine(engineType: TtsEngineType): Result<Unit> {
        return try {
            Log.d(TAG, "切换TTS引擎到: ${engineType.displayName}")
            
            // 停止当前播放
            stopSpeaking()
            
            // 保存新的引擎类型
            ttsConfig.setEngineType(engineType)
            
            // 重新初始化
            isInitialized = false
            initialize()
        } catch (e: Exception) {
            Log.e(TAG, "切换TTS引擎时出错", e)
            Result.failure(e)
        }
    }

    /**
     * 获取当前引擎类型
     */
    fun getCurrentEngineType(): TtsEngineType {
        return currentEngine?.getEngineType() ?: ttsConfig.getEngineType()
    }

    /**
     * 检查当前引擎是否可用
     */
    fun isEngineAvailable(): Boolean {
        return currentEngine?.isAvailable() == true
    }

    /**
     * 检查TTS服务是否已初始化
     */
    fun isInitialized(): Boolean {
        return isInitialized && currentEngine != null
    }

    /**
     * 停止播放
     */
    fun stopSpeaking() {
        try {
            currentEngine?.stopSpeaking()
            Log.d(TAG, "停止播放")
        } catch (e: Exception) {
            Log.e(TAG, "停止播放时出错", e)
        }
    }

    /**
     * 检查是否正在播放
     */
    fun isSpeaking(): Boolean {
        return currentEngine?.isSpeaking() == true
    }

    /**
     * 设置语速
     */
    fun setSpeechRate(rate: Float) {
        try {
            ttsConfig.setSpeechRate(rate)
            currentEngine?.setSpeechRate(rate)
            Log.d(TAG, "设置语速: $rate")
        } catch (e: Exception) {
            Log.e(TAG, "设置语速时出错", e)
        }
    }

    /**
     * 设置音调
     */
    fun setPitch(pitch: Float) {
        try {
            ttsConfig.setPitch(pitch)
            currentEngine?.setPitch(pitch)
            Log.d(TAG, "设置音调: $pitch")
        } catch (e: Exception) {
            Log.e(TAG, "设置音调时出错", e)
        }
    }

    /**
     * 设置语言
     */
    suspend fun setLanguage(language: String): Result<Unit> {
        return try {
            ttsConfig.setLanguage(language)
            currentEngine?.setLanguage(language) ?: Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "设置语言时出错", e)
            Result.failure(e)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            currentEngine?.cleanup()
            currentEngine = null
            isInitialized = false
            Log.d(TAG, "TTS服务资源已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理TTS服务资源时出错", e)
        }
    }

    // 为了保持向后兼容性，保留原有的方法名
    fun stopPlaying() = stopSpeaking()
    fun isPlaying() = isSpeaking()
}
