package com.bei.rag.service

import android.content.Context
import android.util.Log
import com.bei.rag.database.dao.DocumentDao
import com.bei.rag.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 向量搜索服务
 * 提供本地和云端的向量搜索功能
 */
class VectorSearchService(
    private val context: Context,
    private val localDocumentDao: DocumentDao,
    private val vectorService: SupabaseVectorService,
    private val embeddingService: SiliconFlowEmbeddingService
) {
    
    companion object {
        private const val TAG = "VectorSearchService"
        private const val DEFAULT_SIMILARITY_THRESHOLD = 0.3
        private const val DEFAULT_MAX_RESULTS = 5
    }
    
    /**
     * 执行向量搜索
     * 优先使用云端搜索，如果失败则降级到本地搜索
     */
    suspend fun search(request: RagQueryRequest): Result<RagQueryResponse> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting vector search for query: ${request.query}")
            
            // 根据参数选择搜索策略
            return@withContext if (request.useTopKSearch) {
                Log.d(TAG, "Using TOP-K search mode")
                searchTopK(request)
            } else {
                Log.d(TAG, "Using threshold-based search mode")
                performThresholdSearch(request)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Search failed for query: ${request.query}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 执行基于阈值的搜索（原有逻辑）
     */
    private suspend fun performThresholdSearch(request: RagQueryRequest): Result<RagQueryResponse> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting threshold-based vector search for query: ${request.query}")
            
            // 生成查询向量
            val queryEmbedding = embeddingService.generateEmbedding(request.query).getOrThrow()
            
            // 尝试云端搜索
            val searchResults = try {
                if (SupabaseConfig.isInitialized) {
                    searchInSupabase(queryEmbedding.toList(), request).getOrThrow()
                } else {
                    searchLocally(queryEmbedding.toList(), request).getOrThrow()
                }
            } catch (cloudError: Exception) {
                Log.w(TAG, "Cloud search failed, falling back to local search", cloudError)
                searchLocally(queryEmbedding.toList(), request).getOrThrow()
            }
            
            // 构建上下文
            val context = buildContext(searchResults)
            
            val response = RagQueryResponse(
                query = request.query,
                results = searchResults,
                context = context,
                totalResults = searchResults.size
            )
            
            Log.d(TAG, "Threshold search completed: found ${searchResults.size} results")
            Result.success(response)
            
        } catch (e: Exception) {
            Log.e(TAG, "Threshold search failed for query: ${request.query}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 执行TOP-K向量搜索（ANN检索）
     * 保证返回最相似的K个结果，不使用相似度阈值过滤
     */
    suspend fun searchTopK(request: RagQueryRequest): Result<RagQueryResponse> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting TOP-K vector search for query: ${request.query}")
            
            // 生成查询向量
            val queryEmbedding = embeddingService.generateEmbedding(request.query).getOrThrow()
            
            // 尝试云端TOP-K搜索
            val searchResults = try {
                if (SupabaseConfig.isInitialized) {
                    searchTopKInSupabase(queryEmbedding.toList(), request).getOrThrow()
                } else {
                    searchTopKLocally(queryEmbedding.toList(), request).getOrThrow()
                }
            } catch (cloudError: Exception) {
                Log.w(TAG, "Cloud TOP-K search failed, falling back to local TOP-K search", cloudError)
                searchTopKLocally(queryEmbedding.toList(), request).getOrThrow()
            }
            
            // 构建上下文
            val context = buildContext(searchResults)
            
            val response = RagQueryResponse(
                query = request.query,
                results = searchResults,
                context = context,
                totalResults = searchResults.size
            )
            
            Log.d(TAG, "TOP-K search completed: found ${searchResults.size} results")
            Result.success(response)
            
        } catch (e: Exception) {
            Log.e(TAG, "TOP-K search failed for query: ${request.query}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 在Supabase中搜索
     */
    private suspend fun searchInSupabase(
        queryEmbedding: List<Float>,
        request: RagQueryRequest
    ): Result<List<VectorSearchResult>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching in Supabase")
            
            val results = vectorService.searchSimilarChunks(
                queryEmbedding = queryEmbedding,
                similarityThreshold = request.similarityThreshold,
                maxResults = request.maxResults,
                documentIds = request.documentIds
            ).getOrThrow()
            
            Log.d(TAG, "Supabase search returned ${results.size} results")
            Result.success(results)
            
        } catch (e: Exception) {
            Log.e(TAG, "Supabase search failed", e)
            Result.failure(e)
        }
    }
    
    /**
     * 在Supabase中进行TOP-K搜索
     */
    private suspend fun searchTopKInSupabase(
        queryEmbedding: List<Float>,
        request: RagQueryRequest
    ): Result<List<VectorSearchResult>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "TOP-K searching in Supabase")
            
            val results = vectorService.searchTopKSimilarChunks(
                queryEmbedding = queryEmbedding,
                maxResults = request.maxResults,
                documentIds = request.documentIds
            ).getOrThrow()
            
            Log.d(TAG, "Supabase TOP-K search returned ${results.size} results")
            Result.success(results)
            
        } catch (e: Exception) {
            Log.e(TAG, "Supabase TOP-K search failed", e)
            Result.failure(e)
        }
    }
    
    /**
     * 本地搜索（简化版，使用文本相似度）
     */
    private suspend fun searchLocally(
        queryEmbedding: List<Float>,
        request: RagQueryRequest
    ): Result<List<VectorSearchResult>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching locally")
            
            // 获取本地已向量化的文档
            val localDocs = if (request.documentIds != null) {
                request.documentIds.mapNotNull { id ->
                    localDocumentDao.getDocumentByIdSync(id)
                }.filter { it.isVectorized }
            } else {
                localDocumentDao.getVectorizedDocuments()
            }
            
            val results = mutableListOf<VectorSearchResult>()
            val queryText = request.query.lowercase()
            
            localDocs.forEach { doc ->
                if (doc.extractedText.isNotEmpty()) {
                    // 简单的文本匹配（这里可以改进为更复杂的相似度计算）
                    val content = doc.extractedText.lowercase()
                    val similarity = calculateTextSimilarity(queryText, content)
                    
                    if (similarity >= request.similarityThreshold) {
                        // 提取相关片段
                        val relevantChunk = extractRelevantChunk(doc.extractedText, queryText)
                        
                        results.add(
                            VectorSearchResult(
                                chunkId = doc.id, // 使用文档ID作为块ID
                                documentId = doc.id,
                                content = relevantChunk,
                                similarity = similarity,
                                metadata = mapOf(
                                    "source" to "local",
                                    "fileType" to doc.fileType
                                ),
                                documentName = doc.fileName
                            )
                        )
                    }
                }
            }
            
            // 按相似度排序并限制结果数量
            val sortedResults = results
                .sortedByDescending { it.similarity }
                .take(request.maxResults)
            
            Log.d(TAG, "Local search returned ${sortedResults.size} results")
            Result.success(sortedResults)
            
        } catch (e: Exception) {
            Log.e(TAG, "Local search failed", e)
            Result.failure(e)
        }
    }
    
    /**
     * 本地TOP-K搜索（不使用相似度阈值过滤）
     */
    private suspend fun searchTopKLocally(
        queryEmbedding: List<Float>,
        request: RagQueryRequest
    ): Result<List<VectorSearchResult>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "TOP-K searching locally")
            
            // 获取本地已向量化的文档
            val localDocs = if (request.documentIds != null) {
                request.documentIds.mapNotNull { id ->
                    localDocumentDao.getDocumentByIdSync(id)
                }.filter { it.isVectorized }
            } else {
                localDocumentDao.getVectorizedDocuments()
            }
            
            val allResults = mutableListOf<VectorSearchResult>()
            val queryText = request.query.lowercase()
            
            localDocs.forEach { doc ->
                if (doc.extractedText.isNotEmpty()) {
                    // 计算文本相似度，不进行阈值过滤
                    val content = doc.extractedText.lowercase()
                    val similarity = calculateTextSimilarity(queryText, content)
                    
                    // 提取相关片段
                    val relevantChunk = extractRelevantChunk(doc.extractedText, queryText)
                    
                    allResults.add(
                        VectorSearchResult(
                            chunkId = doc.id, // 使用文档ID作为块ID
                            documentId = doc.id,
                            content = relevantChunk,
                            similarity = similarity,
                            metadata = mapOf(
                                "source" to "local_topk",
                                "fileType" to doc.fileType
                            ),
                            documentName = doc.fileName
                        )
                    )
                }
            }
            
            // 按相似度排序并获取TOP-K结果
            val topKResults = allResults
                .sortedByDescending { it.similarity }
                .take(request.maxResults)
            
            Log.d(TAG, "Local TOP-K search returned ${topKResults.size} results, best similarity: ${topKResults.firstOrNull()?.similarity ?: 0.0}")
            Result.success(topKResults)
            
        } catch (e: Exception) {
            Log.e(TAG, "Local TOP-K search failed", e)
            Result.failure(e)
        }
    }
    
    /**
     * 计算文本相似度（简化版）
     */
    private fun calculateTextSimilarity(query: String, content: String): Double {
        val queryWords = query.split("\\s+".toRegex()).filter { it.isNotEmpty() }
        val contentWords = content.split("\\s+".toRegex()).filter { it.isNotEmpty() }.toSet()
        
        if (queryWords.isEmpty()) return 0.0
        
        val matchCount = queryWords.count { word ->
            contentWords.any { it.contains(word, ignoreCase = true) }
        }
        
        return matchCount.toDouble() / queryWords.size
    }
    
    /**
     * 提取相关文本片段
     */
    private fun extractRelevantChunk(text: String, query: String, maxLength: Int = 500): String {
        val queryWords = query.split("\\s+".toRegex()).filter { it.isNotEmpty() }
        val sentences = text.split("[.!?]".toRegex()).filter { it.trim().isNotEmpty() }
        
        // 找到包含查询词的句子
        val relevantSentences = sentences.filter { sentence ->
            queryWords.any { word ->
                sentence.contains(word, ignoreCase = true)
            }
        }
        
        if (relevantSentences.isEmpty()) {
            // 如果没有找到相关句子，返回文本开头
            return text.take(maxLength)
        }
        
        // 组合相关句子，直到达到最大长度
        val result = StringBuilder()
        for (sentence in relevantSentences) {
            val trimmedSentence = sentence.trim()
            if (result.length + trimmedSentence.length + 1 <= maxLength) {
                if (result.isNotEmpty()) result.append(" ")
                result.append(trimmedSentence)
            } else {
                break
            }
        }
        
        return result.toString().ifEmpty { text.take(maxLength) }
    }
    
    /**
     * 构建上下文字符串
     */
    private fun buildContext(results: List<VectorSearchResult>): String {
        if (results.isEmpty()) return ""
        
        return results.mapIndexed { index, result ->
            val source = result.documentName ?: "未知文档"
            "【来源${index + 1}: $source】\n${result.content}"
        }.joinToString("\n\n")
    }
    
    /**
     * 获取搜索统计信息
     */
    suspend fun getSearchStatistics(): Result<Map<String, Any>> = withContext(Dispatchers.IO) {
        try {
            val localStats = mutableMapOf<String, Any>()
            
            // 本地统计
            val localDocs = localDocumentDao.getVectorizedDocuments()
            localStats["localVectorizedDocuments"] = localDocs.size
            localStats["localTotalText"] = localDocs.sumOf { it.extractedText.length }
            
            // 云端统计
            val cloudStats = if (SupabaseConfig.isInitialized) {
                try {
                    vectorService.getStatistics().getOrDefault(emptyMap())
                } catch (e: Exception) {
                    emptyMap()
                }
            } else {
                emptyMap()
            }
            
            val combinedStats = mutableMapOf<String, Any>()
            combinedStats.putAll(localStats)
            combinedStats.putAll(cloudStats)
            combinedStats["searchMode"] = if (SupabaseConfig.isInitialized) "hybrid" else "local"
            
            Result.success(combinedStats)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get search statistics", e)
            Result.failure(e)
        }
    }
    
    /**
     * 测试搜索功能
     */
    suspend fun testSearch(): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Testing search functionality")
            
            val testQuery = "测试"
            val request = RagQueryRequest(
                query = testQuery,
                maxResults = 3,
                similarityThreshold = 0.1 // 降低阈值以便测试
            )
            
            val response = search(request).getOrThrow()
            
            val resultMessage = """
                搜索测试完成：
                - 查询词：${response.query}
                - 找到结果：${response.totalResults} 个
                - 上下文长度：${response.context.length} 字符
                - 搜索模式：${if (SupabaseConfig.isInitialized) "云端+本地" else "仅本地"}
            """.trimIndent()
            
            Log.d(TAG, "Search test completed successfully")
            Result.success(resultMessage)
            
        } catch (e: Exception) {
            Log.e(TAG, "Search test failed", e)
            Result.failure(e)
        }
    }
} 