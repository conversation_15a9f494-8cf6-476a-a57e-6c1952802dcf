package com.bei.rag.service

import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import com.bei.rag.BuildConfig
import com.bei.rag.model.VoiceToTextResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * 语音转文字服务类
 * 负责录音、转换音频格式、调用API等功能
 */
class VoiceToTextService(private val context: Context) {

    private var audioRecord: AudioRecord? = null
    private var isRecording = false
    private lateinit var pcmFile: File
    private lateinit var wavFile: File

    // 音频录制参数
    private val sampleRate = 16000
    private val channelConfig = AudioFormat.CHANNEL_IN_MONO
    private val audioFormat = AudioFormat.ENCODING_PCM_16BIT
    private val bufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat)

    // API服务
    private val apiService: SiliconFlowApiService by lazy {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
        }

        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()

        Retrofit.Builder()
            .baseUrl("https://api.siliconflow.cn")
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(SiliconFlowApiService::class.java)
    }

    /**
     * 开始录音
     */
    suspend fun startRecording(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // 初始化文件
            val cacheDir = context.cacheDir
            pcmFile = File(cacheDir, "temp_record_${System.currentTimeMillis()}.pcm")
            wavFile = File(cacheDir, "final_record_${System.currentTimeMillis()}.wav")

            // 初始化AudioRecord
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                sampleRate,
                channelConfig,
                audioFormat,
                bufferSize
            )

            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                return@withContext Result.failure(Exception("AudioRecord初始化失败"))
            }

            audioRecord?.startRecording()
            isRecording = true

            // 开始录音数据写入
            val buffer = ByteArray(bufferSize)
            val outputStream = FileOutputStream(pcmFile)

            while (isRecording && audioRecord?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                val read = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                if (read > 0) {
                    outputStream.write(buffer, 0, read)
                }
            }
            outputStream.close()

            Result.success(Unit)
        } catch (e: SecurityException) {
            Result.failure(Exception("录音权限被拒绝"))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 停止录音并转换为文字
     */
    suspend fun stopRecordingAndTranscribe(): Result<String> = withContext(Dispatchers.IO) {
        try {
            // 停止录音
            isRecording = false
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null

            // 转换为WAV格式
            createWavFile()

            // 调用API进行语音识别
            val result = transcribeAudio()

            // 清理临时文件
            cleanupFiles()

            result
        } catch (e: Exception) {
            cleanupFiles()
            Result.failure(e)
        }
    }

    /**
     * 调用SiliconFlow API进行语音识别
     */
    private suspend fun transcribeAudio(): Result<String> {
        return try {
            val modelRequestBody = "FunAudioLLM/SenseVoiceSmall".toRequestBody("text/plain".toMediaTypeOrNull())
            val fileRequestBody = wavFile.asRequestBody("audio/wav".toMediaTypeOrNull())
            val filePart = MultipartBody.Part.createFormData("file", wavFile.name, fileRequestBody)
            val authToken = "Bearer ${BuildConfig.SILICONFLOW_API_KEY}"

            val response = apiService.transcribeAudio(authToken, modelRequestBody, filePart)

            if (response.isSuccessful) {
                val transcriptionResponse = response.body()
                if (transcriptionResponse != null && transcriptionResponse.text.isNotBlank()) {
                    Result.success(transcriptionResponse.text)
                } else {
                    Result.failure(Exception("识别结果为空"))
                }
            } else {
                val errorBody = response.errorBody()?.string() ?: "未知错误"
                Result.failure(Exception("API调用失败: ${response.code()} - $errorBody"))
            }
        } catch (e: Exception) {
            Result.failure(Exception("网络请求异常: ${e.message}"))
        }
    }

    /**
     * 将PCM文件转换为WAV格式
     */
    @Throws(IOException::class)
    private fun createWavFile() {
        val pcmInputStream = FileInputStream(pcmFile)
        val wavOutputStream = FileOutputStream(wavFile)
        val pcmDataSize = pcmFile.length()
        val totalDataLen = pcmDataSize + 36
        val byteRate = (sampleRate * 16 * 1 / 8).toLong()

        val header = ByteArray(44)
        // WAV文件头
        header[0] = 'R'.code.toByte(); header[1] = 'I'.code.toByte(); header[2] = 'F'.code.toByte(); header[3] = 'F'.code.toByte()
        header[4] = (totalDataLen and 0xff).toByte(); header[5] = (totalDataLen shr 8 and 0xff).toByte()
        header[6] = (totalDataLen shr 16 and 0xff).toByte(); header[7] = (totalDataLen shr 24 and 0xff).toByte()
        header[8] = 'W'.code.toByte(); header[9] = 'A'.code.toByte(); header[10] = 'V'.code.toByte(); header[11] = 'E'.code.toByte()
        header[12] = 'f'.code.toByte(); header[13] = 'm'.code.toByte(); header[14] = 't'.code.toByte(); header[15] = ' '.code.toByte()
        header[16] = 16; header[17] = 0; header[18] = 0; header[19] = 0
        header[20] = 1; header[21] = 0
        header[22] = 1; header[23] = 0
        header[24] = (sampleRate and 0xff).toByte(); header[25] = (sampleRate shr 8 and 0xff).toByte()
        header[26] = (sampleRate shr 16 and 0xff).toByte(); header[27] = (sampleRate shr 24 and 0xff).toByte()
        header[28] = (byteRate and 0xff).toByte(); header[29] = (byteRate shr 8 and 0xff).toByte()
        header[30] = (byteRate shr 16 and 0xff).toByte(); header[31] = (byteRate shr 24 and 0xff).toByte()
        header[32] = 2; header[33] = 0
        header[34] = 16; header[35] = 0
        header[36] = 'd'.code.toByte(); header[37] = 'a'.code.toByte(); header[38] = 't'.code.toByte(); header[39] = 'a'.code.toByte()
        header[40] = (pcmDataSize and 0xff).toByte(); header[41] = (pcmDataSize shr 8 and 0xff).toByte()
        header[42] = (pcmDataSize shr 16 and 0xff).toByte(); header[43] = (pcmDataSize shr 24 and 0xff).toByte()

        wavOutputStream.write(header, 0, 44)
        pcmInputStream.copyTo(wavOutputStream)
        pcmInputStream.close()
        wavOutputStream.close()
    }

    /**
     * 清理临时文件
     */
    private fun cleanupFiles() {
        try {
            if (::pcmFile.isInitialized && pcmFile.exists()) {
                pcmFile.delete()
            }
            if (::wavFile.isInitialized && wavFile.exists()) {
                wavFile.delete()
            }
        } catch (e: Exception) {
            // 忽略清理错误
        }
    }

    /**
     * 取消录音
     */
    fun cancelRecording() {
        isRecording = false
        audioRecord?.stop()
        audioRecord?.release()
        audioRecord = null
        cleanupFiles()
    }
}
