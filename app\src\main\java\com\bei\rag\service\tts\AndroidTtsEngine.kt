package com.bei.rag.service.tts

import android.content.Context
import android.content.Intent
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import com.bei.rag.utils.TtsEngineType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.util.*
import kotlin.coroutines.resume

/**
 * Android内置TTS引擎实现
 * 基于android.speech.tts.TextToSpeech
 */
class AndroidTtsEngine(private val context: Context) : TtsEngine, TextToSpeech.OnInitListener {
    
    private var tts: TextToSpeech? = null
    private var isInitialized = false
    private var currentUtteranceId: String? = null
    
    // 回调函数
    private var onStartCallback: (() -> Unit)? = null
    private var onCompleteCallback: (() -> Unit)? = null
    private var onErrorCallback: ((String) -> Unit)? = null

    companion object {
        private const val TAG = "AndroidTtsEngine"
    }

    override fun getEngineType(): TtsEngineType = TtsEngineType.ANDROID_TTS

    override suspend fun initialize(): Result<Unit> = withContext(Dispatchers.Main) {
        suspendCancellableCoroutine { continuation ->
            try {
                tts = TextToSpeech(context) { status ->
                    if (status == TextToSpeech.SUCCESS) {
                        isInitialized = true
                        setupTts()
                        Log.d(TAG, "Android TTS初始化成功")
                        continuation.resume(Result.success(Unit))
                    } else {
                        Log.e(TAG, "Android TTS初始化失败，状态码: $status")
                        continuation.resume(Result.failure(Exception("TTS初始化失败，状态码: $status")))
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "创建TTS实例失败", e)
                continuation.resume(Result.failure(e))
            }
        }
    }

    override fun onInit(status: Int) {
        // 此方法由TextToSpeech构造函数中的回调处理
        // 实际初始化逻辑在initialize()方法中处理
    }

    private fun setupTts() {
        tts?.let { ttsInstance ->
            // 设置默认语言为中文
            val result = ttsInstance.setLanguage(Locale.CHINA)
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.w(TAG, "中文语言包不可用，尝试设置为英文")
                ttsInstance.setLanguage(Locale.US)
            }

            // 设置朗读进度监听器
            ttsInstance.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
                override fun onStart(utteranceId: String?) {
                    Log.d(TAG, "开始朗读: $utteranceId")
                    onStartCallback?.invoke()
                }

                override fun onDone(utteranceId: String?) {
                    Log.d(TAG, "朗读完成: $utteranceId")
                    if (utteranceId == currentUtteranceId) {
                        currentUtteranceId = null
                        onCompleteCallback?.invoke()
                    }
                }

                @Deprecated("Deprecated in Java")
                override fun onError(utteranceId: String?) {
                    Log.e(TAG, "朗读错误: $utteranceId")
                    if (utteranceId == currentUtteranceId) {
                        currentUtteranceId = null
                        onErrorCallback?.invoke("朗读出现错误")
                    }
                }

                override fun onError(utteranceId: String?, errorCode: Int) {
                    Log.e(TAG, "朗读错误: $utteranceId, 错误码: $errorCode")
                    if (utteranceId == currentUtteranceId) {
                        currentUtteranceId = null
                        onErrorCallback?.invoke("朗读出现错误，错误码: $errorCode")
                    }
                }
            })
        }
    }

    override suspend fun speakText(
        text: String,
        onStart: (() -> Unit)?,
        onComplete: (() -> Unit)?,
        onError: ((String) -> Unit)?
    ): Result<Unit> = withContext(Dispatchers.Main) {
        if (!isInitialized || tts == null) {
            return@withContext Result.failure(Exception("TTS引擎未初始化"))
        }

        if (text.isBlank()) {
            return@withContext Result.failure(Exception("朗读文本不能为空"))
        }

        try {
            // 停止当前朗读
            stopSpeaking()

            // 设置回调
            onStartCallback = onStart
            onCompleteCallback = onComplete
            onErrorCallback = onError

            // 生成唯一的utteranceId
            currentUtteranceId = "tts_${System.currentTimeMillis()}"

            // 开始朗读
            val result = tts?.speak(
                text,
                TextToSpeech.QUEUE_FLUSH,
                null,
                currentUtteranceId
            )

            if (result == TextToSpeech.SUCCESS) {
                Log.d(TAG, "开始朗读文本: ${text.take(50)}...")
                Result.success(Unit)
            } else {
                currentUtteranceId = null
                Result.failure(Exception("启动朗读失败，错误码: $result"))
            }
        } catch (e: Exception) {
            currentUtteranceId = null
            Log.e(TAG, "朗读文本时出错", e)
            Result.failure(e)
        }
    }

    override fun stopSpeaking() {
        try {
            tts?.stop()
            currentUtteranceId = null
            Log.d(TAG, "停止朗读")
        } catch (e: Exception) {
            Log.e(TAG, "停止朗读时出错", e)
        }
    }

    override fun isSpeaking(): Boolean {
        return tts?.isSpeaking == true
    }

    override fun setSpeechRate(rate: Float) {
        try {
            val result = tts?.setSpeechRate(rate)
            if (result == TextToSpeech.SUCCESS) {
                Log.d(TAG, "设置语速成功: $rate")
            } else {
                Log.w(TAG, "设置语速失败: $rate")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置语速时出错", e)
        }
    }

    override fun setPitch(pitch: Float) {
        try {
            val result = tts?.setPitch(pitch)
            if (result == TextToSpeech.SUCCESS) {
                Log.d(TAG, "设置音调成功: $pitch")
            } else {
                Log.w(TAG, "设置音调失败: $pitch")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置音调时出错", e)
        }
    }

    override suspend fun setLanguage(language: String): Result<Unit> = withContext(Dispatchers.Main) {
        if (!isInitialized || tts == null) {
            return@withContext Result.failure(Exception("TTS引擎未初始化"))
        }

        try {
            val locale = when (language) {
                "zh-CN" -> Locale.CHINA
                "zh-TW" -> Locale.TAIWAN
                "en-US" -> Locale.US
                "en-GB" -> Locale.UK
                else -> Locale.forLanguageTag(language)
            }

            val result = tts?.setLanguage(locale)
            when (result) {
                TextToSpeech.LANG_AVAILABLE,
                TextToSpeech.LANG_COUNTRY_AVAILABLE,
                TextToSpeech.LANG_COUNTRY_VAR_AVAILABLE -> {
                    Log.d(TAG, "设置语言成功: $language")
                    Result.success(Unit)
                }
                TextToSpeech.LANG_MISSING_DATA -> {
                    Log.w(TAG, "语言数据缺失: $language")
                    // 尝试引导用户安装语言数据
                    promptInstallLanguageData()
                    Result.failure(Exception("语言数据缺失，请安装相应的语音包"))
                }
                TextToSpeech.LANG_NOT_SUPPORTED -> {
                    Log.w(TAG, "不支持的语言: $language")
                    Result.failure(Exception("不支持的语言: $language"))
                }
                else -> {
                    Log.w(TAG, "设置语言失败: $language, 结果码: $result")
                    Result.failure(Exception("设置语言失败"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置语言时出错", e)
            Result.failure(e)
        }
    }

    /**
     * 引导用户安装语音数据
     */
    private fun promptInstallLanguageData() {
        try {
            val installIntent = Intent()
            installIntent.action = TextToSpeech.Engine.ACTION_INSTALL_TTS_DATA
            installIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(installIntent)
        } catch (e: Exception) {
            Log.e(TAG, "无法打开TTS数据安装界面", e)
        }
    }

    override fun isAvailable(): Boolean {
        return isInitialized && tts != null
    }

    override fun cleanup() {
        try {
            stopSpeaking()
            tts?.shutdown()
            tts = null
            isInitialized = false
            currentUtteranceId = null
            onStartCallback = null
            onCompleteCallback = null
            onErrorCallback = null
            Log.d(TAG, "TTS引擎资源已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理TTS引擎资源时出错", e)
        }
    }
} 