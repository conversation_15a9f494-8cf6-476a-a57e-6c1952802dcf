package com.bei.rag.service.tts

import android.content.Context
import android.media.MediaPlayer
import android.util.Base64
import android.util.Log
import com.bei.rag.BuildConfig
import com.bei.rag.model.*
import com.bei.rag.service.GeminiTtsApiService
import com.bei.rag.utils.TtsEngineType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.TimeUnit

/**
 * Gemini TTS引擎实现
 * 基于Google Gemini API的在线语音合成
 */
class GeminiTtsEngine(private val context: Context) : TtsEngine {
    
    private var mediaPlayer: MediaPlayer? = null
    private var currentAudioFile: File? = null
    private var speechRate: Float = 1.0f
    private var pitch: Float = 1.0f
    private var language: String = "zh-CN"
    
    // 回调函数
    private var onStartCallback: (() -> Unit)? = null
    private var onCompleteCallback: (() -> Unit)? = null
    private var onErrorCallback: ((String) -> Unit)? = null

    companion object {
        private const val TAG = "GeminiTtsEngine"
    }

    // API服务
    private val apiService: GeminiTtsApiService by lazy {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
        }

        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()

        Retrofit.Builder()
            .baseUrl("https://generativelanguage.googleapis.com/")
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(GeminiTtsApiService::class.java)
    }

    override fun getEngineType(): TtsEngineType = TtsEngineType.GEMINI_TTS

    override suspend fun initialize(): Result<Unit> {
        return try {
            // Gemini TTS不需要特殊初始化，只需检查API密钥
            if (BuildConfig.GEMINI_TTS_API_KEY.isBlank()) {
                Result.failure(Exception("Gemini TTS API密钥未配置"))
            } else {
                Log.d(TAG, "Gemini TTS引擎初始化成功")
                Result.success(Unit)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Gemini TTS引擎初始化失败", e)
            Result.failure(e)
        }
    }

    override suspend fun speakText(
        text: String,
        onStart: (() -> Unit)?,
        onComplete: (() -> Unit)?,
        onError: ((String) -> Unit)?
    ): Result<Unit> = withContext(Dispatchers.IO) {
        if (text.isBlank()) {
            return@withContext Result.failure(Exception("朗读文本不能为空"))
        }

        // 设置回调
        onStartCallback = onStart
        onCompleteCallback = onComplete
        onErrorCallback = onError

        try {
            // 通知开始
            withContext(Dispatchers.Main) {
                onStartCallback?.invoke()
            }

            // 1. 调用Gemini TTS API
            val audioData = fetchTtsAudioData(text)
            
            // 2. 将音频数据保存为临时文件
            val audioFile = saveAudioDataToFile(audioData)
            
            // 3. 播放音频文件
            withContext(Dispatchers.Main) {
                playAudioFile(audioFile)
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "朗读文本时出错", e)
            withContext(Dispatchers.Main) {
                onErrorCallback?.invoke(e.message ?: "朗读失败")
            }
            Result.failure(e)
        }
    }

    /**
     * 调用Gemini TTS API获取音频数据
     */
    private suspend fun fetchTtsAudioData(text: String): ByteArray {
        val request = TtsRequest(
            contents = listOf(
                TtsContent(
                    parts = listOf(TtsPart(text = text))
                )
            ),
            generationConfig = TtsGenerationConfig(
                speechConfig = TtsSpeechConfig(
                    voiceConfig = TtsVoiceConfig(
                        prebuiltVoiceConfig = TtsPrebuiltVoiceConfig(voiceName = "Kore")
                    )
                )
            )
        )

        val response = apiService.generateSpeech(
            apiKey = BuildConfig.GEMINI_TTS_API_KEY,
            request = request
        )

        if (response.isSuccessful) {
            val ttsResponse = response.body()
            val base64Audio = ttsResponse?.candidates?.firstOrNull()?.content?.parts?.firstOrNull()?.inlineData?.data
            
            if (base64Audio != null) {
                return Base64.decode(base64Audio, Base64.DEFAULT)
            } else {
                throw Exception("无法获取音频数据")
            }
        } else {
            val errorBody = response.errorBody()?.string() ?: "未知错误"
            throw Exception("TTS API调用失败: ${response.code()} - $errorBody")
        }
    }

    /**
     * 将音频数据保存为临时文件
     */
    private fun saveAudioDataToFile(audioData: ByteArray): File {
        val cacheDir = context.cacheDir
        val audioFile = File(cacheDir, "gemini_tts_audio_${System.currentTimeMillis()}.wav")
        
        // 创建WAV文件头
        val wavHeader = createWavHeader(audioData.size)
        
        FileOutputStream(audioFile).use { outputStream ->
            outputStream.write(wavHeader)
            outputStream.write(audioData)
        }
        
        return audioFile
    }

    /**
     * 创建WAV文件头
     */
    private fun createWavHeader(dataSize: Int): ByteArray {
        val sampleRate = 24000 // Gemini TTS返回的采样率
        val channels = 1 // 单声道
        val bitsPerSample = 16
        val byteRate = sampleRate * channels * bitsPerSample / 8
        val blockAlign = channels * bitsPerSample / 8
        val totalSize = dataSize + 36

        val header = ByteArray(44)
        
        // RIFF header
        header[0] = 'R'.code.toByte(); header[1] = 'I'.code.toByte(); header[2] = 'F'.code.toByte(); header[3] = 'F'.code.toByte()
        header[4] = (totalSize and 0xff).toByte(); header[5] = (totalSize shr 8 and 0xff).toByte()
        header[6] = (totalSize shr 16 and 0xff).toByte(); header[7] = (totalSize shr 24 and 0xff).toByte()
        header[8] = 'W'.code.toByte(); header[9] = 'A'.code.toByte(); header[10] = 'V'.code.toByte(); header[11] = 'E'.code.toByte()
        
        // fmt chunk
        header[12] = 'f'.code.toByte(); header[13] = 'm'.code.toByte(); header[14] = 't'.code.toByte(); header[15] = ' '.code.toByte()
        header[16] = 16; header[17] = 0; header[18] = 0; header[19] = 0 // fmt chunk size
        header[20] = 1; header[21] = 0 // audio format (PCM)
        header[22] = channels.toByte(); header[23] = 0 // number of channels
        header[24] = (sampleRate and 0xff).toByte(); header[25] = (sampleRate shr 8 and 0xff).toByte()
        header[26] = (sampleRate shr 16 and 0xff).toByte(); header[27] = (sampleRate shr 24 and 0xff).toByte()
        header[28] = (byteRate and 0xff).toByte(); header[29] = (byteRate shr 8 and 0xff).toByte()
        header[30] = (byteRate shr 16 and 0xff).toByte(); header[31] = (byteRate shr 24 and 0xff).toByte()
        header[32] = blockAlign.toByte(); header[33] = 0 // block align
        header[34] = bitsPerSample.toByte(); header[35] = 0 // bits per sample
        
        // data chunk
        header[36] = 'd'.code.toByte(); header[37] = 'a'.code.toByte(); header[38] = 't'.code.toByte(); header[39] = 'a'.code.toByte()
        header[40] = (dataSize and 0xff).toByte(); header[41] = (dataSize shr 8 and 0xff).toByte()
        header[42] = (dataSize shr 16 and 0xff).toByte(); header[43] = (dataSize shr 24 and 0xff).toByte()
        
        return header
    }

    /**
     * 播放音频文件
     */
    private fun playAudioFile(file: File) {
        stopSpeaking() // 停止当前播放
        
        currentAudioFile = file
        mediaPlayer = MediaPlayer().apply {
            setDataSource(file.absolutePath)
            setOnCompletionListener {
                onCompleteCallback?.invoke()
                stopSpeaking()
            }
            setOnErrorListener { _, _, _ ->
                onErrorCallback?.invoke("音频播放失败")
                stopSpeaking()
                true
            }
            setOnPreparedListener {
                start()
            }
            prepareAsync()
        }
    }

    override fun stopSpeaking() {
        try {
            mediaPlayer?.release()
            mediaPlayer = null
            
            // 清理临时音频文件
            currentAudioFile?.let { file ->
                if (file.exists()) {
                    file.delete()
                }
            }
            currentAudioFile = null
            Log.d(TAG, "停止播放")
        } catch (e: Exception) {
            Log.e(TAG, "停止播放时出错", e)
        }
    }

    override fun isSpeaking(): Boolean {
        return mediaPlayer?.isPlaying == true
    }

    override fun setSpeechRate(rate: Float) {
        speechRate = rate
        // Gemini TTS API目前不支持运行时调整语速
        Log.d(TAG, "设置语速: $rate (Gemini TTS暂不支持运行时调整)")
    }

    override fun setPitch(pitch: Float) {
        this.pitch = pitch
        // Gemini TTS API目前不支持运行时调整音调
        Log.d(TAG, "设置音调: $pitch (Gemini TTS暂不支持运行时调整)")
    }

    override suspend fun setLanguage(language: String): Result<Unit> {
        return try {
            this.language = language
            // Gemini TTS API会根据文本内容自动识别语言
            Log.d(TAG, "设置语言: $language (Gemini TTS自动识别语言)")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "设置语言时出错", e)
            Result.failure(e)
        }
    }

    override fun isAvailable(): Boolean {
        return BuildConfig.GEMINI_TTS_API_KEY.isNotBlank()
    }

    override fun cleanup() {
        try {
            stopSpeaking()
            onStartCallback = null
            onCompleteCallback = null
            onErrorCallback = null
            Log.d(TAG, "Gemini TTS引擎资源已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理Gemini TTS引擎资源时出错", e)
        }
    }
} 