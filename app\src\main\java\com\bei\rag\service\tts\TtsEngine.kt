package com.bei.rag.service.tts

import com.bei.rag.utils.TtsEngineType

/**
 * TTS引擎接口
 * 定义所有TTS引擎的统一接口
 */
interface TtsEngine {
    
    /**
     * 获取引擎类型
     */
    fun getEngineType(): TtsEngineType

    /**
     * 初始化TTS引擎
     */
    suspend fun initialize(): Result<Unit>

    /**
     * 将文字转换为语音并播放
     * @param text 要朗读的文本
     * @param onStart 开始播放回调
     * @param onComplete 播放完成回调
     * @param onError 播放错误回调
     */
    suspend fun speakText(
        text: String,
        onStart: (() -> Unit)? = null,
        onComplete: (() -> Unit)? = null,
        onError: ((String) -> Unit)? = null
    ): Result<Unit>

    /**
     * 停止当前播放
     */
    fun stopSpeaking()

    /**
     * 检查是否正在播放
     */
    fun isSpeaking(): Boolean

    /**
     * 设置语速
     * @param rate 语速，1.0为正常速度
     */
    fun setSpeechRate(rate: Float)

    /**
     * 设置音调
     * @param pitch 音调，1.0为正常音调
     */
    fun setPitch(pitch: Float)

    /**
     * 设置语言
     * @param language 语言代码，如"zh-CN"
     */
    suspend fun setLanguage(language: String): Result<Unit>

    /**
     * 检查引擎是否可用
     */
    fun isAvailable(): Boolean

    /**
     * 清理资源
     */
    fun cleanup()
} 