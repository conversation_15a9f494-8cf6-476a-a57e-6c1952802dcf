package com.bei.rag.utils

import com.bei.rag.BuildConfig

/**
 * API配置工具类
 * 提供安全的API密钥访问方式
 */
object ApiConfig {
    
    /**
     * 获取OpenRouter API密钥
     * @return API密钥，如果未配置则返回空字符串
     */
    fun getOpenRouterApiKey(): String {
        return BuildConfig.OPENROUTER_API_KEY.takeIf { it.isNotBlank() } 
            ?: throw IllegalStateException("OpenRouter API密钥未配置，请在local.properties中设置OPENROUTER_API_KEY")
    }
    
    /**
     * 获取Gemini模型名称
     * @return 模型名称
     */
    fun getGeminiModel(): String {
        return BuildConfig.GEMINI_MODEL.takeIf { it.isNotBlank() } 
            ?: "google/gemini-2.5-flash-lite-preview-06-17"
    }
    
    /**
     * 检查API密钥是否已配置
     * @return true如果已配置，false如果未配置
     */
    fun isApiKeyConfigured(): Boolean {
        return BuildConfig.OPENROUTER_API_KEY.isNotBlank()
    }
    
    /**
     * 获取硅基流动API密钥
     * @return API密钥，如果未配置则抛出异常
     */
    fun getSiliconFlowApiKey(): String {
        return BuildConfig.SILICONFLOW_API_KEY.takeIf { it.isNotBlank() } 
            ?: throw IllegalStateException("硅基流动API密钥未配置，请在local.properties中设置SILICONFLOW_API_KEY")
    }
    
    /**
     * 检查硅基流动API密钥是否已配置
     * @return true如果已配置，false如果未配置
     */
    fun isSiliconFlowApiKeyConfigured(): Boolean {
        return BuildConfig.SILICONFLOW_API_KEY.isNotBlank()
    }

    /**
     * 获取API基础URL
     */
    const val OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1/"
    const val SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1/"
}
