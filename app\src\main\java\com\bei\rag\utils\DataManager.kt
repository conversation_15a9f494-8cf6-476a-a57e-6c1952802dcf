package com.bei.rag.utils

import android.content.Context
import com.bei.rag.database.AppDatabase
import com.bei.rag.database.entity.UserEntity
import java.io.File

class DataManager(private val context: Context) {
    
    private val database = AppDatabase.getDatabase(context)
    
    /**
     * 清除所有应用数据
     */
    suspend fun clearAllData() {
        // 清除聊天记录
        database.chatMessageDao().deleteAllMessages()
        
        // 清除文档数据
        database.documentDao().deleteAllDocuments()
        
        // 清除用户信息
        database.userDao().clearUserInfo()
        
        // 清除文档文件
        clearDocumentFiles()
    }
    
    /**
     * 清除所有数据并重置为默认状态
     */
    suspend fun clearAllDataAndReset() {
        // 清除所有数据
        clearAllData()
        
        // 重置用户为默认状态
        val defaultUser = UserEntity()
        database.userDao().insertOrUpdateUser(defaultUser)
    }
    
    /**
     * 只清除聊天记录
     */
    suspend fun clearChatHistory() {
        database.chatMessageDao().deleteAllMessages()
    }
    
    /**
     * 只清除文档数据
     */
    suspend fun clearDocuments() {
        database.documentDao().deleteAllDocuments()
        clearDocumentFiles()
    }
    
    /**
     * 清除文档文件
     */
    private fun clearDocumentFiles() {
        try {
            val documentsDir = File(context.filesDir, "documents")
            if (documentsDir.exists()) {
                documentsDir.listFiles()?.forEach { file ->
                    file.delete()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 只退出登录，不清除任何数据
     * 这个方法主要用于状态重置和日志记录
     */
    suspend fun logoutOnly() {
        // 更新最后登录时间为退出时间
        val currentTime = System.currentTimeMillis()
        database.userDao().updateLastLoginTime(currentTime)

        // 这里可以添加其他退出登录的逻辑，比如：
        // - 清除临时缓存
        // - 记录退出日志
        // - 重置某些状态标志
    }

    /**
     * 获取数据统计信息
     */
    suspend fun getDataStatistics(): DataStatistics {
        val chatCount = database.chatMessageDao().getMessageCount()
        val documentCount = database.documentDao().getDocumentCount()
        val totalSize = database.documentDao().getTotalFileSize() ?: 0L

        return DataStatistics(
            chatMessageCount = chatCount,
            documentCount = documentCount,
            totalFileSize = totalSize
        )
    }
}

data class DataStatistics(
    val chatMessageCount: Int,
    val documentCount: Int,
    val totalFileSize: Long
)
