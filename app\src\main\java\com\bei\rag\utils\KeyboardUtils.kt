package com.bei.rag.utils

import android.app.Activity
import android.graphics.Rect
import android.view.View
import android.view.ViewTreeObserver
import android.view.WindowManager

object KeyboardUtils {
    
    interface KeyboardListener {
        fun onKeyboardShow(keyboardHeight: Int)
        fun onKeyboardHide()
    }
    
    fun setupKeyboardListener(activity: Activity, listener: KeyboardListener) {
        val rootView = activity.findViewById<View>(android.R.id.content)
        var rootViewHeight = 0
        
        rootView.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val rect = Rect()
                rootView.getWindowVisibleDisplayFrame(rect)
                
                val screenHeight = rootView.height
                val keyboardHeight = screenHeight - rect.bottom
                
                if (rootViewHeight == 0) {
                    rootViewHeight = screenHeight
                }
                
                // 键盘高度超过屏幕15%认为是打开状态
                if (keyboardHeight > rootViewHeight * 0.15) {
                    listener.onKeyboardShow(keyboardHeight)
                } else {
                    listener.onKeyboardHide()
                }
            }
        })
    }
    
    fun setAdjustResize(activity: Activity) {
        activity.window.setSoftInputMode(
            WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE or
            WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN
        )
    }
    
    fun setAdjustPan(activity: Activity) {
        activity.window.setSoftInputMode(
            WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN or
            WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN
        )
    }
}
