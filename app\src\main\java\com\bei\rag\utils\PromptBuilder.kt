package com.bei.rag.utils

/**
 * 提示词构建器
 * 集中管理所有AI提示词模板和构建逻辑，避免重复代码
 */
object PromptBuilder {
    
    private const val MAX_CONTEXT_LENGTH = 4000 // 限制上下文长度
    
    /**
     * 基础提示词模板
     */
    private const val BASE_PROMPT_TEMPLATE = """
你是一个知识渊博的助手，请根据以下提供的上下文信息来回答用户的问题。不要输入别的内容只需要输出返回的答案即可。请确保你的答案准确、相关，并且基于上下文。优先输出知识库当中返回的内容，若相关则直接输出。如果上下文信息不包含答案，请简单回答一些客观的内容，不要编造信息。"""
    
    /**
     * 构建标准的增强上下文提示词
     * @param originalContext 原始文档上下文
     * @param query 用户查询
     * @return 完整的提示词
     */
    fun buildStandardPrompt(originalContext: String, query: String): String {
        if (originalContext.isEmpty()) return buildNoContextPrompt(query)
        
        // 限制上下文长度
        val truncatedContext = if (originalContext.length > MAX_CONTEXT_LENGTH) {
            originalContext.take(MAX_CONTEXT_LENGTH) + "..."
        } else {
            originalContext
        }
        
        return """
$BASE_PROMPT_TEMPLATE

用户问题：$query

知识库文档内容：
$truncatedContext

请严格按照上述文档内容回答用户问题。
        """.trimIndent()
    }
    
    /**
     * 构建低相似度结果的特殊提示词
     * @param originalContext 原始文档上下文
     * @param query 用户查询
     * @param maxSimilarity 最大相似度分数
     * @return 完整的提示词
     */
    fun buildLowSimilarityPrompt(originalContext: String, query: String, maxSimilarity: Double): String {
        if (originalContext.isEmpty()) return buildNoContextPrompt(query)
        
        // 限制上下文长度
        val truncatedContext = if (originalContext.length > MAX_CONTEXT_LENGTH) {
            originalContext.take(MAX_CONTEXT_LENGTH) + "..."
        } else {
            originalContext
        }
        
        val similarityPercent = (maxSimilarity * 100).toInt()
        
        return """
$BASE_PROMPT_TEMPLATE

注意：当前找到的文档与问题相似度较低（约${similarityPercent}%），请谨慎参考。

用户问题：$query

知识库文档内容（相似度${similarityPercent}%）：
$truncatedContext

请严格按照上述文档内容回答用户问题。
        """.trimIndent()
    }
    
    /**
     * 构建无上下文的提示词
     * @param query 用户查询
     * @return 完整的提示词
     */
    fun buildNoContextPrompt(query: String): String {
        return """
$BASE_PROMPT_TEMPLATE

用户问题：$query

知识库文档内容：
（暂无相关文档）

请基于当前情况回答用户问题。
        """.trimIndent()
    }
    
    /**
     * 获取最大上下文长度限制
     * @return 最大上下文长度
     */
    fun getMaxContextLength(): Int = MAX_CONTEXT_LENGTH
} 