package com.bei.rag.utils

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.app.Activity
import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.Window
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.annotation.ColorInt
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat

/**
 * 状态栏动画管理器
 * 专门处理状态栏主题切换时的平滑过渡动画效果
 */
class StatusBarAnimationManager(private val activity: Activity) {

    companion object {
        // 动画持续时间常量
        private const val COLOR_TRANSITION_DURATION = 300L
        private const val CONTENT_TRANSITION_DURATION = 200L
        private const val OVERLAY_TRANSITION_DURATION = 250L
        private const val THEME_SWITCH_DURATION = 400L
        
        // 动画延迟常量
        private const val CONTENT_DELAY = 100L
        private const val OVERLAY_DELAY = 50L
    }

    // Remove circular dependency - StatusBarAnimationManager should not create StatusBarManager
    private val colorAdapter = StatusBarColorAdapter(activity)
    private val window: Window = activity.window
    
    // Helper methods to directly manage status bar without circular dependency
    private fun setStatusBarColor(@ColorInt color: Int, animate: Boolean = false) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.statusBarColor = color
        }
    }
    
    private fun setLightStatusBarContent(lightContent: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            WindowCompat.setDecorFitsSystemWindows(window, false)
            val controller = WindowInsetsControllerCompat(window, window.decorView)
            controller.isAppearanceLightStatusBars = lightContent
        }
    }
    
    // 当前运行的动画
    private var currentColorAnimator: ValueAnimator? = null
    private var currentContentAnimator: ValueAnimator? = null
    private var currentOverlayAnimator: ValueAnimator? = null

    /**
     * 主题颜色切换动画
     * @param fromTheme 原主题颜色名称
     * @param toTheme 目标主题颜色名称
     * @param onComplete 动画完成回调
     */
    fun animateThemeColorChange(
        fromTheme: String,
        toTheme: String,
        onComplete: (() -> Unit)? = null
    ) {
        val isDarkMode = ThemeManager(activity).isDarkMode()
        
        val fromColor = colorAdapter.getStatusBarBackgroundColor(fromTheme, isDarkMode)
        val toColor = colorAdapter.getStatusBarBackgroundColor(toTheme, isDarkMode)
        
        // 应用遮罩效果
        val fromFinalColor = colorAdapter.applyOverlayToColor(fromColor, isDarkMode)
        val toFinalColor = colorAdapter.applyOverlayToColor(toColor, isDarkMode)
        
        animateStatusBarColorTransition(fromFinalColor, toFinalColor, THEME_SWITCH_DURATION) {
            onComplete?.invoke()
        }
    }

    /**
     * 主题模式切换动画（浅色/深色模式切换）
     * @param themeColor 当前主题颜色
     * @param fromDarkMode 原模式是否为深色
     * @param toDarkMode 目标模式是否为深色
     * @param onComplete 动画完成回调
     */
    fun animateModeSwitch(
        themeColor: String,
        fromDarkMode: Boolean,
        toDarkMode: Boolean,
        onComplete: (() -> Unit)? = null
    ) {
        val fromColor = colorAdapter.getStatusBarBackgroundColor(themeColor, fromDarkMode)
        val toColor = colorAdapter.getStatusBarBackgroundColor(themeColor, toDarkMode)
        
        val fromFinalColor = colorAdapter.applyOverlayToColor(fromColor, fromDarkMode)
        val toFinalColor = colorAdapter.applyOverlayToColor(toColor, toDarkMode)
        
        // 同时动画化背景色和内容颜色
        animateCompleteThemeTransition(
            fromFinalColor, toFinalColor,
            colorAdapter.shouldUseLightStatusBarContent(fromDarkMode),
            colorAdapter.shouldUseLightStatusBarContent(toDarkMode),
            onComplete
        )
    }

    /**
     * 完整的主题过渡动画（背景色 + 内容颜色）
     */
    private fun animateCompleteThemeTransition(
        @ColorInt fromBackgroundColor: Int,
        @ColorInt toBackgroundColor: Int,
        fromLightContent: Boolean,
        toLightContent: Boolean,
        onComplete: (() -> Unit)? = null
    ) {
        var animationsCompleted = 0
        val totalAnimations = if (fromLightContent != toLightContent) 2 else 1
        
        val checkCompletion = {
            animationsCompleted++
            if (animationsCompleted >= totalAnimations) {
                onComplete?.invoke()
            }
        }
        
        // 背景色动画
        animateStatusBarColorTransition(fromBackgroundColor, toBackgroundColor, THEME_SWITCH_DURATION) {
            checkCompletion()
        }
        
        // 内容颜色动画（如果需要）
        if (fromLightContent != toLightContent) {
            animateContentColorTransition(fromLightContent, toLightContent, CONTENT_DELAY) {
                checkCompletion()
            }
        }
    }

    /**
     * 状态栏背景色过渡动画
     */
    private fun animateStatusBarColorTransition(
        @ColorInt fromColor: Int,
        @ColorInt toColor: Int,
        duration: Long = COLOR_TRANSITION_DURATION,
        onComplete: (() -> Unit)? = null
    ) {
        // 取消当前动画
        currentColorAnimator?.cancel()
        
        currentColorAnimator = ValueAnimator.ofObject(ArgbEvaluator(), fromColor, toColor).apply {
            this.duration = duration
            interpolator = AccelerateDecelerateInterpolator()
            
            addUpdateListener { animator ->
                val animatedColor = animator.animatedValue as Int
                setStatusBarColor(animatedColor, false)
            }
            
            doOnEnd {
                currentColorAnimator = null
                onComplete?.invoke()
            }
            
            start()
        }
    }

    /**
     * 状态栏内容颜色过渡动画
     */
    private fun animateContentColorTransition(
        fromLightContent: Boolean,
        toLightContent: Boolean,
        delay: Long = 0L,
        onComplete: (() -> Unit)? = null
    ) {
        // 取消当前动画
        currentContentAnimator?.cancel()
        
        // 创建透明度动画来实现平滑过渡
        currentContentAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            this.duration = CONTENT_TRANSITION_DURATION
            startDelay = delay
            interpolator = DecelerateInterpolator()
            
            var hasSetMidpoint = false
            
            addUpdateListener { animator ->
                val progress = animator.animatedValue as Float
                
                // 在动画中点切换内容颜色
                if (progress >= 0.5f && !hasSetMidpoint) {
                    setLightStatusBarContent(toLightContent)
                    hasSetMidpoint = true
                }
            }
            
            doOnStart {
                // 动画开始时设置初始状态
                setLightStatusBarContent(fromLightContent)
            }
            
            doOnEnd {
                // 确保最终状态正确
                setLightStatusBarContent(toLightContent)
                currentContentAnimator = null
                onComplete?.invoke()
            }
            
            start()
        }
    }

    /**
     * 遮罩层过渡动画
     */
    fun animateOverlayTransition(
        overlayView: View,
        @ColorInt fromColor: Int,
        @ColorInt toColor: Int,
        onComplete: (() -> Unit)? = null
    ) {
        currentOverlayAnimator?.cancel()
        
        currentOverlayAnimator = ValueAnimator.ofObject(ArgbEvaluator(), fromColor, toColor).apply {
            duration = OVERLAY_TRANSITION_DURATION
            interpolator = AccelerateDecelerateInterpolator()
            
            addUpdateListener { animator ->
                val animatedColor = animator.animatedValue as Int
                overlayView.setBackgroundColor(animatedColor)
            }
            
            doOnEnd {
                currentOverlayAnimator = null
                onComplete?.invoke()
            }
            
            start()
        }
    }

    /**
     * 渐变遮罩过渡动画
     */
    fun animateGradientOverlayTransition(
        overlayView: View,
        @ColorInt fromStartColor: Int,
        @ColorInt fromEndColor: Int,
        @ColorInt toStartColor: Int,
        @ColorInt toEndColor: Int,
        onComplete: (() -> Unit)? = null
    ) {
        currentOverlayAnimator?.cancel()
        
        currentOverlayAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = OVERLAY_TRANSITION_DURATION
            interpolator = AccelerateDecelerateInterpolator()
            
            addUpdateListener { animator ->
                val progress = animator.animatedValue as Float
                
                // 插值计算当前的渐变颜色
                val currentStartColor = interpolateColor(fromStartColor, toStartColor, progress)
                val currentEndColor = interpolateColor(fromEndColor, toEndColor, progress)
                
                // 创建新的渐变背景
                val gradientDrawable = android.graphics.drawable.GradientDrawable(
                    android.graphics.drawable.GradientDrawable.Orientation.TOP_BOTTOM,
                    intArrayOf(currentStartColor, currentEndColor)
                )
                overlayView.background = gradientDrawable
            }
            
            doOnEnd {
                currentOverlayAnimator = null
                onComplete?.invoke()
            }
            
            start()
        }
    }

    /**
     * 颜色插值计算
     */
    private fun interpolateColor(@ColorInt startColor: Int, @ColorInt endColor: Int, fraction: Float): Int {
        val argbEvaluator = ArgbEvaluator()
        return argbEvaluator.evaluate(fraction, startColor, endColor) as Int
    }

    /**
     * 弹性动画效果
     * 适用于用户交互触发的主题切换
     */
    fun animateWithBounceEffect(
        @ColorInt fromColor: Int,
        @ColorInt toColor: Int,
        onComplete: (() -> Unit)? = null
    ) {
        currentColorAnimator?.cancel()
        
        currentColorAnimator = ValueAnimator.ofObject(ArgbEvaluator(), fromColor, toColor).apply {
            duration = THEME_SWITCH_DURATION
            interpolator = android.view.animation.BounceInterpolator()
            
            addUpdateListener { animator ->
                val animatedColor = animator.animatedValue as Int
                setStatusBarColor(animatedColor, false)
            }
            
            doOnEnd {
                currentColorAnimator = null
                onComplete?.invoke()
            }
            
            start()
        }
    }

    /**
     * 脉冲动画效果
     * 用于强调状态栏变化
     */
    fun animateWithPulseEffect(
        @ColorInt baseColor: Int,
        @ColorInt highlightColor: Int,
        pulseCount: Int = 2,
        onComplete: (() -> Unit)? = null
    ) {
        currentColorAnimator?.cancel()
        
        val colors = mutableListOf<Int>()
        repeat(pulseCount) {
            colors.add(baseColor)
            colors.add(highlightColor)
        }
        colors.add(baseColor)
        
        currentColorAnimator = ValueAnimator.ofObject(ArgbEvaluator(), *colors.toTypedArray()).apply {
            duration = THEME_SWITCH_DURATION * pulseCount
            interpolator = AccelerateDecelerateInterpolator()
            
            addUpdateListener { animator ->
                val animatedColor = animator.animatedValue as Int
                setStatusBarColor(animatedColor, false)
            }
            
            doOnEnd {
                currentColorAnimator = null
                onComplete?.invoke()
            }
            
            start()
        }
    }

    /**
     * 取消所有正在运行的动画
     */
    fun cancelAllAnimations() {
        currentColorAnimator?.cancel()
        currentContentAnimator?.cancel()
        currentOverlayAnimator?.cancel()
        
        currentColorAnimator = null
        currentContentAnimator = null
        currentOverlayAnimator = null
    }

    /**
     * 检查是否有动画正在运行
     */
    fun isAnimating(): Boolean {
        return currentColorAnimator?.isRunning == true ||
               currentContentAnimator?.isRunning == true ||
               currentOverlayAnimator?.isRunning == true
    }

    /**
     * 立即完成所有动画
     */
    fun finishAllAnimations() {
        currentColorAnimator?.end()
        currentContentAnimator?.end()
        currentOverlayAnimator?.end()
    }

    /**
     * 设置动画监听器
     */
    fun setAnimationListener(listener: AnimationListener) {
        this.animationListener = listener
    }

    private var animationListener: AnimationListener? = null

    /**
     * 动画监听器接口
     */
    interface AnimationListener {
        fun onAnimationStart()
        fun onAnimationEnd()
        fun onAnimationCancel()
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        cancelAllAnimations()
        animationListener = null
    }
}