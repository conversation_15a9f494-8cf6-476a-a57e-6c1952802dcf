package com.bei.rag.utils

import android.content.Context
import android.graphics.Color
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import com.bei.rag.R

/**
 * 状态栏颜色适配器
 * 专门处理不同主题和模式下的状态栏颜色适配
 */
class StatusBarColorAdapter(private val context: Context) {

    private val themeManager = ThemeManager(context)

    /**
     * 获取适合当前主题的状态栏背景色
     * @param themeColor 当前主题颜色名称
     * @param isDarkMode 是否为深色模式
     * @return 状态栏背景颜色
     */
    @ColorInt
    fun getStatusBarBackgroundColor(themeColor: String, isDarkMode: Boolean): Int {
        return if (isDarkMode) {
            getDarkModeStatusBarColor(themeColor)
        } else {
            getLightModeStatusBarColor(themeColor)
        }
    }

    /**
     * 获取浅色模式下的状态栏颜色
     * 使用较浅的颜色以确保深色文字可见
     */
    @ColorInt
    private fun getLightModeStatusBarColor(themeColor: String): Int {
        return when (themeColor) {
            ThemeManager.THEME_COLOR_BLUE -> ContextCompat.getColor(context, R.color.status_bar_light_blue)
            ThemeManager.THEME_COLOR_GREEN -> ContextCompat.getColor(context, R.color.status_bar_light_green)
            ThemeManager.THEME_COLOR_RED -> ContextCompat.getColor(context, R.color.status_bar_light_red)
            ThemeManager.THEME_COLOR_PURPLE -> ContextCompat.getColor(context, R.color.status_bar_light_purple)
            ThemeManager.THEME_COLOR_ORANGE -> ContextCompat.getColor(context, R.color.status_bar_light_orange)
            ThemeManager.THEME_COLOR_PINK -> ContextCompat.getColor(context, R.color.status_bar_light_pink)
            ThemeManager.THEME_COLOR_GRAY -> ContextCompat.getColor(context, R.color.status_bar_light_gray)
            else -> ContextCompat.getColor(context, R.color.status_bar_light_gray)
        }
    }

    /**
     * 获取深色模式下的状态栏颜色
     * 使用较深的颜色以确保浅色文字可见
     */
    @ColorInt
    private fun getDarkModeStatusBarColor(themeColor: String): Int {
        return when (themeColor) {
            ThemeManager.THEME_COLOR_BLUE -> ContextCompat.getColor(context, R.color.status_bar_dark_blue)
            ThemeManager.THEME_COLOR_GREEN -> ContextCompat.getColor(context, R.color.status_bar_dark_green)
            ThemeManager.THEME_COLOR_RED -> ContextCompat.getColor(context, R.color.status_bar_dark_red)
            ThemeManager.THEME_COLOR_PURPLE -> ContextCompat.getColor(context, R.color.status_bar_dark_purple)
            ThemeManager.THEME_COLOR_ORANGE -> ContextCompat.getColor(context, R.color.status_bar_dark_orange)
            ThemeManager.THEME_COLOR_PINK -> ContextCompat.getColor(context, R.color.status_bar_dark_pink)
            ThemeManager.THEME_COLOR_GRAY -> ContextCompat.getColor(context, R.color.status_bar_dark_gray)
            else -> ContextCompat.getColor(context, R.color.status_bar_dark_gray)
        }
    }

    /**
     * 获取状态栏文字颜色
     * @param isDarkMode 是否为深色模式
     * @return true表示使用深色文字（浅色背景），false表示使用浅色文字（深色背景）
     */
    fun shouldUseLightStatusBarContent(isDarkMode: Boolean): Boolean {
        // 浅色模式使用深色文字，深色模式使用浅色文字
        return !isDarkMode
    }

    /**
     * 获取状态栏文字的实际颜色值
     * @param isDarkMode 是否为深色模式
     * @return 文字颜色
     */
    @ColorInt
    fun getStatusBarTextColor(isDarkMode: Boolean): Int {
        return if (isDarkMode) {
            ContextCompat.getColor(context, R.color.status_bar_text_dark)
        } else {
            ContextCompat.getColor(context, R.color.status_bar_text_light)
        }
    }

    /**
     * 获取半透明遮罩颜色
     * @param isDarkMode 是否为深色模式
     * @return 遮罩颜色
     */
    @ColorInt
    fun getStatusBarOverlayColor(isDarkMode: Boolean): Int {
        return if (isDarkMode) {
            ContextCompat.getColor(context, R.color.status_bar_overlay_dark)
        } else {
            ContextCompat.getColor(context, R.color.status_bar_overlay_light)
        }
    }

    /**
     * 为指定颜色应用半透明遮罩
     * @param baseColor 基础颜色
     * @param isDarkMode 是否为深色模式
     * @return 应用遮罩后的颜色
     */
    @ColorInt
    fun applyOverlayToColor(@ColorInt baseColor: Int, isDarkMode: Boolean): Int {
        val overlayColor = getStatusBarOverlayColor(isDarkMode)
        return blendColors(baseColor, overlayColor, 0.1f)
    }

    /**
     * 混合两种颜色
     * @param color1 基础颜色
     * @param color2 遮罩颜色
     * @param ratio 混合比例 (0.0-1.0)
     * @return 混合后的颜色
     */
    @ColorInt
    private fun blendColors(@ColorInt color1: Int, @ColorInt color2: Int, ratio: Float): Int {
        val inverseRatio = 1f - ratio
        val r = (Color.red(color1) * inverseRatio + Color.red(color2) * ratio).toInt()
        val g = (Color.green(color1) * inverseRatio + Color.green(color2) * ratio).toInt()
        val b = (Color.blue(color1) * inverseRatio + Color.blue(color2) * ratio).toInt()
        val a = (Color.alpha(color1) * inverseRatio + Color.alpha(color2) * ratio).toInt()
        return Color.argb(a, r, g, b)
    }

    /**
     * 检查颜色是否需要深色文字
     * 基于颜色亮度判断
     */
    fun needsDarkText(@ColorInt backgroundColor: Int): Boolean {
        val red = Color.red(backgroundColor)
        val green = Color.green(backgroundColor)
        val blue = Color.blue(backgroundColor)
        
        // 计算相对亮度 (基于 WCAG 2.0 标准)
        val luminance = (0.299 * red + 0.587 * green + 0.114 * blue) / 255
        return luminance > 0.5
    }

    /**
     * 获取完整的状态栏配置
     * @return StatusBarConfig 包含所有状态栏样式信息
     */
    fun getStatusBarConfig(): StatusBarConfig {
        val currentTheme = themeManager.getThemeColor()
        val isDarkMode = themeManager.isDarkMode()
        
        return StatusBarConfig(
            backgroundColor = getStatusBarBackgroundColor(currentTheme, isDarkMode),
            textColor = getStatusBarTextColor(isDarkMode),
            useLightContent = shouldUseLightStatusBarContent(isDarkMode),
            overlayColor = getStatusBarOverlayColor(isDarkMode),
            isDarkMode = isDarkMode,
            themeColor = currentTheme
        )
    }

    /**
     * 状态栏配置数据类
     */
    data class StatusBarConfig(
        @ColorInt val backgroundColor: Int,
        @ColorInt val textColor: Int,
        val useLightContent: Boolean,
        @ColorInt val overlayColor: Int,
        val isDarkMode: Boolean,
        val themeColor: String
    )
}