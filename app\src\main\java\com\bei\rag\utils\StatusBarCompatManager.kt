package com.bei.rag.utils

import android.app.Activity
import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.annotation.ColorInt
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat

/**
 * 状态栏兼容性管理器
 * 处理不同Android版本的状态栏API差异，确保在所有支持的版本上正常工作
 */
class StatusBarCompatManager(private val activity: Activity) {

    private val window: Window = activity.window

    companion object {
        // Android版本常量
        private const val API_KITKAT = Build.VERSION_CODES.KITKAT // 19
        private const val API_LOLLIPOP = Build.VERSION_CODES.LOLLIPOP // 21
        private const val API_MARSHMALLOW = Build.VERSION_CODES.M // 23
        private const val API_R = Build.VERSION_CODES.R // 30
        private const val API_S = Build.VERSION_CODES.S // 31
    }

    /**
     * 设置状态栏颜色（兼容所有版本）
     * @param color 状态栏颜色
     */
    fun setStatusBarColor(@ColorInt color: Int) {
        when {
            Build.VERSION.SDK_INT >= API_LOLLIPOP -> {
                // Android 5.0+ 支持直接设置状态栏颜色
                window.statusBarColor = color
            }
            Build.VERSION.SDK_INT >= API_KITKAT -> {
                // Android 4.4 使用半透明状态栏
                setTranslucentStatusBar()
            }
            else -> {
                // Android 4.4以下版本不支持状态栏自定义
                // 可以考虑使用第三方库或者不做处理
            }
        }
    }

    /**
     * 设置状态栏文字和图标颜色（兼容处理）
     * @param lightContent true为深色内容，false为浅色内容
     */
    fun setStatusBarContentColor(lightContent: Boolean) {
        when {
            Build.VERSION.SDK_INT >= API_R -> {
                // Android 11+ 使用新的WindowInsetsController API
                val controller = window.insetsController
                if (controller != null) {
                    if (lightContent) {
                        controller.setSystemBarsAppearance(
                            android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                            android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                        )
                    } else {
                        controller.setSystemBarsAppearance(
                            0,
                            android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                        )
                    }
                }
            }
            Build.VERSION.SDK_INT >= API_MARSHMALLOW -> {
                // Android 6.0+ 使用WindowInsetsControllerCompat
                val controller = WindowInsetsControllerCompat(window, window.decorView)
                controller.isAppearanceLightStatusBars = lightContent
            }
            else -> {
                // Android 6.0以下版本不支持状态栏文字颜色控制
                // 只能通过调整状态栏背景色来确保可读性
                adjustStatusBarForLegacyVersions(lightContent)
            }
        }
    }

    /**
     * 为旧版本Android调整状态栏
     */
    private fun adjustStatusBarForLegacyVersions(needLightContent: Boolean) {
        if (Build.VERSION.SDK_INT >= API_LOLLIPOP) {
            // 如果需要深色内容但版本不支持，使用浅色背景
            if (needLightContent) {
                window.statusBarColor = Color.argb(255, 240, 240, 240)
            } else {
                window.statusBarColor = Color.argb(255, 50, 50, 50)
            }
        } else if (Build.VERSION.SDK_INT >= API_KITKAT) {
            // Android 4.4 使用半透明效果
            setTranslucentStatusBar()
        }
    }

    /**
     * 设置边到边显示（兼容处理）
     */
    fun setEdgeToEdge() {
        when {
            Build.VERSION.SDK_INT >= API_R -> {
                // Android 11+ 使用新API
                window.setDecorFitsSystemWindows(false)
            }
            Build.VERSION.SDK_INT >= API_LOLLIPOP -> {
                // Android 5.0+ 使用WindowCompat
                WindowCompat.setDecorFitsSystemWindows(window, false)
            }
            Build.VERSION.SDK_INT >= API_KITKAT -> {
                // Android 4.4 使用传统方式
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
            }
        }
    }

    /**
     * 设置半透明状态栏（Android 4.4专用）
     */
    private fun setTranslucentStatusBar() {
        if (Build.VERSION.SDK_INT >= API_KITKAT) {
            @Suppress("DEPRECATION")
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        }
    }

    /**
     * 清除半透明状态栏标志
     */
    private fun clearTranslucentStatusBar() {
        if (Build.VERSION.SDK_INT >= API_KITKAT) {
            @Suppress("DEPRECATION")
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        }
    }

    /**
     * 设置沉浸式状态栏（兼容处理）
     */
    fun setImmersiveStatusBar() {
        when {
            Build.VERSION.SDK_INT >= API_R -> {
                // Android 11+ 使用新的隐藏API
                window.insetsController?.hide(android.view.WindowInsets.Type.statusBars())
            }
            Build.VERSION.SDK_INT >= API_KITKAT -> {
                // Android 4.4+ 使用传统方式
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
            }
        }
    }

    /**
     * 恢复状态栏显示
     */
    fun showStatusBar() {
        when {
            Build.VERSION.SDK_INT >= API_R -> {
                // Android 11+ 显示状态栏
                window.insetsController?.show(android.view.WindowInsets.Type.statusBars())
            }
            Build.VERSION.SDK_INT >= API_KITKAT -> {
                // 清除隐藏标志
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
            }
        }
    }

    /**
     * 获取状态栏高度（兼容处理）
     */
    fun getStatusBarHeight(): Int {
        val resourceId = activity.resources.getIdentifier("status_bar_height", "dimen", "android")
        return if (resourceId > 0) {
            activity.resources.getDimensionPixelSize(resourceId)
        } else {
            // 如果无法获取系统资源，使用默认值
            when {
                Build.VERSION.SDK_INT >= API_LOLLIPOP -> 75 // 约25dp
                else -> 60 // 约20dp
            }
        }
    }

    /**
     * 检查是否支持状态栏颜色自定义
     */
    fun isStatusBarColorSupported(): Boolean {
        return Build.VERSION.SDK_INT >= API_LOLLIPOP
    }

    /**
     * 检查是否支持状态栏文字颜色控制
     */
    fun isStatusBarTextColorSupported(): Boolean {
        return Build.VERSION.SDK_INT >= API_MARSHMALLOW
    }

    /**
     * 检查是否支持新的WindowInsetsController
     */
    fun isNewInsetsControllerSupported(): Boolean {
        return Build.VERSION.SDK_INT >= API_R
    }

    /**
     * 应用兼容的状态栏样式
     * @param backgroundColor 背景颜色
     * @param lightContent 是否使用深色内容
     */
    fun applyCompatStatusBarStyle(@ColorInt backgroundColor: Int, lightContent: Boolean) {
        // 设置边到边显示
        setEdgeToEdge()
        
        // 设置状态栏颜色
        setStatusBarColor(backgroundColor)
        
        // 设置内容颜色
        setStatusBarContentColor(lightContent)
        
        // 确保窗口标志正确设置
        ensureProperWindowFlags()
    }

    /**
     * 确保窗口标志正确设置
     */
    private fun ensureProperWindowFlags() {
        when {
            Build.VERSION.SDK_INT >= API_LOLLIPOP -> {
                // Android 5.0+ 清除半透明标志，使用直接颜色设置
                clearTranslucentStatusBar()
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            }
            Build.VERSION.SDK_INT >= API_KITKAT -> {
                // Android 4.4 使用半透明
                setTranslucentStatusBar()
            }
        }
    }

    /**
     * 获取当前Android版本的状态栏处理策略
     */
    fun getStatusBarStrategy(): StatusBarStrategy {
        return when {
            Build.VERSION.SDK_INT >= API_S -> StatusBarStrategy.MODERN_API
            Build.VERSION.SDK_INT >= API_R -> StatusBarStrategy.NEW_INSETS_CONTROLLER
            Build.VERSION.SDK_INT >= API_MARSHMALLOW -> StatusBarStrategy.INSETS_CONTROLLER_COMPAT
            Build.VERSION.SDK_INT >= API_LOLLIPOP -> StatusBarStrategy.DIRECT_COLOR
            Build.VERSION.SDK_INT >= API_KITKAT -> StatusBarStrategy.TRANSLUCENT
            else -> StatusBarStrategy.LEGACY
        }
    }

    /**
     * 状态栏处理策略枚举
     */
    enum class StatusBarStrategy {
        MODERN_API,              // Android 12+ 最新API
        NEW_INSETS_CONTROLLER,   // Android 11+ WindowInsetsController
        INSETS_CONTROLLER_COMPAT, // Android 6.0+ WindowInsetsControllerCompat
        DIRECT_COLOR,            // Android 5.0+ 直接颜色设置
        TRANSLUCENT,             // Android 4.4+ 半透明
        LEGACY                   // Android 4.4以下
    }

    /**
     * 根据策略应用状态栏样式
     */
    fun applyStyleByStrategy(
        strategy: StatusBarStrategy,
        @ColorInt backgroundColor: Int,
        lightContent: Boolean
    ) {
        when (strategy) {
            StatusBarStrategy.MODERN_API,
            StatusBarStrategy.NEW_INSETS_CONTROLLER -> {
                applyModernStatusBarStyle(backgroundColor, lightContent)
            }
            StatusBarStrategy.INSETS_CONTROLLER_COMPAT -> {
                applyCompatStatusBarStyle(backgroundColor, lightContent)
            }
            StatusBarStrategy.DIRECT_COLOR -> {
                applyDirectColorStyle(backgroundColor, lightContent)
            }
            StatusBarStrategy.TRANSLUCENT -> {
                applyTranslucentStyle()
            }
            StatusBarStrategy.LEGACY -> {
                // 旧版本不做特殊处理
            }
        }
    }

    /**
     * 应用现代API样式
     */
    private fun applyModernStatusBarStyle(@ColorInt backgroundColor: Int, lightContent: Boolean) {
        setEdgeToEdge()
        setStatusBarColor(backgroundColor)
        setStatusBarContentColor(lightContent)
    }

    /**
     * 应用直接颜色样式
     */
    private fun applyDirectColorStyle(@ColorInt backgroundColor: Int, lightContent: Boolean) {
        clearTranslucentStatusBar()
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        setStatusBarColor(backgroundColor)
        
        // Android 5.0-5.1 不支持文字颜色控制，需要调整背景色
        if (Build.VERSION.SDK_INT < API_MARSHMALLOW) {
            adjustStatusBarForLegacyVersions(lightContent)
        } else {
            setStatusBarContentColor(lightContent)
        }
    }

    /**
     * 应用半透明样式
     */
    private fun applyTranslucentStyle() {
        setTranslucentStatusBar()
        setEdgeToEdge()
    }

    /**
     * 重置状态栏到系统默认状态
     */
    fun resetToSystemDefault() {
        when {
            Build.VERSION.SDK_INT >= API_LOLLIPOP -> {
                window.statusBarColor = Color.TRANSPARENT
                window.clearFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            }
            Build.VERSION.SDK_INT >= API_KITKAT -> {
                clearTranslucentStatusBar()
            }
        }
        
        // 重置系统UI可见性
        if (Build.VERSION.SDK_INT >= API_KITKAT) {
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
        }
    }
}