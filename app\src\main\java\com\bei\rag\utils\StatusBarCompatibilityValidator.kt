package com.bei.rag.utils

import android.app.Activity
import android.content.Context
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi

/**
 * 状态栏兼容性验证器
 * 用于验证状态栏功能在不同设备和系统版本上的兼容性
 */
class StatusBarCompatibilityValidator(private val context: Context) {

    companion object {
        private const val TAG = "StatusBarCompatibility"
    }

    /**
     * 设备兼容性信息
     */
    data class DeviceCompatibilityInfo(
        val deviceModel: String,
        val androidVersion: String,
        val apiLevel: Int,
        val manufacturer: String,
        val brand: String,
        val supportedFeatures: List<String>,
        val limitations: List<String>,
        val recommendations: List<String>
    )

    /**
     * 兼容性验证结果
     */
    data class CompatibilityResult(
        val isCompatible: Boolean,
        val compatibilityLevel: CompatibilityLevel,
        val deviceInfo: DeviceCompatibilityInfo,
        val issues: List<String>,
        val solutions: List<String>
    )

    /**
     * 兼容性等级
     */
    enum class CompatibilityLevel {
        FULL_SUPPORT,      // 完全支持
        PARTIAL_SUPPORT,   // 部分支持
        LIMITED_SUPPORT,   // 有限支持
        NO_SUPPORT         // 不支持
    }

    /**
     * 执行完整的兼容性验证
     */
    fun validateCompatibility(activity: Activity): CompatibilityResult {
        val deviceInfo = collectDeviceInfo()
        val supportedFeatures = checkSupportedFeatures()
        val limitations = identifyLimitations()
        val recommendations = generateRecommendations(deviceInfo, limitations)
        
        val compatibilityLevel = determineCompatibilityLevel(supportedFeatures, limitations)
        val issues = identifyIssues(deviceInfo, limitations)
        val solutions = generateSolutions(issues)
        
        val result = CompatibilityResult(
            isCompatible = compatibilityLevel != CompatibilityLevel.NO_SUPPORT,
            compatibilityLevel = compatibilityLevel,
            deviceInfo = deviceInfo.copy(
                supportedFeatures = supportedFeatures,
                limitations = limitations,
                recommendations = recommendations
            ),
            issues = issues,
            solutions = solutions
        )
        
        logCompatibilityResult(result)
        return result
    }

    /**
     * 收集设备信息
     */
    private fun collectDeviceInfo(): DeviceCompatibilityInfo {
        return DeviceCompatibilityInfo(
            deviceModel = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            manufacturer = Build.MANUFACTURER,
            brand = Build.BRAND,
            supportedFeatures = emptyList(),
            limitations = emptyList(),
            recommendations = emptyList()
        )
    }

    /**
     * 检查支持的功能
     */
    private fun checkSupportedFeatures(): List<String> {
        val features = mutableListOf<String>()
        
        // 检查状态栏颜色支持
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            features.add("状态栏颜色自定义")
        }
        
        // 检查状态栏文字颜色支持
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            features.add("状态栏文字颜色控制")
        }
        
        // 检查新版WindowInsetsController支持
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            features.add("新版WindowInsetsController")
        }
        
        // 检查边到边显示支持
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            features.add("边到边显示")
        }
        
        // 检查半透明状态栏支持
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            features.add("半透明状态栏")
        }
        
        // 检查动画支持
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            features.add("状态栏颜色动画")
        }
        
        // 检查模糊效果支持
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            features.add("模糊效果")
        }
        
        return features
    }

    /**
     * 识别限制和问题
     */
    private fun identifyLimitations(): List<String> {
        val limitations = mutableListOf<String>()
        
        when {
            Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT -> {
                limitations.add("不支持状态栏自定义")
                limitations.add("无法实现沉浸式体验")
            }
            Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP -> {
                limitations.add("仅支持半透明状态栏")
                limitations.add("无法直接设置状态栏颜色")
                limitations.add("不支持颜色动画")
            }
            Build.VERSION.SDK_INT < Build.VERSION_CODES.M -> {
                limitations.add("无法控制状态栏文字颜色")
                limitations.add("需要通过背景色调整确保可读性")
            }
            Build.VERSION.SDK_INT < Build.VERSION_CODES.R -> {
                limitations.add("使用旧版WindowInsetsController")
                limitations.add("部分新特性不可用")
            }
        }
        
        // 特定厂商限制
        when (Build.MANUFACTURER.lowercase()) {
            "xiaomi" -> {
                limitations.add("MIUI可能覆盖部分状态栏设置")
                limitations.add("需要适配MIUI特殊行为")
            }
            "huawei" -> {
                limitations.add("EMUI可能影响状态栏显示")
                limitations.add("华为设备可能需要特殊处理")
            }
            "oppo" -> {
                limitations.add("ColorOS可能修改状态栏行为")
            }
            "vivo" -> {
                limitations.add("FuntouchOS可能影响状态栏")
            }
            "samsung" -> {
                limitations.add("One UI可能有特殊的状态栏处理")
            }
        }
        
        return limitations
    }

    /**
     * 生成建议
     */
    private fun generateRecommendations(
        deviceInfo: DeviceCompatibilityInfo,
        limitations: List<String>
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        when {
            deviceInfo.apiLevel < Build.VERSION_CODES.KITKAT -> {
                recommendations.add("建议升级到Android 4.4+以获得基础状态栏功能")
            }
            deviceInfo.apiLevel < Build.VERSION_CODES.LOLLIPOP -> {
                recommendations.add("使用半透明状态栏替代方案")
                recommendations.add("通过遮罩层实现视觉效果")
            }
            deviceInfo.apiLevel < Build.VERSION_CODES.M -> {
                recommendations.add("使用浅色状态栏背景确保深色文字可读")
                recommendations.add("避免使用深色状态栏背景")
            }
        }
        
        // 厂商特定建议
        when (deviceInfo.manufacturer.lowercase()) {
            "xiaomi" -> {
                recommendations.add("测试MIUI环境下的状态栏表现")
                recommendations.add("考虑适配MIUI的状态栏API")
            }
            "huawei" -> {
                recommendations.add("在华为设备上进行充分测试")
                recommendations.add("关注EMUI版本差异")
            }
        }
        
        return recommendations
    }

    /**
     * 确定兼容性等级
     */
    private fun determineCompatibilityLevel(
        supportedFeatures: List<String>,
        limitations: List<String>
    ): CompatibilityLevel {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                if (limitations.size <= 2) CompatibilityLevel.FULL_SUPPORT
                else CompatibilityLevel.PARTIAL_SUPPORT
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                CompatibilityLevel.PARTIAL_SUPPORT
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP -> {
                CompatibilityLevel.LIMITED_SUPPORT
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {
                CompatibilityLevel.LIMITED_SUPPORT
            }
            else -> {
                CompatibilityLevel.NO_SUPPORT
            }
        }
    }

    /**
     * 识别具体问题
     */
    private fun identifyIssues(
        deviceInfo: DeviceCompatibilityInfo,
        limitations: List<String>
    ): List<String> {
        val issues = mutableListOf<String>()
        
        // API级别相关问题
        if (deviceInfo.apiLevel < Build.VERSION_CODES.M) {
            issues.add("状态栏文字颜色无法动态调整")
        }
        
        if (deviceInfo.apiLevel < Build.VERSION_CODES.LOLLIPOP) {
            issues.add("状态栏颜色设置受限")
        }
        
        // 厂商相关问题
        if (isKnownProblematicDevice(deviceInfo)) {
            issues.add("该设备/厂商可能存在状态栏兼容性问题")
        }
        
        return issues
    }

    /**
     * 生成解决方案
     */
    private fun generateSolutions(issues: List<String>): List<String> {
        val solutions = mutableListOf<String>()
        
        issues.forEach { issue ->
            when {
                issue.contains("文字颜色") -> {
                    solutions.add("使用浅色背景确保深色文字可读性")
                }
                issue.contains("颜色设置") -> {
                    solutions.add("使用半透明遮罩层替代直接颜色设置")
                }
                issue.contains("兼容性问题") -> {
                    solutions.add("在目标设备上进行充分测试")
                    solutions.add("提供降级方案")
                }
            }
        }
        
        return solutions
    }

    /**
     * 检查是否为已知问题设备
     */
    private fun isKnownProblematicDevice(deviceInfo: DeviceCompatibilityInfo): Boolean {
        val problematicCombinations = listOf(
            "xiaomi" to "redmi",
            "huawei" to "honor",
            "oppo" to "oneplus"
        )
        
        return problematicCombinations.any { (manufacturer, brand) ->
            deviceInfo.manufacturer.lowercase().contains(manufacturer) ||
            deviceInfo.brand.lowercase().contains(brand)
        }
    }

    /**
     * 记录兼容性验证结果
     */
    private fun logCompatibilityResult(result: CompatibilityResult) {
        Log.i(TAG, "=== 状态栏兼容性验证结果 ===")
        Log.i(TAG, "设备: ${result.deviceInfo.brand} ${result.deviceInfo.deviceModel}")
        Log.i(TAG, "Android版本: ${result.deviceInfo.androidVersion} (API ${result.deviceInfo.apiLevel})")
        Log.i(TAG, "兼容性等级: ${result.compatibilityLevel}")
        Log.i(TAG, "支持功能: ${result.deviceInfo.supportedFeatures.joinToString(", ")}")
        
        if (result.deviceInfo.limitations.isNotEmpty()) {
            Log.w(TAG, "限制: ${result.deviceInfo.limitations.joinToString(", ")}")
        }
        
        if (result.issues.isNotEmpty()) {
            Log.w(TAG, "问题: ${result.issues.joinToString(", ")}")
        }
        
        if (result.solutions.isNotEmpty()) {
            Log.i(TAG, "解决方案: ${result.solutions.joinToString(", ")}")
        }
        
        Log.i(TAG, "========================")
    }

    /**
     * 生成兼容性报告
     */
    fun generateCompatibilityReport(result: CompatibilityResult): String {
        val report = StringBuilder()
        
        report.appendLine("# 状态栏兼容性验证报告")
        report.appendLine()
        
        report.appendLine("## 设备信息")
        report.appendLine("- 设备型号: ${result.deviceInfo.brand} ${result.deviceInfo.deviceModel}")
        report.appendLine("- 制造商: ${result.deviceInfo.manufacturer}")
        report.appendLine("- Android版本: ${result.deviceInfo.androidVersion}")
        report.appendLine("- API级别: ${result.deviceInfo.apiLevel}")
        report.appendLine()
        
        report.appendLine("## 兼容性评估")
        report.appendLine("- 兼容性等级: ${getCompatibilityLevelDescription(result.compatibilityLevel)}")
        report.appendLine("- 整体兼容性: ${if (result.isCompatible) "✅ 兼容" else "❌ 不兼容"}")
        report.appendLine()
        
        if (result.deviceInfo.supportedFeatures.isNotEmpty()) {
            report.appendLine("## 支持的功能")
            result.deviceInfo.supportedFeatures.forEach { feature ->
                report.appendLine("- ✅ $feature")
            }
            report.appendLine()
        }
        
        if (result.deviceInfo.limitations.isNotEmpty()) {
            report.appendLine("## 限制和约束")
            result.deviceInfo.limitations.forEach { limitation ->
                report.appendLine("- ⚠️ $limitation")
            }
            report.appendLine()
        }
        
        if (result.issues.isNotEmpty()) {
            report.appendLine("## 识别的问题")
            result.issues.forEach { issue ->
                report.appendLine("- ❌ $issue")
            }
            report.appendLine()
        }
        
        if (result.solutions.isNotEmpty()) {
            report.appendLine("## 建议的解决方案")
            result.solutions.forEach { solution ->
                report.appendLine("- 💡 $solution")
            }
            report.appendLine()
        }
        
        if (result.deviceInfo.recommendations.isNotEmpty()) {
            report.appendLine("## 开发建议")
            result.deviceInfo.recommendations.forEach { recommendation ->
                report.appendLine("- 📋 $recommendation")
            }
            report.appendLine()
        }
        
        return report.toString()
    }

    /**
     * 获取兼容性等级描述
     */
    private fun getCompatibilityLevelDescription(level: CompatibilityLevel): String {
        return when (level) {
            CompatibilityLevel.FULL_SUPPORT -> "完全支持 - 所有功能均可正常使用"
            CompatibilityLevel.PARTIAL_SUPPORT -> "部分支持 - 大部分功能可用，少数功能受限"
            CompatibilityLevel.LIMITED_SUPPORT -> "有限支持 - 基础功能可用，高级功能不可用"
            CompatibilityLevel.NO_SUPPORT -> "不支持 - 无法使用状态栏自定义功能"
        }
    }
}