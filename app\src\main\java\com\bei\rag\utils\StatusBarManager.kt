package com.bei.rag.utils

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.bei.rag.R

/**
 * 状态栏样式管理工具类
 * 负责处理状态栏颜色、文字颜色、透明度等样式设置
 * 支持浅色/深色主题自适应和平滑过渡动画
 */
class StatusBarManager(private val activity: Activity) {

    companion object {
        private const val TRANSITION_DURATION = 300L
        private const val OVERLAY_ALPHA_LIGHT = 0.1f
        private const val OVERLAY_ALPHA_DARK = 0.0f
    }

    private val window: Window = activity.window
    private val context: Context = activity
    private val colorAdapter = StatusBarColorAdapter(context)
    private val compatManager = StatusBarCompatManager(activity)
    private val animationManager = StatusBarAnimationManager(activity)

    /**
     * 初始化状态栏设置
     * 根据当前主题自动配置状态栏样式
     */
    fun initialize() {
        compatManager.setEdgeToEdge()
        applyThemeBasedStatusBar()
    }

    /**
     * 根据当前主题应用状态栏样式
     */
    fun applyThemeBasedStatusBar() {
        val config = colorAdapter.getStatusBarConfig()
        
        // 应用背景色（带遮罩效果）
        val finalBackgroundColor = colorAdapter.applyOverlayToColor(config.backgroundColor, config.isDarkMode)
        setStatusBarColor(finalBackgroundColor, true)
        
        // 设置文字和图标颜色
        setLightStatusBarContent(config.useLightContent)
    }

    /**
     * 应用浅色主题状态栏样式
     * 使用深色文字和半透明背景遮罩
     */
    private fun applyLightModeStatusBar() {
        val themeManager = ThemeManager(context)
        val currentTheme = themeManager.getThemeColor()
        val backgroundColor = colorAdapter.getStatusBarBackgroundColor(currentTheme, false)
        val finalColor = colorAdapter.applyOverlayToColor(backgroundColor, false)
        
        setStatusBarColor(finalColor, true)
        setLightStatusBarContent(true) // 浅色背景使用深色内容
    }

    /**
     * 应用深色主题状态栏样式
     * 使用浅色文字和深色背景
     */
    private fun applyDarkModeStatusBar() {
        val themeManager = ThemeManager(context)
        val currentTheme = themeManager.getThemeColor()
        val backgroundColor = colorAdapter.getStatusBarBackgroundColor(currentTheme, true)
        
        setStatusBarColor(backgroundColor, true)
        setLightStatusBarContent(false) // 深色背景使用浅色内容
    }

    /**
     * 设置状态栏颜色
     * @param color 状态栏颜色
     * @param animated 是否使用动画过渡
     */
    fun setStatusBarColor(@ColorInt color: Int, animated: Boolean = true) {
        if (animated && compatManager.isStatusBarColorSupported()) {
            animateStatusBarColorChange(window.statusBarColor, color)
        } else {
            compatManager.setStatusBarColor(color)
        }
    }

    /**
     * 设置状态栏内容颜色（文字和图标）
     * @param lightContent true为深色内容（浅色背景），false为浅色内容（深色背景）
     */
    fun setLightStatusBarContent(lightContent: Boolean) {
        compatManager.setStatusBarContentColor(lightContent)
    }

    /**
     * 设置透明状态栏
     * @param fullyTransparent 是否完全透明
     */
    fun setTransparentStatusBar(fullyTransparent: Boolean = true) {
        if (fullyTransparent) {
            setStatusBarColor(Color.TRANSPARENT, true)
        } else {
            // 半透明效果
            val themeManager = ThemeManager(context)
            val themeColor = themeManager.getCurrentThemeColor()
            val semiTransparentColor = applyOverlay(themeColor, 0.3f)
            setStatusBarColor(semiTransparentColor, true)
        }
    }

    /**
     * 根据内容背景自动调整状态栏样式
     * @param backgroundColor 内容区域的背景颜色
     */
    fun autoAdjustForBackground(@ColorInt backgroundColor: Int) {
        val isLightBackground = isLightColor(backgroundColor)
        
        if (isLightBackground) {
            // 浅色背景使用深色内容
            setLightStatusBarContent(true)
            setStatusBarColor(applyOverlay(backgroundColor, 0.1f), true)
        } else {
            // 深色背景使用浅色内容
            setLightStatusBarContent(false)
            setStatusBarColor(backgroundColor, true)
        }
    }

    /**
     * 主题切换时的状态栏过渡动画
     * @param newThemeColorName 新主题颜色名称
     * @param onComplete 动画完成回调
     */
    fun animateThemeTransition(newThemeColorName: String, onComplete: (() -> Unit)? = null) {
        val themeManager = ThemeManager(context)
        val currentTheme = themeManager.getThemeColor()
        
        animationManager.animateThemeColorChange(currentTheme, newThemeColorName) {
            onComplete?.invoke()
        }
    }

    /**
     * 主题模式切换时的状态栏过渡动画（浅色/深色模式切换）
     * @param onComplete 动画完成回调
     */
    fun animateModeTransition(onComplete: (() -> Unit)? = null) {
        val themeManager = ThemeManager(context)
        val currentTheme = themeManager.getThemeColor()
        val currentIsDarkMode = themeManager.isDarkMode()
        val newIsDarkMode = isDarkMode()
        
        animationManager.animateModeSwitch(currentTheme, currentIsDarkMode, newIsDarkMode) {
            onComplete?.invoke()
        }
    }

    /**
     * 带弹性效果的主题切换动画
     * 适用于用户手动切换主题时的视觉反馈
     */
    fun animateThemeTransitionWithBounce(newThemeColorName: String, onComplete: (() -> Unit)? = null) {
        val themeManager = ThemeManager(context)
        val currentTheme = themeManager.getThemeColor()
        val isDarkMode = isDarkMode()
        
        val fromColor = colorAdapter.getStatusBarBackgroundColor(currentTheme, isDarkMode)
        val toColor = colorAdapter.getStatusBarBackgroundColor(newThemeColorName, isDarkMode)
        
        val fromFinalColor = colorAdapter.applyOverlayToColor(fromColor, isDarkMode)
        val toFinalColor = colorAdapter.applyOverlayToColor(toColor, isDarkMode)
        
        animationManager.animateWithBounceEffect(fromFinalColor, toFinalColor) {
            onComplete?.invoke()
        }
    }

    /**
     * 脉冲动画效果
     * 用于强调主题变化
     */
    fun animateThemeTransitionWithPulse(newThemeColorName: String, onComplete: (() -> Unit)? = null) {
        val themeManager = ThemeManager(context)
        val isDarkMode = isDarkMode()
        
        val baseColor = colorAdapter.getStatusBarBackgroundColor(newThemeColorName, isDarkMode)
        val highlightColor = colorAdapter.applyOverlayToColor(baseColor, isDarkMode)
        
        animationManager.animateWithPulseEffect(baseColor, highlightColor, 2) {
            onComplete?.invoke()
        }
    }

    /**
     * 状态栏颜色渐变动画
     */
    private fun animateStatusBarColorChange(@ColorInt fromColor: Int, @ColorInt toColor: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val colorAnimator = android.animation.ValueAnimator.ofArgb(fromColor, toColor)
            colorAnimator.duration = TRANSITION_DURATION
            colorAnimator.addUpdateListener { animator ->
                window.statusBarColor = animator.animatedValue as Int
            }
            colorAnimator.start()
        }
    }

    /**
     * 为颜色添加遮罩层
     * @param baseColor 基础颜色
     * @param alpha 遮罩透明度 (0.0-1.0)
     * @return 应用遮罩后的颜色
     */
    private fun applyOverlay(@ColorInt baseColor: Int, alpha: Float): Int {
        if (alpha <= 0f) return baseColor
        
        val overlayColor = if (isDarkMode()) Color.BLACK else Color.WHITE
        return blendColors(baseColor, overlayColor, alpha)
    }

    /**
     * 混合两种颜色
     */
    private fun blendColors(@ColorInt color1: Int, @ColorInt color2: Int, ratio: Float): Int {
        val inverseRatio = 1f - ratio
        val r = (Color.red(color1) * inverseRatio + Color.red(color2) * ratio).toInt()
        val g = (Color.green(color1) * inverseRatio + Color.green(color2) * ratio).toInt()
        val b = (Color.blue(color1) * inverseRatio + Color.blue(color2) * ratio).toInt()
        val a = (Color.alpha(color1) * inverseRatio + Color.alpha(color2) * ratio).toInt()
        return Color.argb(a, r, g, b)
    }

    /**
     * 判断是否为深色模式
     */
    private fun isDarkMode(): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * 判断颜色是否为浅色
     * 使用相对亮度计算
     */
    private fun isLightColor(@ColorInt color: Int): Boolean {
        val red = Color.red(color)
        val green = Color.green(color)
        val blue = Color.blue(color)
        
        // 计算相对亮度 (基于 WCAG 标准)
        val luminance = (0.299 * red + 0.587 * green + 0.114 * blue) / 255
        return luminance > 0.5
    }

    /**
     * 获取主题相关的状态栏颜色资源
     */
    fun getThemeStatusBarColor(): Int {
        return try {
            ContextCompat.getColor(context, R.attr.colorHeader)
        } catch (e: Exception) {
            Color.TRANSPARENT
        }
    }

    /**
     * 重置状态栏为默认样式
     */
    fun resetToDefault() {
        if (isStatusBarCustomizationSupported()) {
            applyThemeBasedStatusBar()
        } else {
            // 对于不支持自定义的旧版本，重置到系统默认
            compatManager.resetToSystemDefault()
        }
    }

    /**
     * 设置沉浸式状态栏模式
     */
    fun setImmersiveMode() {
        compatManager.setImmersiveStatusBar()
    }

    /**
     * 退出沉浸式模式，显示状态栏
     */
    fun exitImmersiveMode() {
        compatManager.showStatusBar()
        applyThemeBasedStatusBar()
    }

    /**
     * 取消所有正在运行的动画
     */
    fun cancelAnimations() {
        animationManager.cancelAllAnimations()
    }

    /**
     * 检查是否有动画正在运行
     */
    fun isAnimating(): Boolean {
        return animationManager.isAnimating()
    }

    /**
     * 立即完成所有动画
     */
    fun finishAnimations() {
        animationManager.finishAllAnimations()
    }

    /**
     * 设置动画监听器
     */
    fun setAnimationListener(listener: StatusBarAnimationManager.AnimationListener) {
        animationManager.setAnimationListener(listener)
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        animationManager.cleanup()
    }

    /**
     * 为特定页面设置自定义状态栏样式
     * @param backgroundColor 页面背景色
     * @param forceTransparent 是否强制透明
     */
    fun setCustomStatusBarForPage(@ColorInt backgroundColor: Int, forceTransparent: Boolean = false) {
        if (forceTransparent) {
            setTransparentStatusBar(true)
            setLightStatusBarContent(isLightColor(backgroundColor))
        } else {
            autoAdjustForBackground(backgroundColor)
        }
    }

    /**
     * 获取当前状态栏高度
     */
    fun getStatusBarHeight(): Int {
        return compatManager.getStatusBarHeight()
    }

    /**
     * 检查当前设备是否支持状态栏自定义
     */
    fun isStatusBarCustomizationSupported(): Boolean {
        return compatManager.isStatusBarColorSupported()
    }

    /**
     * 检查是否支持状态栏文字颜色控制
     */
    fun isStatusBarTextColorSupported(): Boolean {
        return compatManager.isStatusBarTextColorSupported()
    }

    /**
     * 获取当前设备的状态栏处理策略
     */
    fun getStatusBarStrategy(): StatusBarCompatManager.StatusBarStrategy {
        return compatManager.getStatusBarStrategy()
    }
}