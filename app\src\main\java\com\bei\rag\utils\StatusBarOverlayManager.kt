package com.bei.rag.utils

import android.app.Activity
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.annotation.ColorInt
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

/**
 * 状态栏半透明遮罩管理器
 * 专门处理状态栏区域的半透明遮罩效果
 */
class StatusBarOverlayManager(private val activity: Activity) {

    companion object {
        private const val OVERLAY_TAG = "status_bar_overlay"
        private const val LIGHT_OVERLAY_ALPHA = 0.15f
        private const val DARK_OVERLAY_ALPHA = 0.08f
    }

    private val statusBarManager = StatusBarManager(activity)
    private val colorAdapter = StatusBarColorAdapter(activity)
    private var currentOverlayView: View? = null

    /**
     * 创建状态栏半透明遮罩
     * @param rootView 根视图容器
     * @param overlayColor 遮罩颜色
     * @param alpha 透明度
     */
    fun createStatusBarOverlay(
        rootView: ViewGroup,
        @ColorInt overlayColor: Int = Color.BLACK,
        alpha: Float = LIGHT_OVERLAY_ALPHA
    ) {
        // 移除现有遮罩
        removeStatusBarOverlay(rootView)

        val statusBarHeight = statusBarManager.getStatusBarHeight()
        if (statusBarHeight <= 0) return

        // 创建遮罩视图
        val overlayView = View(activity).apply {
            tag = OVERLAY_TAG
            setBackgroundColor(Color.argb((alpha * 255).toInt(), 
                Color.red(overlayColor), 
                Color.green(overlayColor), 
                Color.blue(overlayColor)))
        }

        // 设置布局参数
        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            statusBarHeight
        ).apply {
            topMargin = 0
        }

        // 添加到根视图
        rootView.addView(overlayView, 0, layoutParams)
        currentOverlayView = overlayView

        // 确保遮罩在最顶层
        overlayView.elevation = 100f
    }

    /**
     * 创建渐变遮罩效果
     * 从顶部状态栏区域向下渐变
     */
    fun createGradientOverlay(
        rootView: ViewGroup,
        @ColorInt startColor: Int,
        @ColorInt endColor: Int = Color.TRANSPARENT
    ) {
        removeStatusBarOverlay(rootView)

        val statusBarHeight = statusBarManager.getStatusBarHeight()
        if (statusBarHeight <= 0) return

        val overlayView = View(activity).apply {
            tag = OVERLAY_TAG
            background = createGradientDrawable(startColor, endColor)
        }

        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            statusBarHeight * 2 // 渐变区域稍大一些
        )

        rootView.addView(overlayView, 0, layoutParams)
        currentOverlayView = overlayView
        overlayView.elevation = 100f
    }

    /**
     * 创建渐变Drawable
     */
    private fun createGradientDrawable(@ColorInt startColor: Int, @ColorInt endColor: Int): android.graphics.drawable.GradientDrawable {
        return android.graphics.drawable.GradientDrawable(
            android.graphics.drawable.GradientDrawable.Orientation.TOP_BOTTOM,
            intArrayOf(startColor, endColor)
        )
    }

    /**
     * 根据当前主题自动创建合适的遮罩
     */
    fun createThemeBasedOverlay(rootView: ViewGroup) {
        val config = colorAdapter.getStatusBarConfig()
        
        if (config.isDarkMode) {
            // 深色模式：使用轻微的白色遮罩或无遮罩
            createStatusBarOverlay(rootView, Color.WHITE, DARK_OVERLAY_ALPHA)
        } else {
            // 浅色模式：使用黑色半透明遮罩增强对比度
            createStatusBarOverlay(rootView, Color.BLACK, LIGHT_OVERLAY_ALPHA)
        }
    }

    /**
     * 为聊天界面创建特殊遮罩
     * 根据聊天背景动态调整
     */
    fun createChatOverlay(rootView: ViewGroup, @ColorInt chatBackgroundColor: Int) {
        val needsDarkText = colorAdapter.needsDarkText(chatBackgroundColor)
        
        if (needsDarkText) {
            // 浅色背景，需要深色文字，添加轻微黑色遮罩
            createStatusBarOverlay(rootView, Color.BLACK, 0.1f)
        } else {
            // 深色背景，需要浅色文字，添加轻微白色遮罩
            createStatusBarOverlay(rootView, Color.WHITE, 0.05f)
        }
    }

    /**
     * 创建毛玻璃效果遮罩
     * 适用于需要模糊效果的场景
     */
    fun createBlurOverlay(rootView: ViewGroup, @ColorInt baseColor: Int) {
        removeStatusBarOverlay(rootView)

        val statusBarHeight = statusBarManager.getStatusBarHeight()
        if (statusBarHeight <= 0) return

        val overlayView = View(activity).apply {
            tag = OVERLAY_TAG
            setBackgroundColor(Color.argb(40, 
                Color.red(baseColor), 
                Color.green(baseColor), 
                Color.blue(baseColor)))
            
            // 添加模糊效果（API 31+）
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                setRenderEffect(android.graphics.RenderEffect.createBlurEffect(
                    10f, 10f, android.graphics.Shader.TileMode.CLAMP))
            }
        }

        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            statusBarHeight
        )

        rootView.addView(overlayView, 0, layoutParams)
        currentOverlayView = overlayView
        overlayView.elevation = 100f
    }

    /**
     * 移除状态栏遮罩
     */
    fun removeStatusBarOverlay(rootView: ViewGroup) {
        currentOverlayView?.let { overlay ->
            rootView.removeView(overlay)
            currentOverlayView = null
        }

        // 也尝试通过tag查找并移除
        val existingOverlay = rootView.findViewWithTag<View>(OVERLAY_TAG)
        existingOverlay?.let { rootView.removeView(it) }
    }

    /**
     * 更新遮罩颜色（带动画）
     */
    fun updateOverlayColor(
        @ColorInt newColor: Int, 
        alpha: Float = LIGHT_OVERLAY_ALPHA,
        animated: Boolean = true
    ) {
        currentOverlayView?.let { overlay ->
            val targetColor = Color.argb((alpha * 255).toInt(), 
                Color.red(newColor), 
                Color.green(newColor), 
                Color.blue(newColor))

            if (animated) {
                animateColorChange(overlay, targetColor)
            } else {
                overlay.setBackgroundColor(targetColor)
            }
        }
    }

    /**
     * 遮罩颜色变化动画
     */
    private fun animateColorChange(view: View, @ColorInt targetColor: Int) {
        val currentDrawable = view.background
        if (currentDrawable is android.graphics.drawable.ColorDrawable) {
            val currentColor = currentDrawable.color
            val colorAnimator = android.animation.ValueAnimator.ofArgb(currentColor, targetColor)
            colorAnimator.duration = 300L
            colorAnimator.addUpdateListener { animator ->
                view.setBackgroundColor(animator.animatedValue as Int)
            }
            colorAnimator.start()
        } else {
            view.setBackgroundColor(targetColor)
        }
    }

    /**
     * 主题切换时更新遮罩
     */
    fun onThemeChanged(rootView: ViewGroup) {
        if (currentOverlayView != null) {
            // 重新创建基于主题的遮罩
            createThemeBasedOverlay(rootView)
        }
    }

    /**
     * 为DrawerLayout创建特殊遮罩
     * 确保侧滑菜单区域的遮罩正确显示
     */
    fun createDrawerOverlay(drawerLayout: androidx.drawerlayout.widget.DrawerLayout) {
        val mainContent = drawerLayout.getChildAt(0) as? ViewGroup ?: return
        createThemeBasedOverlay(mainContent)
    }

    /**
     * 检查是否已有遮罩
     */
    fun hasOverlay(): Boolean {
        return currentOverlayView != null
    }

    /**
     * 获取当前遮罩视图
     */
    fun getCurrentOverlay(): View? {
        return currentOverlayView
    }

    /**
     * 设置遮罩的可见性
     */
    fun setOverlayVisibility(visible: Boolean) {
        currentOverlayView?.visibility = if (visible) View.VISIBLE else View.GONE
    }

    /**
     * 为全屏模式创建遮罩
     * 适用于图片查看、视频播放等场景
     */
    fun createFullscreenOverlay(rootView: ViewGroup, @ColorInt overlayColor: Int) {
        createStatusBarOverlay(rootView, overlayColor, 0.2f)
    }

    /**
     * 清理资源
     */
    fun cleanup(rootView: ViewGroup) {
        removeStatusBarOverlay(rootView)
        currentOverlayView = null
    }
}