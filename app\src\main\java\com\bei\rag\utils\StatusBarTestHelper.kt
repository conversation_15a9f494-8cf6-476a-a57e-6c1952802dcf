package com.bei.rag.utils

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.bei.rag.MainActivity
import com.bei.rag.R

/**
 * 状态栏测试辅助工具
 * 用于测试和验证状态栏在各种场景下的显示效果
 */
class StatusBarTestHelper(private val context: Context) {

    companion object {
        private const val TAG = "StatusBarTest"
    }

    private val themeManager = ThemeManager(context)

    /**
     * 测试结果数据类
     */
    data class TestResult(
        val testName: String,
        val success: Boolean,
        val message: String,
        val details: Map<String, Any> = emptyMap()
    )

    /**
     * 执行完整的状态栏测试套件
     */
    fun runFullTestSuite(activity: Activity): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        Log.d(TAG, "开始执行状态栏测试套件")
        
        // 基础功能测试
        results.addAll(testBasicFunctionality(activity))
        
        // 主题切换测试
        results.addAll(testThemeSwitching(activity))
        
        // 兼容性测试
        results.addAll(testCompatibility(activity))
        
        // 动画测试
        results.addAll(testAnimations(activity))
        
        // 遮罩测试
        results.addAll(testOverlays(activity))
        
        // 设备兼容性验证
        results.addAll(testDeviceCompatibility(activity))
        
        Log.d(TAG, "状态栏测试套件完成，共执行 ${results.size} 项测试")
        logTestSummary(results)
        
        return results
    }

    /**
     * 测试基础功能
     */
    private fun testBasicFunctionality(activity: Activity): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        try {
            val statusBarManager = (activity as? MainActivity)?.getStatusBarManager()
            
            if (statusBarManager == null) {
                results.add(TestResult(
                    "状态栏管理器初始化",
                    false,
                    "无法获取状态栏管理器实例"
                ))
                return results
            }
            
            // 测试状态栏高度获取
            val statusBarHeight = statusBarManager.getStatusBarHeight()
            results.add(TestResult(
                "状态栏高度获取",
                statusBarHeight > 0,
                if (statusBarHeight > 0) "状态栏高度: ${statusBarHeight}px" else "无法获取状态栏高度",
                mapOf("height" to statusBarHeight)
            ))
            
            // 测试兼容性检查
            val colorSupported = statusBarManager.isStatusBarCustomizationSupported()
            results.add(TestResult(
                "状态栏颜色自定义支持",
                true,
                "状态栏颜色自定义支持: $colorSupported",
                mapOf("supported" to colorSupported)
            ))
            
            val textColorSupported = statusBarManager.isStatusBarTextColorSupported()
            results.add(TestResult(
                "状态栏文字颜色控制支持",
                true,
                "状态栏文字颜色控制支持: $textColorSupported",
                mapOf("supported" to textColorSupported)
            ))
            
            // 测试策略获取
            val strategy = statusBarManager.getStatusBarStrategy()
            results.add(TestResult(
                "状态栏处理策略",
                true,
                "当前策略: $strategy",
                mapOf("strategy" to strategy.name)
            ))
            
        } catch (e: Exception) {
            results.add(TestResult(
                "基础功能测试异常",
                false,
                "测试过程中发生异常: ${e.message}"
            ))
        }
        
        return results
    }

    /**
     * 测试主题切换功能
     */
    private fun testThemeSwitching(activity: Activity): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        try {
            val statusBarManager = (activity as? MainActivity)?.getStatusBarManager()
                ?: return listOf(TestResult("主题切换测试", false, "无法获取状态栏管理器"))
            
            val originalTheme = themeManager.getThemeColor()
            
            // 测试不同主题颜色
            val testColors = listOf(
                ThemeManager.THEME_COLOR_BLUE,
                ThemeManager.THEME_COLOR_GREEN,
                ThemeManager.THEME_COLOR_RED,
                ThemeManager.THEME_COLOR_PURPLE
            )
            
            testColors.forEach { color ->
                try {
                    themeManager.setThemeColor(color)
                    statusBarManager.applyThemeBasedStatusBar()
                    
                    results.add(TestResult(
                        "主题颜色切换 - $color",
                        true,
                        "成功切换到 ${themeManager.getThemeColorName(color)} 主题",
                        mapOf("color" to color)
                    ))
                } catch (e: Exception) {
                    results.add(TestResult(
                        "主题颜色切换 - $color",
                        false,
                        "切换失败: ${e.message}"
                    ))
                }
            }
            
            // 恢复原始主题
            themeManager.setThemeColor(originalTheme)
            statusBarManager.applyThemeBasedStatusBar()
            
        } catch (e: Exception) {
            results.add(TestResult(
                "主题切换测试异常",
                false,
                "测试过程中发生异常: ${e.message}"
            ))
        }
        
        return results
    }

    /**
     * 测试版本兼容性
     */
    private fun testCompatibility(activity: Activity): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        try {
            val compatManager = StatusBarCompatManager(activity)
            
            // 测试API支持情况
            results.add(TestResult(
                "状态栏颜色API支持",
                true,
                "支持状态: ${compatManager.isStatusBarColorSupported()}",
                mapOf("supported" to compatManager.isStatusBarColorSupported())
            ))
            
            results.add(TestResult(
                "状态栏文字颜色API支持",
                true,
                "支持状态: ${compatManager.isStatusBarTextColorSupported()}",
                mapOf("supported" to compatManager.isStatusBarTextColorSupported())
            ))
            
            results.add(TestResult(
                "新版WindowInsetsController支持",
                true,
                "支持状态: ${compatManager.isNewInsetsControllerSupported()}",
                mapOf("supported" to compatManager.isNewInsetsControllerSupported())
            ))
            
            // 测试不同策略的应用
            val strategy = compatManager.getStatusBarStrategy()
            try {
                compatManager.applyStyleByStrategy(strategy, Color.BLUE, true)
                results.add(TestResult(
                    "兼容性策略应用",
                    true,
                    "成功应用 $strategy 策略"
                ))
            } catch (e: Exception) {
                results.add(TestResult(
                    "兼容性策略应用",
                    false,
                    "策略应用失败: ${e.message}"
                ))
            }
            
        } catch (e: Exception) {
            results.add(TestResult(
                "兼容性测试异常",
                false,
                "测试过程中发生异常: ${e.message}"
            ))
        }
        
        return results
    }

    /**
     * 测试动画功能
     */
    private fun testAnimations(activity: Activity): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        try {
            val statusBarManager = (activity as? MainActivity)?.getStatusBarManager()
                ?: return listOf(TestResult("动画测试", false, "无法获取状态栏管理器"))
            
            // 测试动画状态检查
            val isAnimating = statusBarManager.isAnimating()
            results.add(TestResult(
                "动画状态检查",
                true,
                "当前动画状态: $isAnimating",
                mapOf("animating" to isAnimating)
            ))
            
            // 测试动画取消功能
            try {
                statusBarManager.cancelAnimations()
                results.add(TestResult(
                    "动画取消功能",
                    true,
                    "动画取消功能正常"
                ))
            } catch (e: Exception) {
                results.add(TestResult(
                    "动画取消功能",
                    false,
                    "动画取消失败: ${e.message}"
                ))
            }
            
            // 测试主题切换动画
            try {
                var animationCompleted = false
                statusBarManager.animateThemeTransition(ThemeManager.THEME_COLOR_GREEN) {
                    animationCompleted = true
                }
                
                // 等待一小段时间检查动画是否开始
                Thread.sleep(100)
                val wasAnimating = statusBarManager.isAnimating()
                
                results.add(TestResult(
                    "主题切换动画",
                    true,
                    "动画启动成功，动画状态: $wasAnimating",
                    mapOf("started" to wasAnimating)
                ))
                
                // 取消动画以避免影响后续测试
                statusBarManager.cancelAnimations()
                
            } catch (e: Exception) {
                results.add(TestResult(
                    "主题切换动画",
                    false,
                    "动画测试失败: ${e.message}"
                ))
            }
            
        } catch (e: Exception) {
            results.add(TestResult(
                "动画测试异常",
                false,
                "测试过程中发生异常: ${e.message}"
            ))
        }
        
        return results
    }

    /**
     * 测试遮罩功能
     */
    private fun testOverlays(activity: Activity): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        try {
            val overlayManager = StatusBarOverlayManager(activity)
            val rootView = activity.findViewById<ViewGroup>(android.R.id.content)
            
            if (rootView == null) {
                results.add(TestResult(
                    "遮罩测试",
                    false,
                    "无法获取根视图"
                ))
                return results
            }
            
            // 测试基础遮罩创建
            try {
                overlayManager.createThemeBasedOverlay(rootView)
                val hasOverlay = overlayManager.hasOverlay()
                
                results.add(TestResult(
                    "基础遮罩创建",
                    hasOverlay,
                    if (hasOverlay) "遮罩创建成功" else "遮罩创建失败",
                    mapOf("hasOverlay" to hasOverlay)
                ))
                
                // 清理遮罩
                overlayManager.removeStatusBarOverlay(rootView)
                
            } catch (e: Exception) {
                results.add(TestResult(
                    "基础遮罩创建",
                    false,
                    "遮罩创建失败: ${e.message}"
                ))
            }
            
            // 测试渐变遮罩
            try {
                overlayManager.createGradientOverlay(rootView, Color.BLUE, Color.TRANSPARENT)
                val hasGradientOverlay = overlayManager.hasOverlay()
                
                results.add(TestResult(
                    "渐变遮罩创建",
                    hasGradientOverlay,
                    if (hasGradientOverlay) "渐变遮罩创建成功" else "渐变遮罩创建失败"
                ))
                
                // 清理遮罩
                overlayManager.cleanup(rootView)
                
            } catch (e: Exception) {
                results.add(TestResult(
                    "渐变遮罩创建",
                    false,
                    "渐变遮罩创建失败: ${e.message}"
                ))
            }
            
        } catch (e: Exception) {
            results.add(TestResult(
                "遮罩测试异常",
                false,
                "测试过程中发生异常: ${e.message}"
            ))
        }
        
        return results
    }

    /**
     * 测试Fragment中的状态栏适配
     */
    fun testFragmentStatusBar(fragment: Fragment, rootView: ViewGroup): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        try {
            // 测试聊天界面状态栏设置
            StatusBarHelper.setupChatStatusBar(fragment, rootView)
            results.add(TestResult(
                "聊天界面状态栏设置",
                true,
                "聊天界面状态栏设置成功"
            ))
            
            // 测试状态栏内边距应用
            val paddingTop = rootView.paddingTop
            results.add(TestResult(
                "状态栏内边距应用",
                paddingTop > 0,
                "顶部内边距: ${paddingTop}px",
                mapOf("paddingTop" to paddingTop)
            ))
            
        } catch (e: Exception) {
            results.add(TestResult(
                "Fragment状态栏测试异常",
                false,
                "测试过程中发生异常: ${e.message}"
            ))
        }
        
        return results
    }

    /**
     * 记录测试摘要
     */
    private fun logTestSummary(results: List<TestResult>) {
        val successCount = results.count { it.success }
        val failureCount = results.size - successCount
        
        Log.i(TAG, "=== 状态栏测试摘要 ===")
        Log.i(TAG, "总测试数: ${results.size}")
        Log.i(TAG, "成功: $successCount")
        Log.i(TAG, "失败: $failureCount")
        Log.i(TAG, "成功率: ${(successCount.toFloat() / results.size * 100).toInt()}%")
        
        if (failureCount > 0) {
            Log.w(TAG, "失败的测试:")
            results.filter { !it.success }.forEach { result ->
                Log.w(TAG, "- ${result.testName}: ${result.message}")
            }
        }
        
        Log.i(TAG, "==================")
    }

    /**
     * 生成测试报告
     */
    fun generateTestReport(results: List<TestResult>): String {
        val report = StringBuilder()
        report.appendLine("# 状态栏UI优化测试报告")
        report.appendLine()
        
        val successCount = results.count { it.success }
        val failureCount = results.size - successCount
        
        report.appendLine("## 测试概览")
        report.appendLine("- 总测试数: ${results.size}")
        report.appendLine("- 成功: $successCount")
        report.appendLine("- 失败: $failureCount")
        report.appendLine("- 成功率: ${(successCount.toFloat() / results.size * 100).toInt()}%")
        report.appendLine()
        
        report.appendLine("## 详细结果")
        results.forEach { result ->
            val status = if (result.success) "✅" else "❌"
            report.appendLine("### $status ${result.testName}")
            report.appendLine("**结果**: ${result.message}")
            
            if (result.details.isNotEmpty()) {
                report.appendLine("**详细信息**:")
                result.details.forEach { (key, value) ->
                    report.appendLine("- $key: $value")
                }
            }
            report.appendLine()
        }
        
        return report.toString()
    }

    /**
     * 测试设备兼容性
     */
    private fun testDeviceCompatibility(activity: Activity): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        try {
            val validator = StatusBarCompatibilityValidator(activity)
            val compatibilityResult = validator.validateCompatibility(activity)
            
            results.add(TestResult(
                "设备兼容性验证",
                compatibilityResult.isCompatible,
                "兼容性等级: ${compatibilityResult.compatibilityLevel}",
                mapOf(
                    "device" to "${compatibilityResult.deviceInfo.brand} ${compatibilityResult.deviceInfo.deviceModel}",
                    "android" to compatibilityResult.deviceInfo.androidVersion,
                    "api" to compatibilityResult.deviceInfo.apiLevel,
                    "level" to compatibilityResult.compatibilityLevel.name
                )
            ))
            
            // 测试支持的功能
            results.add(TestResult(
                "功能支持检查",
                compatibilityResult.deviceInfo.supportedFeatures.isNotEmpty(),
                "支持 ${compatibilityResult.deviceInfo.supportedFeatures.size} 项功能",
                mapOf("features" to compatibilityResult.deviceInfo.supportedFeatures)
            ))
            
            // 测试限制识别
            results.add(TestResult(
                "限制识别",
                true,
                "识别到 ${compatibilityResult.deviceInfo.limitations.size} 项限制",
                mapOf("limitations" to compatibilityResult.deviceInfo.limitations)
            ))
            
        } catch (e: Exception) {
            results.add(TestResult(
                "设备兼容性验证异常",
                false,
                "验证过程中发生异常: ${e.message}"
            ))
        }
        
        return results
    }

    /**
     * 快速健康检查
     */
    fun quickHealthCheck(activity: Activity): Boolean {
        return try {
            val statusBarManager = (activity as? MainActivity)?.getStatusBarManager()
            val statusBarHeight = statusBarManager?.getStatusBarHeight() ?: 0
            val isSupported = statusBarManager?.isStatusBarCustomizationSupported() ?: false
            
            // 同时进行兼容性快速检查
            val validator = StatusBarCompatibilityValidator(activity)
            val compatibilityResult = validator.validateCompatibility(activity)
            
            statusBarHeight > 0 && 
            (isSupported || android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.LOLLIPOP) &&
            compatibilityResult.isCompatible
        } catch (e: Exception) {
            Log.e(TAG, "健康检查失败", e)
            false
        }
    }

    /**
     * 生成完整的兼容性报告
     */
    fun generateCompatibilityReport(activity: Activity): String {
        return try {
            val validator = StatusBarCompatibilityValidator(activity)
            val compatibilityResult = validator.validateCompatibility(activity)
            validator.generateCompatibilityReport(compatibilityResult)
        } catch (e: Exception) {
            "兼容性报告生成失败: ${e.message}"
        }
    }
}