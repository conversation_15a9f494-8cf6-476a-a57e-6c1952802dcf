package com.bei.rag.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.os.Build;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.view.WindowManager;

/**
 * 状态栏工具类
 * 提供获取状态栏高度和设置状态栏相关功能
 */
public class StatusBarUtil {
    private static final String TAG = "StatusBarUtil";
    
    /**
     * 获取状态栏高度
     * @param context 上下文
     * @return 状态栏高度（像素）
     */
    public static int getStatusBarHeight(Context context) {
        int statusBarHeight = 0;
        try {
            // 尝试通过系统资源获取
            int resourceId = context.getResources().getIdentifier(
                    "status_bar_height", "dimen", "android");
            if (resourceId > 0) {
                statusBarHeight = context.getResources().getDimensionPixelSize(resourceId);
            }
            
            // 如果获取失败，使用默认值
            if (statusBarHeight == 0) {
                statusBarHeight = dpToPx(context, 25);
            }
        } catch (Exception e) {
            Log.e(TAG, "获取状态栏高度失败", e);
            statusBarHeight = dpToPx(context, 25);
        }
        return statusBarHeight;
    }
    
    /**
     * 设置状态栏颜色
     * @param activity 活动
     * @param color 颜色值
     */
    public static void setStatusBarColor(Activity activity, int color) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            activity.getWindow().setStatusBarColor(color);
        }
    }
    
    /**
     * 设置状态栏为亮色模式（状态栏文字图标为深色）
     * @param activity 活动
     */
    public static void setLightStatusBar(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            View decorView = activity.getWindow().getDecorView();
            int flags = decorView.getSystemUiVisibility();
            flags |= View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
            decorView.setSystemUiVisibility(flags);
        }
    }
    
    /**
     * 设置状态栏为暗色模式（状态栏文字图标为浅色）
     * @param activity 活动
     */
    public static void setDarkStatusBar(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            View decorView = activity.getWindow().getDecorView();
            int flags = decorView.getSystemUiVisibility();
            flags &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
            decorView.setSystemUiVisibility(flags);
        }
    }
    
    /**
     * 设置全屏模式（隐藏状态栏）
     * @param activity 活动
     */
    public static void setFullScreen(Activity activity) {
        activity.getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
    }
    
    /**
     * 设置沉浸式状态栏（内容延伸到状态栏）
     * @param activity 活动
     */
    public static void setImmersiveStatusBar(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            View decorView = activity.getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            activity.getWindow().setStatusBarColor(0x00000000);
        }
    }
    
    /**
     * 为给定的视图添加状态栏高度的上内边距
     * @param context 上下文
     * @param view 需要添加内边距的视图
     */
    public static void addStatusBarPadding(Context context, View view) {
        if (view != null) {
            int statusBarHeight = getStatusBarHeight(context);
            view.setPadding(
                    view.getPaddingLeft(),
                    view.getPaddingTop() + statusBarHeight,
                    view.getPaddingRight(),
                    view.getPaddingBottom()
            );
        }
    }
    
    /**
     * 为给定的视图添加状态栏高度的上外边距
     * @param context 上下文
     * @param view 需要添加外边距的视图
     */
    public static void addStatusBarMargin(Context context, View view) {
        if (view != null) {
            int statusBarHeight = getStatusBarHeight(context);
            ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
            params.topMargin += statusBarHeight;
            view.setLayoutParams(params);
        }
    }
    
    /**
     * 将dp转换为像素
     * @param context 上下文
     * @param dp dp值
     * @return 像素值
     */
    private static int dpToPx(Context context, float dp) {
        float density = context.getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }
}