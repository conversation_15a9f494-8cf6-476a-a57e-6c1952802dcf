package com.bei.rag.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.appcompat.app.AppCompatDelegate
import com.bei.rag.R

class ThemeManager(private val context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences("theme_prefs", Context.MODE_PRIVATE)
    
    companion object {
        const val THEME_MODE_KEY = "theme_mode"
        const val THEME_COLOR_KEY = "theme_color"
        
        const val THEME_MODE_SYSTEM = 0
        const val THEME_MODE_LIGHT = 1
        const val THEME_MODE_DARK = 2
        
        const val THEME_COLOR_BLUE = "blue"
        const val THEME_COLOR_GREEN = "green"
        const val THEME_COLOR_RED = "red"
        const val THEME_COLOR_PURPLE = "purple"
        const val THEME_COLOR_ORANGE = "orange"
        const val THEME_COLOR_PINK = "pink"
        const val THEME_COLOR_GRAY = "gray"
    }
    
    fun setThemeMode(mode: Int) {
        prefs.edit().putInt(THEME_MODE_KEY, mode).apply()
        applyThemeMode(mode)
    }
    
    fun getThemeMode(): Int {
        return prefs.getInt(THEME_MODE_KEY, THEME_MODE_SYSTEM)
    }
    
    fun setThemeColor(color: String) {
        prefs.edit().putString(THEME_COLOR_KEY, color).apply()
    }
    
    fun getThemeColor(): String {
        return prefs.getString(THEME_COLOR_KEY, THEME_COLOR_GRAY) ?: THEME_COLOR_GRAY
    }
    
    fun applyThemeMode(mode: Int) {
        when (mode) {
            THEME_MODE_SYSTEM -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
            THEME_MODE_LIGHT -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            THEME_MODE_DARK -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
        }
    }
    
    fun getThemeColorName(color: String): String {
        return when (color) {
            THEME_COLOR_BLUE -> "蓝色"
            THEME_COLOR_GREEN -> "绿色"
            THEME_COLOR_RED -> "红色"
            THEME_COLOR_PURPLE -> "紫色"
            THEME_COLOR_ORANGE -> "橙色"
            THEME_COLOR_PINK -> "粉色"
            THEME_COLOR_GRAY -> "灰色"
            else -> "灰色"
        }
    }
    
    fun getThemeColorResource(color: String): Int {
        return when (color) {
            THEME_COLOR_BLUE -> R.color.ios_blue
            THEME_COLOR_GREEN -> R.color.theme_green
            THEME_COLOR_RED -> R.color.theme_red
            THEME_COLOR_PURPLE -> R.color.theme_purple
            THEME_COLOR_ORANGE -> R.color.theme_orange
            THEME_COLOR_PINK -> R.color.theme_pink
            THEME_COLOR_GRAY -> R.color.theme_gray
            else -> R.color.theme_gray
        }
    }

    fun getThemeStyleResource(color: String): Int {
        return when (color) {
            THEME_COLOR_BLUE -> R.style.Theme_Ragandroid_Blue
            THEME_COLOR_GREEN -> R.style.Theme_Ragandroid_Green
            THEME_COLOR_RED -> R.style.Theme_Ragandroid_Red
            THEME_COLOR_PURPLE -> R.style.Theme_Ragandroid_Purple
            THEME_COLOR_ORANGE -> R.style.Theme_Ragandroid_Orange
            THEME_COLOR_PINK -> R.style.Theme_Ragandroid_Pink
            THEME_COLOR_GRAY -> R.style.Theme_Ragandroid_Gray
            else -> R.style.Theme_Ragandroid_Gray
        }
    }
    
    fun getThemeModeName(mode: Int): String {
        return when (mode) {
            THEME_MODE_SYSTEM -> "跟随系统"
            THEME_MODE_LIGHT -> "浅色模式"
            THEME_MODE_DARK -> "深色模式"
            else -> "跟随系统"
        }
    }
    
    fun initializeTheme() {
        val mode = getThemeMode()
        applyThemeMode(mode)
    }

    fun applyThemeColor(activity: android.app.Activity) {
        val color = getThemeColor()
        val themeRes = getThemeStyleResource(color)
        activity.setTheme(themeRes)
    }

    /**
     * 获取当前主题颜色的实际颜色值
     */
    fun getCurrentThemeColor(): Int {
        val colorName = getThemeColor()
        val colorRes = getThemeColorResource(colorName)
        return context.getColor(colorRes)
    }

    /**
     * 主题切换时通知状态栏更新
     * @param animated 是否使用动画过渡
     * @param onComplete 动画完成回调
     */
    fun onThemeChanged(activity: android.app.Activity, animated: Boolean = true, onComplete: (() -> Unit)? = null) {
        // 应用新主题
        applyThemeColor(activity)
        
        // 通知MainActivity更新状态栏
        if (activity is com.bei.rag.MainActivity) {
            activity.onThemeChanged(animated, onComplete)
        } else {
            onComplete?.invoke()
        }
    }

    /**
     * 主题颜色切换时通知状态栏更新
     * @param newColor 新主题颜色
     * @param animated 是否使用动画过渡
     * @param onComplete 动画完成回调
     */
    fun onThemeColorChanged(activity: android.app.Activity, newColor: String, animated: Boolean = true, onComplete: (() -> Unit)? = null) {
        // 更新主题颜色
        setThemeColor(newColor)
        applyThemeColor(activity)
        
        // 通知MainActivity更新状态栏
        if (activity is com.bei.rag.MainActivity) {
            activity.onThemeColorChanged(newColor, animated, onComplete)
        } else {
            onComplete?.invoke()
        }
    }

    /**
     * 检查当前是否为深色模式
     */
    fun isDarkMode(): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and 
            android.content.res.Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == android.content.res.Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * 获取适合状态栏的颜色（考虑深浅模式）
     */
    fun getStatusBarColor(): Int {
        val baseColor = getCurrentThemeColor()
        return if (isDarkMode()) {
            // 深色模式下使用原色或稍微调暗
            adjustColorBrightness(baseColor, 0.8f)
        } else {
            // 浅色模式下使用原色
            baseColor
        }
    }

    /**
     * 调整颜色亮度
     */
    private fun adjustColorBrightness(color: Int, factor: Float): Int {
        val red = (android.graphics.Color.red(color) * factor).toInt().coerceIn(0, 255)
        val green = (android.graphics.Color.green(color) * factor).toInt().coerceIn(0, 255)
        val blue = (android.graphics.Color.blue(color) * factor).toInt().coerceIn(0, 255)
        return android.graphics.Color.rgb(red, green, blue)
    }
}
