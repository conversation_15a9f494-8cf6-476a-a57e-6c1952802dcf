package com.bei.rag.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * TTS配置管理类
 * 负责管理TTS引擎配置和用户偏好设置
 */
class TtsConfig(context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    companion object {
        private const val PREFS_NAME = "tts_config"
        private const val KEY_ENGINE_TYPE = "engine_type"
        private const val KEY_SPEECH_RATE = "speech_rate"
        private const val KEY_PITCH = "pitch"
        private const val KEY_LANGUAGE = "language"
        private const val KEY_AUTO_PLAY_AI_REPLY = "auto_play_ai_reply"
        
        // 默认值
        private const val DEFAULT_SPEECH_RATE = 1.0f
        private const val DEFAULT_PITCH = 1.0f
        private const val DEFAULT_LANGUAGE = "zh-CN"
        private const val DEFAULT_AUTO_PLAY_AI_REPLY = false
    }

    /**
     * 获取当前选择的TTS引擎类型
     */
    fun getEngineType(): TtsEngineType {
        val engineName = sharedPreferences.getString(KEY_ENGINE_TYPE, null)
        return if (engineName != null) {
            TtsEngineType.fromString(engineName)
        } else {
            TtsEngineType.getDefault()
        }
    }

    /**
     * 设置TTS引擎类型
     */
    fun setEngineType(engineType: TtsEngineType) {
        sharedPreferences.edit()
            .putString(KEY_ENGINE_TYPE, engineType.name)
            .apply()
    }

    /**
     * 获取语速设置
     */
    fun getSpeechRate(): Float {
        return sharedPreferences.getFloat(KEY_SPEECH_RATE, DEFAULT_SPEECH_RATE)
    }

    /**
     * 设置语速
     */
    fun setSpeechRate(rate: Float) {
        sharedPreferences.edit()
            .putFloat(KEY_SPEECH_RATE, rate)
            .apply()
    }

    /**
     * 获取音调设置
     */
    fun getPitch(): Float {
        return sharedPreferences.getFloat(KEY_PITCH, DEFAULT_PITCH)
    }

    /**
     * 设置音调
     */
    fun setPitch(pitch: Float) {
        sharedPreferences.edit()
            .putFloat(KEY_PITCH, pitch)
            .apply()
    }

    /**
     * 获取语言设置
     */
    fun getLanguage(): String {
        return sharedPreferences.getString(KEY_LANGUAGE, DEFAULT_LANGUAGE) ?: DEFAULT_LANGUAGE
    }

    /**
     * 设置语言
     */
    fun setLanguage(language: String) {
        sharedPreferences.edit()
            .putString(KEY_LANGUAGE, language)
            .apply()
    }

    /**
     * 获取AI回复自动播放设置
     */
    fun getAutoPlayAiReply(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_PLAY_AI_REPLY, DEFAULT_AUTO_PLAY_AI_REPLY)
    }

    /**
     * 设置AI回复自动播放
     */
    fun setAutoPlayAiReply(autoPlay: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_AUTO_PLAY_AI_REPLY, autoPlay)
            .apply()
    }

    /**
     * 重置为默认设置
     */
    fun resetToDefault() {
        sharedPreferences.edit()
            .putString(KEY_ENGINE_TYPE, TtsEngineType.getDefault().name)
            .putFloat(KEY_SPEECH_RATE, DEFAULT_SPEECH_RATE)
            .putFloat(KEY_PITCH, DEFAULT_PITCH)
            .putString(KEY_LANGUAGE, DEFAULT_LANGUAGE)
            .putBoolean(KEY_AUTO_PLAY_AI_REPLY, DEFAULT_AUTO_PLAY_AI_REPLY)
            .apply()
    }
} 