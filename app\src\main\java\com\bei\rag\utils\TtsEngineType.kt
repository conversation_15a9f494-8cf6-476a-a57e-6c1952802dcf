package com.bei.rag.utils

/**
 * TTS引擎类型枚举
 */
enum class TtsEngineType(
    val displayName: String,
    val description: String,
    val isOffline: Boolean
) {
    ANDROID_TTS(
        displayName = "系统语音引擎",
        description = "使用设备内置的文字转语音引擎",
        isOffline = true
    ),
    GEMINI_TTS(
        displayName = "Gemini语音引擎",
        description = "使用Google Gemini在线语音合成",
        isOffline = false
    );

    companion object {
        /**
         * 从字符串获取引擎类型
         */
        fun fromString(value: String): TtsEngineType {
            return values().find { it.name == value } ?: ANDROID_TTS
        }

        /**
         * 获取默认引擎
         */
        fun getDefault(): TtsEngineType = ANDROID_TTS
    }
} 