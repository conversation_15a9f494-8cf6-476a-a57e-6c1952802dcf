<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="?attr/colorBorder" />
            <stroke
                android:width="1dp"
                android:color="?attr/colorThemePrimary" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <solid android:color="?attr/colorCardBackground" />
            <stroke
                android:width="1dp"
                android:color="?attr/colorBorder" />
        </shape>
    </item>
</selector>
