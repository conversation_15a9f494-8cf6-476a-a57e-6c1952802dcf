<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    style="@style/AppPageRootLayout"
    tools:context=".ChatScreen">

    <!-- 头部导航栏 -->
    <LinearLayout
        android:id="@+id/header_layout"
        style="@style/AppHeaderLayout">

        <LinearLayout
            style="@style/AppHeaderContent">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                android:contentDescription="返回"
                app:tint="@color/white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="智慧笔记"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center" />

            <ImageButton
                android:id="@+id/btn_menu"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_more_vert"
                android:contentDescription="菜单"
                app:tint="@color/white" />

        </LinearLayout>

    </LinearLayout>

    <!-- 聊天消息区域 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_chat_messages"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/chat_background"
        android:clipToPadding="false"
        android:paddingTop="8dp"
        android:paddingBottom="8dp" />

    <!-- 输入区域 -->
    <LinearLayout
        android:id="@+id/input_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:elevation="8dp">

        <ImageButton
            android:id="@+id/btn_voice"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/bg_voice_button"
            android:src="@drawable/ic_mic"
            android:contentDescription="语音输入"
            app:tint="@color/ios_text_secondary"
            android:layout_marginEnd="8dp"
            android:elevation="2dp" />

        <EditText
            android:id="@+id/et_message_input"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:hint="输入你的问题..."
            android:background="@drawable/edit_text_background"
            android:paddingHorizontal="20dp"
            android:paddingVertical="12dp"
            android:textSize="16sp"
            android:textColor="@color/text_dark"
            android:textColorHint="@color/text_gray"
            android:maxLines="3"
            android:inputType="textMultiLine|textCapSentences"
            android:layout_marginEnd="8dp" />

        <ImageButton
            android:id="@+id/btn_send"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/bg_send_button"
            android:src="@drawable/ic_send"
            android:contentDescription="发送"
            app:tint="@color/white"
            android:elevation="2dp" />

    </LinearLayout>

</LinearLayout>
