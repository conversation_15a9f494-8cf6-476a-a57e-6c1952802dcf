<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="?attr/colorAppBackground">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="状态栏UI优化测试工具"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="?attr/colorTextPrimary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 测试按钮区域 -->
    <!-- 测试按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp">

        <!-- 第一行按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btn_health_check"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="快速检查"
                android:layout_marginEnd="4dp"
                android:backgroundTint="?attr/colorThemePrimary"
                android:textColor="@color/white" />

            <Button
                android:id="@+id/btn_run_tests"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="完整测试"
                android:layout_marginHorizontal="4dp"
                android:backgroundTint="?attr/colorThemePrimary"
                android:textColor="@color/white" />

        </LinearLayout>

        <!-- 第二行按钮 -->
        <Button
            android:id="@+id/btn_compatibility_report"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="兼容性报告"
            android:backgroundTint="?attr/colorAccent"
            android:textColor="@color/white" />

    </LinearLayout>

    <!-- 测试结果显示区域 -->
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="?attr/colorCardBackground"
        android:padding="12dp"
        android:scrollbars="vertical">

        <TextView
            android:id="@+id/tv_test_results"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="点击上方按钮开始测试..."
            android:textColor="?attr/colorTextPrimary"
            android:textSize="14sp"
            android:fontFamily="monospace"
            android:lineSpacingExtra="2dp" />

    </ScrollView>

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="此工具用于测试状态栏UI优化功能的各项特性，包括颜色适配、动画效果、版本兼容性等。"
        android:textSize="12sp"
        android:textColor="?attr/colorTextSecondary"
        android:layout_marginTop="12dp"
        android:gravity="center" />

</LinearLayout>