<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="创建分组"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/ios_text_primary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 分组名称输入 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入分组名称"
        android:layout_marginBottom="16dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_group_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:imeOptions="actionDone" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 分组描述输入 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="分组描述（可选）"
        android:layout_marginBottom="24dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_group_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:maxLines="3"
            android:imeOptions="actionDone" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="分组功能支持对话分类管理，并通过专属指令定制回复，使交流更专注、个性化且持续发展。"
        android:textSize="14sp"
        android:textColor="@color/ios_text_secondary"
        android:lineSpacingExtra="4dp"
        android:layout_marginBottom="24dp" />

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/btn_cancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:textColor="@color/ios_text_secondary"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_create"
            style="@style/Widget.Material3.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="确认创建"
            android:backgroundTint="@color/ios_blue" />

    </LinearLayout>

</LinearLayout>
