<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 昵称输入 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="昵称"
        android:textSize="16sp"
        android:textColor="@color/ios_text_primary"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/et_nickname"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/ios_input_background"
        android:hint="请输入昵称"
        android:textColorHint="@color/ios_text_secondary"
        android:textColor="@color/ios_text_primary"
        android:textSize="16sp"
        android:paddingHorizontal="16dp"
        android:inputType="textPersonName"
        android:layout_marginBottom="16dp" />

    <!-- 邮箱输入 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="邮箱"
        android:textSize="16sp"
        android:textColor="@color/ios_text_primary"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/et_email"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/ios_input_background"
        android:hint="请输入邮箱地址"
        android:textColorHint="@color/ios_text_secondary"
        android:textColor="@color/ios_text_primary"
        android:textSize="16sp"
        android:paddingHorizontal="16dp"
        android:inputType="textEmailAddress" />

</LinearLayout>
