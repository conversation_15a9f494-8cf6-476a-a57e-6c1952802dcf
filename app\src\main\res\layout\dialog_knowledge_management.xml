<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="知识库管理"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/ios_text_primary"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <!-- 副标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="上传和管理您的文档，让AI助手基于您的内容回答问题"
        android:textSize="16sp"
        android:textColor="@color/ios_text_secondary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 上传区域 -->
    <LinearLayout
        android:id="@+id/upload_area"
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/ios_upload_area"
        android:orientation="vertical"
        android:gravity="center"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground">

        <ImageView
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:src="@drawable/ic_upload"
            android:layout_marginBottom="12dp"
            app:tint="@color/ios_blue" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="点击上传文档"
            android:textSize="18sp"
            android:textColor="@color/ios_text_primary"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="支持 PDF、Word、CSV、TXT 格式"
            android:textSize="14sp"
            android:textColor="@color/ios_text_secondary"
            android:layout_marginTop="4dp" />

    </LinearLayout>

    <!-- 快速统计 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/ios_card_background"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_document_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/ios_blue" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="文档数量"
                android:textSize="14sp"
                android:textColor="@color/ios_text_secondary" />

        </LinearLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/ios_separator" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_total_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0 MB"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/ios_green" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="总大小"
                android:textSize="14sp"
                android:textColor="@color/ios_text_secondary" />

        </LinearLayout>

    </LinearLayout>

    <!-- 功能按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="关闭"
            android:textColor="@color/ios_text_secondary"
            android:textSize="17sp"
            android:background="@drawable/ios_button_secondary"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="管理文档"
            android:textColor="@color/ios_text_white"
            android:textSize="17sp"
            android:background="@drawable/ios_button_primary"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
