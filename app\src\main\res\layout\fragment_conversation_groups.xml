<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/AppPageRootLayout">

    <!-- 标题栏 -->
    <LinearLayout style="@style/AppHeaderLayout">

        <LinearLayout style="@style/AppHeaderContent">

            <!-- 返回按钮 -->
            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                app:tint="@color/white"
                android:contentDescription="返回"
                android:padding="12dp" />

            <!-- 标题 -->
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="会话分组"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center" />

            <!-- 新建分组按钮 -->
            <ImageButton
                android:id="@+id/btn_add_group"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_add"
                app:tint="@color/white"
                android:contentDescription="新建分组"
                android:padding="12dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 默认分组 -->
    <LinearLayout
        android:id="@+id/layout_default_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/ios_card_background"
        android:padding="16dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:gravity="center_vertical"
        android:clickable="true"
        android:focusable="true">

        <!-- 分组图标 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_chat"
            app:tint="@color/ios_blue"
            android:layout_marginEnd="12dp" />

        <!-- 分组信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="默认分组"
                android:textColor="@color/ios_text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_default_group_count"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0 个会话"
                android:textColor="@color/ios_text_secondary"
                android:textSize="14sp"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- 箭头 -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_right"
            app:tint="@color/ios_text_secondary" />

    </LinearLayout>

    <!-- 分组列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_groups"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:paddingTop="8dp"
        android:paddingBottom="8dp" />

</LinearLayout>