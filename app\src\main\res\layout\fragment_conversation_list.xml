<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/AppPageRootLayout">

    <!-- 头部导航栏 -->
    <LinearLayout style="@style/AppHeaderLayout">
        <LinearLayout style="@style/AppHeaderContent">

            <!-- 返回按钮 -->
            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                app:tint="@color/white"
                android:contentDescription="返回"
                android:padding="12dp" />

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="会话列表"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center" />

            <!-- 新建会话按钮 -->
            <ImageButton
                android:id="@+id/btn_new_chat"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_add"
                app:tint="@color/white"
                android:contentDescription="新建会话"
                android:padding="12dp" />

        </LinearLayout>
    </LinearLayout>

    <!-- 会话列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_conversations"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="?attr/colorAppBackground"
        android:clipToPadding="false"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:overScrollMode="ifContentScrolls" />

</LinearLayout>
