<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/AppPageRootLayout"
    android:orientation="vertical">

    <!-- 标题栏 - 使用统一样式 -->
    <LinearLayout style="@style/AppHeaderLayout">
        <LinearLayout style="@style/AppHeaderContent">

            <!-- 返回按钮 -->
            <ImageButton
                android:id="@+id/btn_back"
                style="@style/AppBackButton" />

            <!-- 标题 -->
            <TextView
                style="@style/AppHeaderTitle"
                android:text="知识库" />

            <!-- 占位空间，保持标题居中 -->
            <View style="@style/AppHeaderSpacer" />

        </LinearLayout>
    </LinearLayout>

    <!-- 内容区域 - 优化滚动体验 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:overScrollMode="ifContentScrolls">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 上传区域卡片 - 现代化设计 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="?attr/colorCardBackground"
                android:elevation="2dp"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="上传文档以扩充您的知识库"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextSecondary"
                    android:gravity="center"
                    android:layout_marginBottom="20dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <Button
                        android:id="@+id/btn_upload"
                        android:layout_width="wrap_content"
                        android:layout_height="52dp"
                        android:text="📤 选择文件上传"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:background="@drawable/ios_button_primary"
                        android:paddingHorizontal="24dp"
                        android:layout_marginEnd="12dp"
                        android:elevation="2dp" />

                    <Button
                        android:id="@+id/btn_process_documents"
                        android:layout_width="wrap_content"
                        android:layout_height="52dp"
                        android:text="🔄 批量处理"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:background="@drawable/ios_button_secondary"
                        android:paddingHorizontal="24dp"
                        android:layout_marginStart="12dp"
                        android:elevation="2dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- 处理状态区域 - 现代化设计 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="?attr/colorCardBackground"
                android:elevation="2dp"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/tv_processing_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="就绪"
                    android:textSize="14sp"
                    android:textColor="?attr/colorTextSecondary"
                    android:gravity="center"
                    android:layout_marginBottom="12dp" />

                <ProgressBar
                    android:id="@+id/pb_processing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:indeterminate="true" />

            </LinearLayout>

            <!-- 已上传文档标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="已上传文档"
                android:textSize="20sp"
                android:textColor="?attr/colorTextPrimary"
                android:textStyle="bold"
                android:layout_marginBottom="16dp"
                android:paddingHorizontal="4dp" />

            <!-- 文档列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_documents"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false"
                android:overScrollMode="ifContentScrolls" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
