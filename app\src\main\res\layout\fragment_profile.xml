<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/AppPageRootLayout"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 页面标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="我的"
            android:textColor="@color/ios_text_primary"
            android:textSize="32sp"
            android:textStyle="bold"
            android:layout_marginBottom="24dp" />

        <!-- 个人信息卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/ios_card_background"
            android:padding="24dp"
            android:gravity="center"
            android:layout_marginBottom="24dp">

            <!-- 头像 -->
            <LinearLayout
                android:layout_width="96dp"
                android:layout_height="96dp"
                android:background="@drawable/circle_background"
                android:gravity="center"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_profile"
                    app:tint="@color/ios_text_white" />

            </LinearLayout>

            <!-- 昵称 -->
            <TextView
                android:id="@+id/tv_nickname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="张三"
                android:textColor="@color/ios_text_primary"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginBottom="4dp" />

            <!-- 邮箱 -->
            <TextView
                android:id="@+id/tv_email"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="<EMAIL>"
                android:textColor="@color/ios_text_secondary"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- 设置选项卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/ios_card_background"
            android:layout_marginBottom="16dp">

            <!-- 编辑个人资料 -->
            <LinearLayout
                android:id="@+id/layout_edit_profile"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_edit"
                    app:tint="@color/ios_blue"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="编辑个人资料"
                    android:textSize="17sp"
                    android:textColor="@color/ios_text_primary" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="@color/ios_text_secondary" />

            </LinearLayout>

                <!-- 分割线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="?attr/colorBorder"
                    android:layout_marginStart="60dp"
                    android:alpha="0.5" />

            <!-- 通知设置 -->
            <LinearLayout
                android:id="@+id/layout_notifications"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_notifications"
                    app:tint="@color/ios_blue"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="通知设置"
                    android:textSize="17sp"
                    android:textColor="@color/ios_text_primary" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="@color/ios_text_secondary" />

            </LinearLayout>

                <!-- 分割线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="?attr/colorBorder"
                    android:layout_marginStart="60dp"
                    android:alpha="0.5" />

                <!-- 清除数据 -->
                <LinearLayout
                    android:id="@+id/layout_clear_data"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="20dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_delete"
                        app:tint="@color/theme_red"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="清除数据"
                        android:textSize="17sp"
                        android:textColor="?attr/colorTextPrimary" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="?attr/colorTextSecondary" />

                </LinearLayout>

                <!-- 分割线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="?attr/colorBorder"
                    android:layout_marginStart="60dp"
                    android:alpha="0.5" />

                <!-- 关于我们 -->
                <LinearLayout
                    android:id="@+id/layout_about"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="20dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_info"
                        app:tint="?attr/colorThemePrimary"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="关于我们"
                        android:textSize="17sp"
                        android:textColor="?attr/colorTextPrimary" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="?attr/colorTextSecondary" />

                </LinearLayout>

        </LinearLayout>

        <!-- 退出登录按钮 - 现代化设计 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/theme_red">

            <Button
                android:id="@+id/btn_logout"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="退出登录"
                android:textColor="@color/white"
                android:textSize="17sp"
                android:textStyle="bold"
                android:background="@android:color/transparent" />

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
