<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/AppPageRootLayout"
    android:orientation="vertical">

    <!-- 标题栏 - 使用统一样式 -->
    <LinearLayout style="@style/AppHeaderLayout">
        <LinearLayout style="@style/AppHeaderContent">

            <!-- 返回按钮 -->
            <ImageButton
                android:id="@+id/btn_back"
                style="@style/AppBackButton" />

            <!-- 标题 -->
            <TextView
                style="@style/AppHeaderTitle"
                android:text="系统设置" />

            <!-- 占位 -->
            <View style="@style/AppHeaderSpacer" />

        </LinearLayout>
    </LinearLayout>

    <!-- 设置列表 -->
    <ScrollView style="@style/AppScrollView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingVertical="16dp">

            <!-- 系统设置标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="系统设置"
                android:textSize="14sp"
                android:textColor="@color/ios_text_secondary"
                android:textStyle="bold"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp" />

            <!-- 接收消息通知 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="@color/white">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="接收消息通知"
                    android:textSize="16sp"
                    android:textColor="@color/text_dark" />

                <Switch
                    android:id="@+id/switch_notifications"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

            </LinearLayout>

            <!-- 个性化设置标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="个性化设置"
                android:textSize="14sp"
                android:textColor="@color/ios_text_secondary"
                android:textStyle="bold"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:layout_marginTop="16dp" />

            <!-- 允许个性化推荐 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:background="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="允许个性化推荐"
                        android:textSize="16sp"
                        android:textColor="@color/text_dark" />

                    <Switch
                        android:id="@+id/switch_personalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />

                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="开启后，将基于您的浏览记录进行个性化推荐"
                    android:textSize="12sp"
                    android:textColor="@color/ios_text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <!-- 清除缓存 -->
            <LinearLayout
                android:id="@+id/setting_clear_cache"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground"
                android:layout_marginTop="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="清除缓存"
                    android:textSize="16sp"
                    android:textColor="@color/text_dark" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_right"
                    android:tint="@color/ios_text_secondary" />

            </LinearLayout>

            <!-- 当前版本 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:layout_marginTop="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="当前版本"
                    android:textSize="16sp"
                    android:textColor="@color/text_dark" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2.18.0"
                    android:textSize="14sp"
                    android:textColor="@color/ios_text_secondary" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>