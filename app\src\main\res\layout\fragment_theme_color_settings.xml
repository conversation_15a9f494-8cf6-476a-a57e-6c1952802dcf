<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/AppPageRootLayout">

    <!-- 标题栏 -->
    <LinearLayout
        style="@style/AppHeaderLayout">

        <LinearLayout
            style="@style/AppHeaderContent">

            <!-- 返回按钮 -->
            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                android:tint="@color/white"
                android:contentDescription="返回"
                android:padding="12dp" />

            <!-- 标题 -->
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="主题色设置"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center" />

            <!-- 占位 -->
            <View
                android:layout_width="48dp"
                android:layout_height="48dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 颜色选择列表 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingVertical="16dp">

            <!-- 蓝色 -->
            <LinearLayout
                android:id="@+id/color_blue"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <View
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/color_circle"
                    android:backgroundTint="@color/ios_blue"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="蓝色"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/check_blue"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check"
                    android:tint="@color/ios_blue"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 绿色 -->
            <LinearLayout
                android:id="@+id/color_green"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <View
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/color_circle"
                    android:backgroundTint="@color/theme_green"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="绿色"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/check_green"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check"
                    android:tint="@color/theme_green"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 红色 -->
            <LinearLayout
                android:id="@+id/color_red"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <View
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/color_circle"
                    android:backgroundTint="@color/theme_red"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="红色"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/check_red"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check"
                    android:tint="@color/theme_red"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 紫色 -->
            <LinearLayout
                android:id="@+id/color_purple"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <View
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/color_circle"
                    android:backgroundTint="@color/theme_purple"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="紫色"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/check_purple"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check"
                    android:tint="@color/theme_purple"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 橙色 -->
            <LinearLayout
                android:id="@+id/color_orange"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <View
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/color_circle"
                    android:backgroundTint="@color/theme_orange"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="橙色"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/check_orange"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check"
                    android:tint="@color/theme_orange"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 粉色 -->
            <LinearLayout
                android:id="@+id/color_pink"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <View
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/color_circle"
                    android:backgroundTint="@color/theme_pink"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="粉色"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/check_pink"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check"
                    android:tint="@color/theme_pink"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 灰色 -->
            <LinearLayout
                android:id="@+id/color_gray"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <View
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/color_circle"
                    android:backgroundTint="@color/theme_gray"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="灰色"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary" />

                <ImageView
                    android:id="@+id/check_gray"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check"
                    android:tint="@color/theme_gray"
                    android:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
