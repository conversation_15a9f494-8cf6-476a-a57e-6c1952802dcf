<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/AppPageRootLayout"
    android:orientation="vertical">

    <!-- 头部导航栏 -->
    <LinearLayout style="@style/AppHeaderLayout">
        <LinearLayout style="@style/AppHeaderContent">

            <!-- 返回按钮 -->
            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                app:tint="@color/white"
                android:contentDescription="返回"
                android:padding="12dp" />

            <!-- 标题 -->
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="语音引擎设置"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center" />

            <!-- 占位空间，保持标题居中 -->
            <View
                android:layout_width="48dp"
                android:layout_height="48dp" />

        </LinearLayout>
    </LinearLayout>

    <!-- 内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:overScrollMode="ifContentScrolls">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 语音识别设置卡片 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="?attr/colorCardBackground"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:elevation="2dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="语音识别"
                    android:textSize="18sp"
                    android:textColor="?attr/colorTextPrimary"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- 语音识别引擎选择 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="识别引擎"
                        android:textSize="16sp"
                        android:textColor="?attr/colorTextPrimary" />

                    <Spinner
                        android:id="@+id/spinner_speech_engine"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="120dp" />

                </LinearLayout>

                <!-- 语音识别语言 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="识别语言"
                        android:textSize="16sp"
                        android:textColor="?attr/colorTextPrimary" />

                    <Spinner
                        android:id="@+id/spinner_speech_language"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="120dp" />

                </LinearLayout>

                <!-- 语音识别灵敏度 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="12dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="识别灵敏度"
                            android:textSize="16sp"
                            android:textColor="?attr/colorTextPrimary" />

                        <TextView
                            android:id="@+id/tv_sensitivity_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="中等"
                            android:textSize="14sp"
                            android:textColor="?attr/colorTextSecondary" />

                    </LinearLayout>

                    <SeekBar
                        android:id="@+id/seekbar_sensitivity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:max="100"
                        android:progress="50" />

                </LinearLayout>

            </LinearLayout>

            <!-- 语音合成设置卡片 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="?attr/colorCardBackground"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:elevation="2dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="语音合成"
                    android:textSize="18sp"
                    android:textColor="?attr/colorTextPrimary"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- TTS引擎选择 -->
                <LinearLayout
                    android:id="@+id/layout_engine_selection"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?attr/selectableItemBackground"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="合成引擎"
                        android:textSize="16sp"
                        android:textColor="?attr/colorTextPrimary" />

                    <TextView
                        android:id="@+id/text_current_engine"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="系统TTS"
                        android:textSize="14sp"
                        android:textColor="?attr/colorTextSecondary"
                        android:layout_marginEnd="8dp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="?attr/colorTextSecondary" />

                </LinearLayout>

                <!-- 语音选择 -->
                <LinearLayout
                    android:id="@+id/layout_language"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?attr/selectableItemBackground"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="语言选择"
                        android:textSize="16sp"
                        android:textColor="?attr/colorTextPrimary" />

                    <TextView
                        android:id="@+id/text_current_language"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="中文（简体）"
                        android:textSize="14sp"
                        android:textColor="?attr/colorTextSecondary"
                        android:layout_marginEnd="8dp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="?attr/colorTextSecondary" />

                </LinearLayout>

                <!-- 语速调节 -->
                <LinearLayout
                    android:id="@+id/layout_speech_rate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="12dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="语速"
                            android:textSize="16sp"
                            android:textColor="?attr/colorTextPrimary" />

                        <TextView
                            android:id="@+id/text_speech_rate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1.0x"
                            android:textSize="14sp"
                            android:textColor="?attr/colorTextSecondary" />

                    </LinearLayout>

                    <SeekBar
                        android:id="@+id/seekbar_speech_rate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:max="200"
                        android:progress="100" />

                </LinearLayout>

                <!-- 音调调节 -->
                <LinearLayout
                    android:id="@+id/layout_pitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="12dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="音调"
                            android:textSize="16sp"
                            android:textColor="?attr/colorTextPrimary" />

                        <TextView
                            android:id="@+id/text_pitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1.0x"
                            android:textSize="14sp"
                            android:textColor="?attr/colorTextSecondary" />

                    </LinearLayout>

                    <SeekBar
                        android:id="@+id/seekbar_pitch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:max="200"
                        android:progress="100" />

                </LinearLayout>

                <!-- 自动播放AI回复 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="自动播放AI回复"
                        android:textSize="16sp"
                        android:textColor="?attr/colorTextPrimary" />

                    <Switch
                        android:id="@+id/switch_auto_play"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </LinearLayout>

            <!-- 测试区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="?attr/colorCardBackground"
                android:padding="20dp"
                android:elevation="2dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试语音"
                    android:textSize="18sp"
                    android:textColor="?attr/colorTextPrimary"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <EditText
                    android:id="@+id/et_test_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="输入测试文本..."
                    android:text="你好，这是语音合成测试。"
                    android:background="@drawable/ios_input_background"
                    android:padding="16dp"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary"
                    android:textColorHint="?attr/colorTextSecondary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <Button
                        android:id="@+id/btn_test_voice"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="🔊 测试语音"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:background="@drawable/ios_button_primary"
                        android:layout_marginEnd="8dp"
                        android:elevation="2dp" />

                    <Button
                        android:id="@+id/btn_reset_settings"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="🔄 重置设置"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:background="@drawable/ios_button_secondary"
                        android:layout_marginStart="8dp"
                        android:elevation="2dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>