<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@drawable/ios_card_background"
    android:padding="16dp"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="4dp"
    android:gravity="center_vertical">

    <!-- 会话图标 -->
    <ImageView
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/ic_chat"
        android:tint="@color/ios_blue"
        android:layout_marginEnd="12dp" />

    <!-- 会话信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 会话标题 -->
        <TextView
            android:id="@+id/tv_conversation_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="会话标题"
            android:textColor="@color/ios_text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 最后一条消息 -->
        <TextView
            android:id="@+id/tv_last_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="最后一条消息内容..."
            android:textColor="@color/ios_text_secondary"
            android:textSize="14sp"
            android:layout_marginTop="4dp"
            android:maxLines="2"
            android:ellipsize="end" />

    </LinearLayout>

    <!-- 右侧信息 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="end"
        android:layout_marginStart="8dp">

        <!-- 时间 -->
        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="12:34"
            android:textColor="@color/ios_text_secondary"
            android:textSize="12sp" />

        <!-- 消息数量 -->
        <TextView
            android:id="@+id/tv_message_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="5"
            android:textColor="@color/ios_text_secondary"
            android:textSize="12sp"
            android:layout_marginTop="4dp"
            android:background="@drawable/ios_badge_background"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:minWidth="20dp"
            android:gravity="center" />

    </LinearLayout>

    <!-- 删除按钮 -->
    <ImageButton
        android:id="@+id/btn_delete"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_delete"
        android:contentDescription="删除会话"
        android:tint="@color/ios_red"
        android:layout_marginStart="8dp"
        android:padding="4dp" />

</LinearLayout>
