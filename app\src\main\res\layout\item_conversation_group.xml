<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@drawable/ios_card_background"
    android:padding="16dp"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="4dp"
    android:gravity="center_vertical">

    <!-- 分组图标 -->
    <ImageView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_chat"
        android:tint="@color/ios_blue"
        android:layout_marginEnd="12dp" />

    <!-- 分组信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 分组名称 -->
        <TextView
            android:id="@+id/tv_group_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="分组名称"
            android:textColor="@color/ios_text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 会话数量 -->
        <TextView
            android:id="@+id/tv_conversation_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="0 个会话"
            android:textColor="@color/ios_text_secondary"
            android:textSize="14sp"
            android:layout_marginTop="4dp" />

    </LinearLayout>

    <!-- 更多操作按钮 -->
    <ImageButton
        android:id="@+id/btn_more"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_more_vert"
        android:contentDescription="更多操作"
        android:tint="@color/ios_text_secondary"
        android:layout_marginStart="8dp"
        android:padding="4dp" />

    <!-- 箭头 -->
    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_arrow_right"
        android:tint="@color/ios_text_secondary"
        android:layout_marginStart="8dp" />

</LinearLayout>
