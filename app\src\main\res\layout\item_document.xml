<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="?attr/colorCardBackground"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="20dp">

        <!-- 文件图标 -->
        <ImageView
            android:id="@+id/iv_file_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_file_pdf"
            android:layout_marginEnd="16dp"
            android:layout_gravity="center_vertical" />

        <!-- 文件信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_gravity="center_vertical">

            <!-- 文件名 -->
            <TextView
                android:id="@+id/tv_file_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="RAG技术详细应用.pdf"
                android:textSize="17sp"
                android:textColor="?attr/colorTextPrimary"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- 文件大小和上传时间 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="6dp">

                <TextView
                    android:id="@+id/tv_file_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2.1 MB"
                    android:textSize="14sp"
                    android:textColor="?attr/colorTextSecondary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" • "
                    android:textSize="14sp"
                    android:textColor="?attr/colorTextSecondary" />

                <TextView
                    android:id="@+id/tv_upload_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2024-04-15 10:30"
                    android:textSize="14sp"
                    android:textColor="?attr/colorTextSecondary" />

            </LinearLayout>

            <!-- 处理状态 -->
            <TextView
                android:id="@+id/tv_processing_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="待处理"
                android:textSize="12sp"
                android:textColor="?attr/colorTextSecondary"
                android:layout_marginTop="8dp"
                android:background="@drawable/bg_source_pill"
                android:paddingHorizontal="10dp"
                android:paddingVertical="4dp" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="center_vertical">

            <!-- 处理按钮 -->
            <ImageButton
                android:id="@+id/btn_process"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:src="@drawable/ic_refresh"
                app:tint="?attr/colorThemePrimary"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="处理文档"
                android:layout_marginEnd="8dp"
                android:visibility="gone" />

            <!-- 删除按钮 -->
            <ImageButton
                android:id="@+id/btn_delete"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:src="@drawable/ic_delete"
                app:tint="@color/theme_red"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="删除文档" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
