<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?attr/colorAppBackground"
    android:paddingHorizontal="20dp"
    android:paddingVertical="16dp"
    android:animateLayoutChanges="true">

    <!-- AI回复内容区域 - 现代卡片风格 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="4dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="?attr/colorCardBackground">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:id="@+id/tv_message_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorTextPrimary"
                android:textSize="16sp"
                android:lineSpacingExtra="6dp"
                android:text="AI助手回复内容" />

            <TextView
                android:id="@+id/tv_sources"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorTextSecondary"
                android:textSize="12sp"
                android:layout_marginTop="12dp"
                android:background="@drawable/bg_source_pill"
                android:paddingHorizontal="12dp"
                android:paddingVertical="6dp"
                android:visibility="gone"
                android:text="来源: 文档A, 文档B" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 底部操作区域 - 现代化风格 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginTop="8dp"
        android:paddingVertical="12dp">

        <!-- 左侧时间 -->
        <TextView
            android:id="@+id/tv_message_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="?attr/colorTextSecondary"
            android:textSize="12sp"
            android:text="12:34" />

        <!-- 右侧操作按钮组 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="24dp"
            app:cardElevation="1dp"
            app:cardBackgroundColor="?attr/colorCardBackground">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="6dp"
                android:paddingVertical="4dp">

                <!-- 刷新按钮 -->
                <ImageButton
                    android:id="@+id/btn_refresh"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/bg_operation_button"
                    android:src="@drawable/ic_refresh"
                    app:tint="?attr/colorTextSecondary"
                    android:contentDescription="重新生成"
                    android:padding="10dp"
                    android:layout_margin="2dp" />

                <!-- 复制按钮 -->
                <ImageButton
                    android:id="@+id/btn_copy"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/bg_operation_button"
                    android:src="@drawable/ic_copy"
                    app:tint="?attr/colorTextSecondary"
                    android:contentDescription="复制"
                    android:padding="10dp"
                    android:layout_margin="2dp" />

                <!-- 分享按钮 -->
                <ImageButton
                    android:id="@+id/btn_share"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/bg_operation_button"
                    android:src="@drawable/ic_share"
                    app:tint="?attr/colorTextSecondary"
                    android:contentDescription="分享"
                    android:padding="10dp"
                    android:layout_margin="2dp" />

                <!-- 音频播放按钮 -->
                <ImageButton
                    android:id="@+id/btn_audio"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/bg_operation_button"
                    android:src="@drawable/ic_volume_up"
                    app:tint="?attr/colorTextSecondary"
                    android:contentDescription="播放音频"
                    android:padding="10dp"
                    android:layout_margin="2dp" />

                <!-- 更多操作按钮 -->
                <ImageButton
                    android:id="@+id/btn_more"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/bg_operation_button"
                    android:src="@drawable/ic_more_horiz"
                    app:tint="?attr/colorTextSecondary"
                    android:contentDescription="更多"
                    android:padding="10dp"
                    android:layout_margin="2dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <!-- 底部分割线 - 更细更柔和 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="?attr/colorBorder"
        android:layout_marginTop="12dp"
        android:alpha="0.3" />

</LinearLayout>
