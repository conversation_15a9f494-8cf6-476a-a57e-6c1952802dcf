<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="end"
    android:paddingHorizontal="20dp"
    android:paddingVertical="12dp"
    android:animateLayoutChanges="true">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginStart="80dp"
        android:gravity="end">

        <!-- 用户消息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="3dp"
            app:cardBackgroundColor="?attr/colorUserMessageBg">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="18dp"
                android:paddingVertical="14dp"
                android:minWidth="60dp">

                <TextView
                    android:id="@+id/tv_message_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:lineSpacingExtra="4dp"
                    android:text="用户消息内容" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 底部时间区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end|center_vertical"
            android:layout_marginTop="8dp">

            <TextView
                android:id="@+id/tv_message_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorTextSecondary"
                android:textSize="12sp"
                android:layout_marginEnd="8dp"
                android:text="12:34" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
