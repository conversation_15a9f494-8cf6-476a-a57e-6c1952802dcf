<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp"
    android:paddingVertical="8dp"
    android:clickable="true"
    android:focusable="true"
    android:background="?attr/selectableItemBackground">

    <!-- 会话标题 -->
    <TextView
        android:id="@+id/tv_conversation_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="会话标题"
        android:textSize="15sp"
        android:textColor="@color/text_dark"
        android:maxLines="2"
        android:ellipsize="end"
        android:lineSpacingExtra="2dp" />

</LinearLayout>
