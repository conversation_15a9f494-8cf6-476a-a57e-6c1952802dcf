<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp"
    android:clickable="true"
    android:focusable="true"
    android:background="?attr/selectableItemBackground">

    <!-- 分组图标 -->
    <ImageView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/ic_folder"
        android:tint="@color/ios_text_secondary"
        android:layout_marginEnd="12dp" />

    <!-- 分组名称 -->
    <TextView
        android:id="@+id/tv_group_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="分组名称"
        android:textSize="15sp"
        android:textColor="@color/text_dark"
        android:maxLines="1"
        android:ellipsize="end" />

    <!-- 会话数量徽章 -->
    <TextView
        android:id="@+id/tv_conversation_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0"
        android:textSize="12sp"
        android:textColor="@color/ios_text_secondary"
        android:background="@drawable/ios_badge_background"
        android:paddingHorizontal="6dp"
        android:paddingVertical="2dp"
        android:minWidth="20dp"
        android:gravity="center" />

</LinearLayout>
