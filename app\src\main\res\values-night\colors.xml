<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 深色模式背景色 -->
    <color name="ios_background">#1C1C1E</color>
    <color name="ios_background_secondary">#2C2C2E</color>
    <color name="ios_background_tertiary">#3A3A3C</color>
    <color name="ios_card_background">#1C1C1E</color>
    <color name="ios_background_unified">#1C1C1E</color>

    <!-- 深色模式文字颜色 -->
    <color name="ios_text_primary">#FFFFFF</color>
    <color name="ios_text_secondary">#AEAEB2</color>
    <color name="ios_text_tertiary">#8E8E93</color>

    <!-- 深色模式分割线和边框 -->
    <color name="ios_separator">#38383A</color>
    <color name="ios_border">#48484A</color>

    <!-- 深色模式应用主题色映射 -->
    <color name="chat_background">#1C1C1E</color>
    <color name="ai_message_bg">#2C2C2E</color>
    <color name="text_gray">#AEAEB2</color>
    <color name="text_dark">#FFFFFF</color>
    <color name="border_gray">#48484A</color>
    <color name="source_pill_bg">#3A3A3C</color>

    <!-- 深色模式设置项背景 -->
    <color name="setting_item_bg">#2C2C2E</color>
    <color name="document_item_bg">#2C2C2E</color>
    <color name="upload_area_bg">#3A3A3C</color>
    <color name="upload_border">#48484A</color>

    <!-- 深色模式灰色系 -->
    <color name="gray_light">#3A3A3C</color>
    <color name="gray_border">#48484A</color>
    <color name="gray_pressed">#48484A</color>
    <color name="gray_background">#1C1C1E</color>
    <color name="gray_text">#AEAEB2</color>
    <color name="gray_text_light">#8E8E93</color>

    <!-- 深色模式灰色主题色 -->
    <color name="theme_gray">#AEAEB2</color>
    <color name="theme_gray_dark">#8E8E93</color>
    <color name="theme_gray_light">#3A3A3C</color>
</resources>
