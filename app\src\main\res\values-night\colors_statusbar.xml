<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 深色模式下的状态栏颜色定义 -->
    <color name="status_bar_light_blue">#0D47A1</color>
    <color name="status_bar_light_green">#1B5E20</color>
    <color name="status_bar_light_red">#B71C1C</color>
    <color name="status_bar_light_purple">#4A148C</color>
    <color name="status_bar_light_orange">#E65100</color>
    <color name="status_bar_light_pink">#880E4F</color>
    <color name="status_bar_light_gray">#212121</color>

    <!-- 深色模式状态栏颜色保持不变 -->
    <color name="status_bar_dark_blue">#1565C0</color>
    <color name="status_bar_dark_green">#2E7D32</color>
    <color name="status_bar_dark_red">#C62828</color>
    <color name="status_bar_dark_purple">#7B1FA2</color>
    <color name="status_bar_dark_orange">#EF6C00</color>
    <color name="status_bar_dark_pink">#C2185B</color>
    <color name="status_bar_dark_gray">#424242</color>

    <!-- 深色模式下的状态栏文字颜色 -->
    <color name="status_bar_text_light">#E6E1E5</color>  <!-- 浅色文字，用于深色背景 -->
    <color name="status_bar_text_dark">#1C1B1F</color>   <!-- 深色文字，用于浅色背景 -->
</resources>