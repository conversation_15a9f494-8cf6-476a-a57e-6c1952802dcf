<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 主题色属性定义 -->
    <attr name="colorThemePrimary" format="color" />
    <attr name="colorThemePrimaryDark" format="color" />
    <attr name="colorThemePrimaryLight" format="color" />
    
    <!-- 应用特定的主题色属性 -->
    <attr name="colorHeader" format="color" />
    <attr name="colorHeaderDark" format="color" />
    <attr name="colorAccent" format="color" />
    <attr name="colorTabSelected" format="color" />
    <attr name="colorUserMessageBg" format="color" />
    <attr name="colorProfileHeader" format="color" />
    <attr name="colorFileIconDoc" format="color" />
    <attr name="colorSendButton" format="color" />
    <attr name="colorSendButtonPressed" format="color" />
    <attr name="colorBadgeBackground" format="color" />

    <!-- 背景和文字颜色属性 -->
    <attr name="colorAppBackground" format="color" />
    <attr name="colorCardBackground" format="color" />
    <attr name="colorTextPrimary" format="color" />
    <attr name="colorTextSecondary" format="color" />
    <attr name="colorBorder" format="color" />
</resources>
