<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 状态栏颜色定义 - 浅色主题 -->
    <color name="status_bar_light_blue">#E3F2FD</color>
    <color name="status_bar_light_green">#E8F5E8</color>
    <color name="status_bar_light_red">#FFEBEE</color>
    <color name="status_bar_light_purple">#F3E5F5</color>
    <color name="status_bar_light_orange">#FFF3E0</color>
    <color name="status_bar_light_pink">#FCE4EC</color>
    <color name="status_bar_light_gray">#F5F5F5</color>

    <!-- 状态栏颜色定义 - 深色主题 -->
    <color name="status_bar_dark_blue">#1565C0</color>
    <color name="status_bar_dark_green">#2E7D32</color>
    <color name="status_bar_dark_red">#C62828</color>
    <color name="status_bar_dark_purple">#7B1FA2</color>
    <color name="status_bar_dark_orange">#EF6C00</color>
    <color name="status_bar_dark_pink">#C2185B</color>
    <color name="status_bar_dark_gray">#424242</color>

    <!-- 状态栏文字颜色 -->
    <color name="status_bar_text_light">#1C1B1F</color>  <!-- 深色文字，用于浅色背景 -->
    <color name="status_bar_text_dark">#E6E1E5</color>   <!-- 浅色文字，用于深色背景 -->

    <!-- 状态栏半透明遮罩 -->
    <color name="status_bar_overlay_light">#1A000000</color>  <!-- 10% 黑色遮罩 -->
    <color name="status_bar_overlay_dark">#1AFFFFFF</color>   <!-- 10% 白色遮罩 -->
</resources>