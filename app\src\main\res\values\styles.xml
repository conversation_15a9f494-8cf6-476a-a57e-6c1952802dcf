<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- 统一的页面根布局样式 - 自动处理状态栏 -->
    <style name="AppPageRootLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:background">?attr/colorAppBackground</item>
    </style>
    
    <!-- 统一的标题栏样式 - 自动处理状态栏 -->
    <style name="AppHeaderLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">?attr/colorHeader</item>
        <item name="android:elevation">4dp</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:paddingTop">@dimen/status_bar_height</item>
    </style>
    
    <!-- 统一的标题栏内容样式 -->
    <style name="AppHeaderContent">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingHorizontal">20dp</item>
    </style>
    
    <!-- 统一的返回按钮样式 -->
    <style name="AppBackButton">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:src">@drawable/ic_arrow_back</item>
        <item name="android:tint">@color/white</item>
        <item name="android:contentDescription">返回</item>
        <item name="android:padding">12dp</item>
    </style>
    
    <!-- 统一的标题文字样式 -->
    <style name="AppHeaderTitle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style>
    
    <!-- 统一的占位空间样式 -->
    <style name="AppHeaderSpacer">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
    </style>
    
    <!-- 统一的内容区域样式 -->
    <style name="AppContentLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
    </style>
    
    <!-- 统一的滚动视图样式 -->
    <style name="AppScrollView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:fillViewport">true</item>
        <item name="android:overScrollMode">ifContentScrolls</item>
    </style>
    
</resources>