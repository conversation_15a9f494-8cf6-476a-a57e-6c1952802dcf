<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Ragandroid" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <!-- 状态栏配置 - 浅色主题优化 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowTranslucentStatus" tools:targetApi="kitkat">false</item>
        
        <!-- 确保软键盘模式不被覆盖 -->
        <item name="android:windowSoftInputMode">adjustResize</item>

        <!-- 默认主题色 - 蓝色 -->
        <item name="colorThemePrimary">@color/ios_blue</item>
        <item name="colorThemePrimaryDark">@color/ios_blue_dark</item>
        <item name="colorThemePrimaryLight">@color/ios_blue_light</item>
        <item name="colorHeader">@color/ios_blue</item>
        <item name="colorHeaderDark">@color/ios_blue_dark</item>
        <item name="colorAccent">@color/ios_blue</item>
        <item name="colorTabSelected">@color/ios_blue</item>
        <item name="colorUserMessageBg">@color/ios_blue</item>
        <item name="colorProfileHeader">@color/ios_blue</item>
        <item name="colorFileIconDoc">@color/ios_blue</item>
        <item name="colorSendButton">@color/ios_blue</item>
        <item name="colorSendButtonPressed">@color/ios_blue_dark</item>
        <item name="colorBadgeBackground">@color/ios_blue</item>

        <!-- 背景和文字颜色 -->
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>

    <!-- 蓝色主题 -->
    <style name="Theme.Ragandroid.Blue" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/ios_blue</item>
        <item name="colorThemePrimaryDark">@color/ios_blue_dark</item>
        <item name="colorThemePrimaryLight">@color/ios_blue_light</item>
        <item name="colorHeader">@color/ios_blue</item>
        <item name="colorHeaderDark">@color/ios_blue_dark</item>
        <item name="colorAccent">@color/ios_blue</item>
        <item name="colorTabSelected">@color/ios_blue</item>
        <item name="colorUserMessageBg">@color/ios_blue</item>
        <item name="colorProfileHeader">@color/ios_blue</item>
        <item name="colorFileIconDoc">@color/ios_blue</item>
        <item name="colorSendButton">@color/ios_blue</item>
        <item name="colorSendButtonPressed">@color/ios_blue_dark</item>
        <item name="colorBadgeBackground">@color/ios_blue</item>

        <!-- 背景和文字颜色 -->
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>

    <!-- 绿色主题 -->
    <style name="Theme.Ragandroid.Green" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_green</item>
        <item name="colorThemePrimaryDark">@color/theme_green_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_green_light</item>
        <item name="colorHeader">@color/theme_green</item>
        <item name="colorHeaderDark">@color/theme_green_dark</item>
        <item name="colorAccent">@color/theme_green</item>
        <item name="colorTabSelected">@color/theme_green</item>
        <item name="colorUserMessageBg">@color/theme_green</item>
        <item name="colorProfileHeader">@color/theme_green</item>
        <item name="colorFileIconDoc">@color/theme_green</item>
        <item name="colorSendButton">@color/theme_green</item>
        <item name="colorSendButtonPressed">@color/theme_green_dark</item>
        <item name="colorBadgeBackground">@color/theme_green</item>

        <!-- 背景和文字颜色 -->
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>

    <!-- 红色主题 -->
    <style name="Theme.Ragandroid.Red" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_red</item>
        <item name="colorThemePrimaryDark">@color/theme_red_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_red_light</item>
        <item name="colorHeader">@color/theme_red</item>
        <item name="colorHeaderDark">@color/theme_red_dark</item>
        <item name="colorAccent">@color/theme_red</item>
        <item name="colorTabSelected">@color/theme_red</item>
        <item name="colorUserMessageBg">@color/theme_red</item>
        <item name="colorProfileHeader">@color/theme_red</item>
        <item name="colorFileIconDoc">@color/theme_red</item>
        <item name="colorSendButton">@color/theme_red</item>
        <item name="colorSendButtonPressed">@color/theme_red_dark</item>
        <item name="colorBadgeBackground">@color/theme_red</item>
    </style>

    <!-- 紫色主题 -->
    <style name="Theme.Ragandroid.Purple" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_purple</item>
        <item name="colorThemePrimaryDark">@color/theme_purple_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_purple_light</item>
        <item name="colorHeader">@color/theme_purple</item>
        <item name="colorHeaderDark">@color/theme_purple_dark</item>
        <item name="colorAccent">@color/theme_purple</item>
        <item name="colorTabSelected">@color/theme_purple</item>
        <item name="colorUserMessageBg">@color/theme_purple</item>
        <item name="colorProfileHeader">@color/theme_purple</item>
        <item name="colorFileIconDoc">@color/theme_purple</item>
        <item name="colorSendButton">@color/theme_purple</item>
        <item name="colorSendButtonPressed">@color/theme_purple_dark</item>
        <item name="colorBadgeBackground">@color/theme_purple</item>
    </style>

    <!-- 橙色主题 -->
    <style name="Theme.Ragandroid.Orange" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_orange</item>
        <item name="colorThemePrimaryDark">@color/theme_orange_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_orange_light</item>
        <item name="colorHeader">@color/theme_orange</item>
        <item name="colorHeaderDark">@color/theme_orange_dark</item>
        <item name="colorAccent">@color/theme_orange</item>
        <item name="colorTabSelected">@color/theme_orange</item>
        <item name="colorUserMessageBg">@color/theme_orange</item>
        <item name="colorProfileHeader">@color/theme_orange</item>
        <item name="colorFileIconDoc">@color/theme_orange</item>
        <item name="colorSendButton">@color/theme_orange</item>
        <item name="colorSendButtonPressed">@color/theme_orange_dark</item>
        <item name="colorBadgeBackground">@color/theme_orange</item>
    </style>

    <!-- 粉色主题 -->
    <style name="Theme.Ragandroid.Pink" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_pink</item>
        <item name="colorThemePrimaryDark">@color/theme_pink_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_pink_light</item>
        <item name="colorHeader">@color/theme_pink</item>
        <item name="colorHeaderDark">@color/theme_pink_dark</item>
        <item name="colorAccent">@color/theme_pink</item>
        <item name="colorTabSelected">@color/theme_pink</item>
        <item name="colorUserMessageBg">@color/theme_pink</item>
        <item name="colorProfileHeader">@color/theme_pink</item>
        <item name="colorFileIconDoc">@color/theme_pink</item>
        <item name="colorSendButton">@color/theme_pink</item>
        <item name="colorSendButtonPressed">@color/theme_pink_dark</item>
        <item name="colorBadgeBackground">@color/theme_pink</item>
    </style>

    <!-- 灰色主题 -->
    <style name="Theme.Ragandroid.Gray" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_gray</item>
        <item name="colorThemePrimaryDark">@color/theme_gray_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_gray_light</item>
        <item name="colorHeader">@color/theme_gray</item>
        <item name="colorHeaderDark">@color/theme_gray_dark</item>
        <item name="colorAccent">@color/theme_gray</item>
        <item name="colorTabSelected">@color/theme_gray</item>
        <item name="colorUserMessageBg">@color/theme_gray</item>
        <item name="colorProfileHeader">@color/theme_gray</item>
        <item name="colorFileIconDoc">@color/theme_gray</item>
        <item name="colorSendButton">@color/theme_gray</item>
        <item name="colorSendButtonPressed">@color/theme_gray_dark</item>
        <item name="colorBadgeBackground">@color/theme_gray</item>

        <!-- 背景和文字颜色 -->
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>
</resources>